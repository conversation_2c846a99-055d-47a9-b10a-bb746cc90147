<?php

namespace App\Http\Controllers;

use App\Models\SystemSetting;
use App\Models\Ticker;
use Illuminate\Http\Request;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Action\QueueSummaryAction;
use PAMI\Message\Event\QueueMemberEvent;
use PAMI\Message\Event\QueueParamsEvent;
use PAMI\Message\Event\QueueSummaryEvent;

class DashboardController extends Controller
{
    protected function getOptions()
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connect_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    /**
     * Get Data for Dashboard / Wallboard
     */

    public function get_data(Request $request)
    {
        $queue = $request->queue;
        $options = $this->getOptions();
        $client = new ClientImpl($options);
        $action = new QueueStatusAction($queue);
        $action2 = new QueueSummaryAction($queue);
        try {
            $client->open();
            $response = $client->send($action);
            $response2 = $client->send($action2);
            $client->close();
            $data = [];
            $data2 = [];
            foreach ($response->getEvents() as $event) {
                if($event instanceof QueueParamsEvent || $event instanceof QueueMemberEvent) {
                    $data[] = $event->getKeys();
                }
            }
            foreach ($response2->getEvents() as $event) {
                if ($event instanceof QueueSummaryEvent)
                    $data2[] = $event->getKeys();
            }
            return response()->json([$data, $data2]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    public function ticker(){

        try{
            $data = Ticker::first();
            return response()->json($data);
           // dd($data);
        }catch(\Exception $e){
            return response()->json($e->getMessage());

        }
        
    }

    public function update(Ticker $ticker, Request $request){

        try{
            
            $ticker->message = $request->message;
            $ticker->save();

            return response()->json('Ticket has been  updated');

        }catch(\Exception $e){
            return response()->json($e->getMessage());

        }
    }
}
