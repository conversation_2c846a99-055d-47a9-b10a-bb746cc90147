<?php

namespace App\Http\Controllers;

use App\Models\Campaign;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CampaignUserController extends Controller
{
    /**
     * Get campaign users
     * @param Campaign $campaign
     * @return JsonResponse
     */
    public function index(Campaign $campaign): JsonResponse
    {
        return response()->json($campaign->users()->where('type', '!=', 'Normal')->get());
    }

    /**
     * @param Campaign $campaign
     * @param User $user
     * @param Request $request
     * @return JsonResponse
     */
    public function add_user(Campaign $campaign, User $user, Request $request): JsonResponse
    {
        try {
            $campaign->users()->sync([$user->id]);
            return response()->json("User has been added in the campaign.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * @param Campaign $campaign
     * @param Request $request
     * @return JsonResponse
     */
    public function add_users(Campaign $campaign, Request $request): JsonResponse
    {
        try {
            $request->validate([
                'users' => ['required', 'array'],
                'users.*' => ['exists:users,id']
            ]);
            $campaign->users()->sync($request->users);
            return response()->json("User(s) has been added in the campaign.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * @param User $user
     * @param Request $request
     * @return JsonResponse
     */
    public function get_campaign(User $user): JsonResponse
    {
        try {
            return response()->json($user->campaigns()->withCount(['custom_numbers as count' => function($query) {
                $query->where('status', false)->where('attempts', '>', 0);
            }])->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Delete user from campaign
     * @param Campaign $campaign
     * @param User $user
     * @return JsonResponse
     */
    public function destroy(Campaign $campaign, User $user): JsonResponse
    {
        try {
            $campaign->users()->detach([$user->id]);
            return response()->json("User has been removed from campaign.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
