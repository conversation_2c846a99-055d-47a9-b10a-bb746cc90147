<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Carbon\Traits\Creator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

//test

class AgentCallReportController extends Controller
{

    public function agentCallSummary(): \Illuminate\Http\JsonResponse
    {
        $sql = "select * from ( select substr(a.channel,7,4) ext1,b.name, count(*) Total_Call_Attempts, sum(if(disposition ='ANSWERED',1,0)) Total_Answered_Calls, sum(if(disposition = 'ANSWERED',duration,0)) Total_Talk_Time, sum(if(disposition = 'ANSWERED',TIMESTAMPDIFF(SECOND,start,end),0)) Total from cdr a, users b where  a.accountcode='Outbound' and disposition in ('ANSWERED','NO ANSWER') and substr(a.channel,7,4) = b.auth_username and start>= CURDATE() AND end<= NOW()  group by substr(a.channel,7,4) ) e left join( select ext,count(*) Total_Unique_Customers, sum(TUAC) Total_Unique_Answered_Calls from ( SELECT *,substr(channel,7,4) ext,sum(if(disposition ='ANSWERED',1,0)) TUAC FROM cdr where accountcode='Outbound' and disposition in ('ANSWERED','NO ANSWER') and start>= CURDATE() AND end<= NOW()  GROUP BY dst,substr(channel,7,4) HAVING COUNT(*) > 1 ) c group by ext ) f on e.ext1=f.ext";
        $data = DB::select($sql);
        return response()->json($data);
    }


    public function agentCallSummaryInboundOLd(): \Illuminate\Http\JsonResponse
    {
        $sql = "SELECT
  *
FROM
  (SELECT
    SUBSTR(a.dstchannel, 7, 4) ext1,
    b.name,
    COUNT(*) Total_Call_Attempts,
    SUM(IF(disposition = 'ANSWERED', 1, 0)) Total_Answered_Calls,
    SUM(
      IF(
        disposition = 'ANSWERED',
        duration,
        0
      )
    ) Total_Talk_Time,
    SUM(
      IF(
        disposition = 'ANSWERED',
        TIMESTAMPDIFF(SECOND, START,
        END),
      0
    )
  ) Total
  FROM
    cdr a,
    users b
  WHERE a.accountcode = 'Queue'
    AND disposition IN ('ANSWERED', 'NO ANSWER')
    AND SUBSTR(a.dstchannel, 7, 4) = b.auth_username
    AND START >= CURDATE()
    AND
  END <= NOW()
  GROUP BY SUBSTR(a.dstchannel, 7, 4)) e
  LEFT JOIN
    (SELECT
      ext,
      COUNT(*) Total_Unique_Customers,
      COUNT(TUAC) Total_Unique_Answered_Calls
    FROM
      (SELECT
        *,
        SUBSTR(dstchannel, 7, 4) ext,
        SUM(IF(disposition = 'ANSWERED', 1, 0)) TUAC
      FROM
        cdr
      WHERE accountcode = 'Queue'
        AND disposition IN ('ANSWERED', 'NO ANSWER')
        AND START >= CURDATE()
        AND
      END <= NOW()
      GROUP BY dst,
        SUBSTR(dstchannel, 7, 4)
      HAVING COUNT(*) > 1) c
    GROUP BY ext) f
    ON e.ext1 = f.ext ;";
        $data = DB::select($sql);
        return response()->json($data);
    }
    public function agentCallSummaryInbound(): \Illuminate\Http\JsonResponse
    {
    $sql = "SELECT * FROM ( 
                SELECT substr(a.dstchannel, 7, 4) ext1, b.name, count(*) Total_Call_Attempts,
                       sum(IF(disposition = 'ANSWERED', 1, 0)) Total_Answered_Calls,
                       sum(IF(disposition = 'ANSWERED', duration, 0)) Total_Talk_Time,
                       sum(IF(disposition = 'ANSWERED', TIMESTAMPDIFF(SECOND, start, end), 0)) Total 
                FROM cdr a, users b 
                WHERE a.accountcode = 'Queue' 
                  AND disposition IN ('ANSWERED', 'NO ANSWER') 
                  AND substr(a.dstchannel, 7, 4) = b.auth_username 
                  AND start >= CURDATE() 
                  AND end <= NOW() 
                GROUP BY substr(a.dstchannel, 7, 4)
            ) e 
            LEFT JOIN (
                SELECT ext, count(*) Total_Unique_Customers, count(TUAC) Total_Unique_Answered_Calls 
                FROM (
                    SELECT *, substr(dstchannel, 7, 4) ext, sum(IF(disposition = 'ANSWERED', 1, 0)) TUAC 
                    FROM cdr 
                    WHERE accountcode = 'Queue' 
                      AND disposition IN ('ANSWERED', 'NO ANSWER') 
                      AND start >= CURDATE() 
                      AND end <= NOW() 
                    GROUP BY dst, substr(dstchannel, 7, 4) 
                    HAVING count(*) > 1 
                ) c 
                GROUP BY ext
            ) f ON e.ext1 = f.ext";
    $data = DB::select($sql);
    return response()->json($data);
    }   

    
    public function agentCallSummaryFilteredInbound(Request $request): \Illuminate\Http\JsonResponse
    {
        $date1 = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
        $date2 = Carbon::parse($request->range[1])->timezone('Asia/Karachi');

        $sql = "select * from ( select substr(a.dstchannel,7,4) ext1,b.name, count(*) Total_Call_Attempts, sum(if(disposition ='ANSWERED',1,0)) Total_Answered_Calls, sum(if(disposition = 'ANSWERED',duration,0)) Total_Talk_Time, sum(if(disposition = 'ANSWERED',TIMESTAMPDIFF(SECOND,start,end),0)) Total from cdr a, users b where  a.accountcode='Queue' and disposition in ('ANSWERED','NO ANSWER') and substr(a.dstchannel,7,4) = b.auth_username and START >= '" . $date1 . "' AND END<= '" . $date2 . "'  group by substr(a.dstchannel,7,4) ) e left join( select ext,count(*) Total_Unique_Customers, count(TUAC) Total_Unique_Answered_Calls from ( SELECT *,substr(dstchannel,7,4) ext,sum(if(disposition ='ANSWERED',1,0)) TUAC FROM cdr where accountcode='Queue' and disposition in ('ANSWERED','NO ANSWER') and START >= '" . $date1 . "' AND END<= '" . $date2 . "' GROUP BY dst,substr(dstchannel,7,4) HAVING COUNT(*) > 1 ) c group by ext ) f on e.ext1=f.ext";
        $data = DB::select($sql);
        return response()->json($data);
    }

    public function agentCallSummaryFiltered(Request $request): \Illuminate\Http\JsonResponse
    {
        $sql = "select * from ( select substr(a.channel,7,4) ext1,b.name, count(*) Total_Call_Attempts, sum(if(disposition ='ANSWERED',1,0)) Total_Answered_Calls, sum(if(disposition = 'ANSWERED',duration,0)) Total_Talk_Time, sum(if(disposition = 'ANSWERED',TIMESTAMPDIFF(SECOND,start,end),0)) Total from cdr a, users b where  a.accountcode='Outbound' and disposition in ('ANSWERED','NO ANSWER') and substr(a.channel,7,4) = b.auth_username and START >= '" . Carbon::parse($request->range[0])->timezone("Asia/Karachi") . "' AND END<= '" . Carbon::parse($request->range[1])->timezone("Asia/Karachi") . "'  group by substr(a.channel,7,4) ) e left join( select ext,count(*) Total_Unique_Customers, sum(TUAC) Total_Unique_Answered_Calls from ( SELECT *,substr(channel,7,4) ext,sum(if(disposition ='ANSWERED',1,0)) TUAC FROM cdr where accountcode='Outbound' and disposition in ('ANSWERED','NO ANSWER') and START >= '" . new Carbon($request->range[0]) . "' AND END<= '" . new Carbon($request->range[1]) . "' GROUP BY dst,substr(channel,7,4) HAVING COUNT(*) > 1 ) c group by ext ) f on e.ext1=f.ext";
        $data = DB::select($sql);
        return response()->json($data);
    }

    public function index(Request $request)
    {
        $outboundStringParam = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
        try {
            $cdr = Cdr::query();
            $cdr->select(['uniqueid', 'start', 'dst', 'channel', 'disposition', 'duration']);
            // Display only current months records in default query
            $date = Carbon::now();
            $cdr->whereDate('start', '>=', $date->startOfDay())->whereDate('end', '<=', $date->endOfDay());
            $cdr->where('dstchannel', 'like', "%{$outboundStringParam}%");
            $cdr->orderBy('start', 'desc');
            return response()->json($cdr->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getFilteredReport(Request $request)
    {
        $outboundStringParam = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
        try {
            $cdr = Cdr::query();
            $cdr->select(['uniqueid', 'start', 'dst', 'channel', 'disposition', 'duration']);
            $cdr->where('dstchannel', 'like', "%{$outboundStringParam}%");
            if ($request->has('range') && is_array($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTime(), $end->toDateTime()]);
            }
            if ($request->has('agent')) {
                $agents = $request->agent;
                if (is_array($agents)) {
                    $cdr->where(function ($query) use ($agents) {
                        foreach ($agents as $agent) {
                            $query->orWhere('channel', 'like', "%PJSIP/{$agent}%");
                        }
                    });
                } else {
                    $cdr->where('channel', 'like', "%PJSIP/{$agents}%");
                }
            }
            if ($request->has('destination'))
                $cdr->where('dst', $request->destination);
            if ($request->has('call_status'))
                $cdr->where('disposition', $request->call_status);
            $cdr->orderBy('start', 'desc'); // Return latest records first
            return response()->json($cdr->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
