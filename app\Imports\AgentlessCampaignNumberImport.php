<?php

namespace App\Imports;

use App\Models\AgentlessCampaignNumber;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class AgentlessCampaignNumberImport implements ToModel, WithChunkReading, ShouldQueue
{
    private int $campaignId;

    public function __construct(int $campaignId)
    {
        $this->campaignId = $campaignId;
    }

    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        if(empty($row[0]) || trim($row[0]) === ''){
            return null;
        }
        
        return new AgentlessCampaignNumber([
            'number' => $row[0],
            'campaign_id' => $this->campaignId
        ]);
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
