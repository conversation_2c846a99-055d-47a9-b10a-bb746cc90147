<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ServiceRatingSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('service_rating_settings')->upsert([
            'id' => 1,
            'status' => false,
            'service_rating_file_id' => null,
            'best' => 1,
            'worst' => 0,
            'created_at' => Carbon::now(),
        ], 'id', ['best', 'worst', 'created_at']);
    }
}
