<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\{Navbar,Role,Permission};
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class NavbarController extends Controller
{
    public function index(Request $request)
    {

        $navbar = Navbar::get()->toArray();

        $menuTree = $this->buildMenuTree($navbar);

        return response()->json($menuTree);
    }

    public function getAllowedNavbarModules()
    {

        $user = auth()->user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        
        $allowedPermissions = $user->roles()
        ->with('permissions')
        ->get()
        ->flatMap(function ($role) {
            return $role->permissions->pluck('name');
        })
        ->toArray();

        $allowedModules = Navbar::whereIn('permission', $allowedPermissions)
        ->where('module_accessibility', true)
        ->get()
        ->map(function ($module) {
            return collect($module)->except(['created_at', 'updated_at'])->toArray();
        })
        ->toArray();

        $menuTree = $this->buildMenuTree($allowedModules);

        return response()->json($menuTree);
    }

    private function buildMenuTree($menuItems, $parentId = null)
    {
        $tree = [];
        foreach ($menuItems as $item) {
            if ($item['parent_id'] == $parentId) {
                $item['children'] = $this->buildMenuTree($menuItems, $item['navbar_id']);
                $tree[] = $item;
            }
        }
        return $tree;
    }

}
