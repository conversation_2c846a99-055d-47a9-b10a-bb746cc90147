<?php

namespace App\Http\Controllers;

use App\Models\ScheduleCalls;
use App\Models\Notification;
use Illuminate\Http\Request;
use Auth;
use Carbon\Carbon;

class ScheduleCallsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        $data = ScheduleCalls::where('user_id', Auth::user()->id)->whereDate('datetime', '<=' ,Carbon::now())->where('status', 0)->orderBy('datetime', 'asc')->paginate(5); 
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try{
            $data = new ScheduleCalls;
            $data->number = $request->number;
            $data->datetime = $request->datetime;
            $data->status = 0;
            $data->user_id = Auth::user()->id;
            $data->save();
            // $notification = new Notification;
            // $notification->type = 'Call Scheduler';
            // $notification->status = 0;
            // $notification->message ='Test Message from call scheduler';
            // $notification->schedule_call_id = $data->id;
            // $notification->user_id = Auth::user()->id;
            // $notification->save();
            return response()->json($data);
        }
        catch(\Exception $e) {
          return response()->json($e->getMessage(), 500);  
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ScheduleCalls  $scheduleCalls
     * @return \Illuminate\Http\Response
     */
    public function show(ScheduleCalls $scheduleCalls)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ScheduleCalls  $scheduleCalls
     * @return \Illuminate\Http\Response
     */
    public function edit(ScheduleCalls $scheduleCalls)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ScheduleCalls  $scheduleCalls
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ScheduleCalls $scheduleCalls)
    {   
        $scheduleCalls->status = 1;
        $scheduleCalls->save();
        return response()->json('Status has been updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ScheduleCalls  $scheduleCalls
     * @return \Illuminate\Http\Response
     */
    public function destroy(ScheduleCalls $scheduleCalls)
    {
       
    }
}
