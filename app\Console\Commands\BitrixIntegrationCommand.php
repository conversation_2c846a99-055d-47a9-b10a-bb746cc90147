<?php

namespace App\Console\Commands;

use App\Models\QueueLog;
use App\Models\SystemSetting;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueueLogAction;
use PAMI\Message\Event\AgentCompleteEvent;
use PAMI\Message\Event\AgentConnectEvent;
use PAMI\Message\Event\DialBeginEvent;
use PAMI\Message\Event\DialEndEvent;
use PAMI\Message\Event\DialEvent;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Event\HangupEvent;
use Illuminate\Support\Facades\Http;
use App\Models\BitrixIntegration;
use App\Models\BitrixCallLog;
use Carbon\Carbon;
use App\Models\Cdr;
use Illuminate\Support\Facades\Storage;

class BitrixIntegrationCommand extends Command
{
     /**
     * Get manager options
     * @return array
     */
    public function getManagerOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }



    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'start:bitrix-integration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'The command is used to start the integration of bitrix lead on our inbound/outbound calls';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $manager = new ClientImpl($this->getManagerOptions());
        $manager->registerEventListener(function (EventMessage $eventMessage) {
            $this->handleEvent($eventMessage);
            //dump($eventMessage->getKeys());
        }, function (EventMessage $eventMessage) {
            return $this->allowedEvents($eventMessage);
        });
        $manager->open();
        while (true) {
           $manager->process();
        }
    }

    private function allowedEvents(EventMessage $eventMessage): bool
    {
        return (($eventMessage instanceof AgentConnectEvent) ||
            ($eventMessage instanceof AgentCompleteEvent) ||
            ($eventMessage instanceof HangupEvent && $eventMessage->getCause() === 16 && !Str::contains($eventMessage->getKey('channel'), $this->getTrunkValue())) ||
            ($eventMessage instanceof DialEndEvent && $eventMessage->getDialStatus() === 'ANSWER'));
    }

    private function getTrunkValue()
    {
        return SystemSetting::GetSetting('outbound_string');
    }

    private function getAuthUsernameFromInterface($interface)
    {
        return explode('/', $interface)[1];
    }

    private function getAuthUsernameFromChannel($channel)
    {
        return explode('/', explode('-', $channel)[0])[1];
    }

    private function handleEvent(EventMessage $eventMessage)
    {
        try {
            $event = $eventMessage->getKey('event');
            // Log only inbound hold events.
            if ($event === 'AgentConnect') {
                $this->leadOnIncomingCall($eventMessage->getKey('calleridnum'), $eventMessage->getKey('uniqueid'), $this->getAuthUsernameFromInterface($eventMessage->getKey('interface')));
            } elseif ($event === 'AgentComplete') {
                $this->callFinish($eventMessage->getKey('uniqueid'), $this->getAuthUsernameFromInterface($eventMessage->getKey('interface')));
            } elseif ($event === 'Hangup') {
                if($eventMessage->getKey('accountcode') === 'CustomerCare' || $eventMessage->getKey('accountcode') === 'Custom_Routing') {
                    $this->callFinish($eventMessage->getKey('linkedid'), $this->getAuthUsernameFromChannel($eventMessage->getKey('channel')));
                } else {
                    $this->callFinish($eventMessage->getKey('uniqueid'), $this->getAuthUsernameFromChannel($eventMessage->getKey('channel')));
                }
            } elseif ($event === 'DialEnd') {
                if($eventMessage->getKey('accountcode') === 'CustomerCare' || $eventMessage->getKey('accountcode') === 'Custom_Routing') {
                    $this->leadOnIncomingCall($eventMessage->getKey('calleridnum'), $eventMessage->getKey('uniqueid'), $this->getAuthUsernameFromChannel($eventMessage->getKey('destchannel')));
                } else {
                    $this->leadOnOutgoingCall($eventMessage->getKey('connectedlinenum'), $eventMessage->getKey('uniqueid'), $this->getAuthUsernameFromChannel($eventMessage->getKey('channel')));
                }
            }

        } catch(\Exception $e) {
            $date = Carbon::now()->toDateTimeString();
            //$this->error("{$e->getTraceAsString()}\n");
            $eventError = json_encode($eventMessage->getKeys());
            $this->error("{$date}, ERROR:, Message: {$e->getMessage()}, Line#: {$e->getLine()}, File Name: {$e->getFile()}, Event: $eventError");
        }

    }
    private function getBitrixIdByAuthUsername($authUsername): int
    {
        return User::query()->where('auth_username', $authUsername)->first()->bitrix_id;
    }

    public function leadOnOutgoingCall($phone_number, $unique_id, $authUsername )
    {
            $number = (string)$phone_number;
            if($number[0] === '0'){
                $array = explode('0', $number, 2);
                $phoneNumber = '+92'.$array[1];
            }else{
                $phoneNumber = '+92'.$number;
            }
            $api = BitrixIntegration::first();
            $api_key = $api->api_key;
            $api_enable = $api->is_enable;
            $bitrix_id = $this->getBitrixIdByAuthUsername($authUsername);
            if($api_enable === 1 && $bitrix_id !== NULL){
                //$internal_number = Auth::user()->auth_username;
                $response = Http::post("$api_key/telephony.externalcall.register",[
                    'USER_ID' => $bitrix_id,
                    'USER_PHONE_INNER' => $authUsername,
                    'PHONE_NUMBER' => $phoneNumber,
                    'TYPE' => 1,
                    'CALL_START_DATE' => Carbon::now()->toISOString(),
                    'CRM_CREATE' => 1,
                    'CRM_SOURCE' => 'Call',
                    'CRM_ENTITY_TYPE' => 'Lead',
                ]);
                $response = $response->object();
                $call_id = $response->result->CALL_ID;
                $call_log = new BitrixCallLog;
                $call_log->call_id = $call_id;
                $call_log->call_unique_id = $unique_id;
                $call_log->save();
                Cache::put($bitrix_id.'_call_id', $call_id);
               // return response()->json($response);
               $date = Carbon::now()->toDateTimeString();
               $this->info("{$date}, INFO:, Call ID: {$response->result->CALL_ID}, CRM ID: {$response->result->CRM_ENTITY_ID}, RESPONSE DATE: {$response->time->date_start}");
            }
    }

    public function leadOnIncomingCall($phone_number, $unique_id, $authUsername )
    {
        //condition for storing number starting with 92
            $number = (string)$phone_number;
            if($number[0] === '0'){
                $array = explode('0', $number, 2);
                $phoneNumber = '+92'.$array[1];
            }else{
                $phoneNumber = '+92'.$number;
            }
            $api = BitrixIntegration::first();
            $api_key = $api->api_key;
            $api_enable = $api->is_enable;
            //$bitrix_id = Auth::user()->bitrix_id;
            $bitrix_id = $this->getBitrixIdByAuthUsername($authUsername);
            if($api_enable === 1 && $bitrix_id !== NULL){
                //$internal_number = Auth::user()->auth_username;
                $response = Http::post("$api_key/telephony.externalcall.register",[
                    'USER_ID' => $bitrix_id,
                    'USER_PHONE_INNER' => $authUsername,
                    'PHONE_NUMBER' => $phoneNumber,
                    'TYPE' => 2,
                    'CALL_START_DATE' => Carbon::now()->toISOString(),
                    'CRM_CREATE' => 1,
                    'CRM_SOURCE' => 'Call',
                    'CRM_ENTITY_TYPE' => 'Lead',
                ]);
                $response = $response->object();
                $call_id = $response->result->CALL_ID;
                $call_log = new BitrixCallLog;
                $call_log->call_id = $call_id;
                $call_log->call_unique_id = $unique_id;
                $call_log->save();
                Cache::put($bitrix_id.'_call_id', $call_id);
                $date = Carbon::now()->toDateTimeString();
                $this->info("{$date}, INFO:, Call ID: {$response->result->CALL_ID}, CRM ID: {$response->result->CRM_ENTITY_ID},  RESPONSE DATE: {$response->time->date_start}");
               // return response()->json($response);
            }
    }

    public function callFinish($unique_id, $authUsername){

            usleep(5000000);
            $api = BitrixIntegration::first();
            $api_key = $api->api_key;
            $api_enable = $api->is_enable;
            //$bitrix_id = Auth::user()->bitrix_id;
            $bitrix_id = $this->getBitrixIdByAuthUsername($authUsername);
            $bitrix_call_id = Cache::pull($bitrix_id.'_call_id');
            if($api_enable === 1 && $bitrix_id !== NULL && $bitrix_call_id ){
                //getting unique id from bitrix_call_logs tables because sometime we are getting incorrect unique id in some hangup events and due to some lead are facing no sync of recordings
                $uniqueid_for_recording = BitrixCallLog::where('call_id', $bitrix_call_id)->orderBy('created_at', 'desc')->pluck('call_unique_id')->first();
                $cdr = Cdr::where('uniqueid', $uniqueid_for_recording)->orderBy('end' , 'DESC')->first();
                $recordingFileName =  $cdr->recordingfile;
                $response = Http::post("$api_key/telephony.externalcall.finish",[
                    'CALL_ID' => $bitrix_call_id,
                    'USER_ID' => $bitrix_id,
                    'DURATION' => $cdr->billsec,
                ]);
                //attach record
                $date = Carbon::createFromFormat("Ymd", explode("-", $recordingFileName)[3]);
                $recordingFile = Storage::disk('recordings')->get("{$date->year}/{$date->format('m')}/{$date->format('d')}/$recordingFileName");
                $recordingFile = base64_encode($recordingFile);
                sleep(10);
                $response = Http::post("$api_key/telephony.externalCall.attachRecord",[

                    'CALL_ID' => $bitrix_call_id,
                    'FILENAME'=> $recordingFileName,
                    'FILE_CONTENT' => $recordingFile,
                ]);
                $response = $response->object();
                $date = Carbon::now()->toDateTimeString();
                $this->info("{$date}, INFO:, FILE ID: {$response->result->FILE_ID} FOR CALL ID: {$bitrix_call_id} RESPONSE DATE: {$response->time->date_start}");
            }
    }

}
