<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('callback_requests', function (Blueprint $table) {
            $table->id();
            $table->string('caller_id', 20);
            $table->string('queue');
            $table->string('agent')->nullable();
            $table->string('filePath')->nullable();
            $table->string('fileName')->nullable();
            $table->string('fileLoc')->nullable();
            $table->boolean('status')->default(false);
            $table->timestamp('answered_date')->nullable();
            $table->timestamp('request_time')->useCurrent();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('callback_requests');
    }
};
