<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OutboundAgentSummaryController extends Controller
{
    public function getOutboundAgentSummaryColumns(): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(Cdr::query()->select(['disposition'])->distinct()->get()->pluck('disposition'));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getFilteredOutboundAgentSummaryOld(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $agents = Cdr::query()->select(['channel'])->where('dstchannel', 'like', "%$outboundTrunkString%")->get()->unique('channel')->pluck('channel');
            $agents = $agents->map(function ($value) {
                return DB::raw("case when channel like 'PJSIP/$value-%' then '$value' END");
            });
            $call_statuses = Cdr::query()->select(['disposition'])->distinct()->get()->pluck('disposition');
            $call_statuses = $call_statuses->map(function ($value) {
                return DB::raw("count(case disposition when '$value' then 1 else null end) as '$value'");
            });
            $cdr = Cdr::query()->leftJoin("users", function($join) {
                $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
            })
            ->select(['users.username','channel', ...$call_statuses])
            ->where('lastapp', 'Dial')
            ->where('dstchannel', 'like', "%$outboundTrunkString%");

            if($request->has('time') && is_array($request->time)){
                $start = Carbon::parse($request->time[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->time[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start",[$start->toDateTime(), $end->toDateTime()]);
            }
            $cdr->groupBy(...$agents);
            return response()->json($cdr->get()->toArray());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getFilteredOutboundAgentSummaryBKP(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $agents = Cdr::query()->select(['channel'])->where('dstchannel', 'like', "%$outboundTrunkString%")->get()->unique('channel')->pluck('channel');

            $agents = $agents->map(function ($value) {
                return DB::raw("case when channel like 'PJSIP/$value-%' then '$value' END");
            });

            $call_statuses = Cdr::query()->select(['disposition'])->distinct()->get()->pluck('disposition');
            $call_statuses = $call_statuses->map(function ($value) {
                return DB::raw("count(case disposition when '$value' then 1 else null end) as '$value'");
            });
            $cdr = Cdr::query()->leftJoin("users", function ($join) {
                $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
            })
                ->select(['users.username', 'channel', ...$call_statuses])
                ->where('lastapp', 'Dial')
                ->where('dstchannel', 'like', "%$outboundTrunkString%");

            if ($request->has('time') && is_array($request->time)) {
                $start = Carbon::parse($request->time[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->time[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTime(), $end->toDateTime()]);
            } else {
                return response()->json(['message' => 'Please select a valid time range (start and end).'], 400);
            }


            if ($request->has('agent') && !empty($request->agent)) {
                $agent = $request->agent;
                $cdr->where('users.auth_username', $agent);
            }

            $cdr->groupBy('channel');

            $result = $cdr->get();

            if ($result->isEmpty()) {
                return response()->json(['message' => 'No data matches your condition.'], 200);
            }

            return response()->json($result->toArray());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getFilteredOutboundAgentSummary(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';

            $call_statuses = Cdr::query()->select('disposition')->distinct()->pluck('disposition');
            $call_statuses = $call_statuses->map(function ($value) {
                return DB::raw("count(case disposition when '$value' then 1 else null end) as `$value`");
            });

            $cdr = Cdr::query()
                ->leftJoin('users', function ($join) {
                    $join->on('cdr.channel', 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
                })
                ->select([
                    'users.username',
                    'cdr.channel',
                    ...$call_statuses
                ])
                ->where('cdr.lastapp', 'Dial')
                ->where('cdr.dstchannel', 'like', "%$outboundTrunkString%");

            if ($request->has('agent') && !empty($request->agent)) {
                $cdr->where('users.auth_username', $request->agent);
            }

            if ($request->has('time') && is_array($request->time)) {
                $start = Carbon::parse($request->time[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->time[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTimeString(), $end->toDateTimeString()]);
            }

            $cdr->groupBy('users.username');

            return response()->json($cdr->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getOutboundAgentSummary(): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $agents = Cdr::query()->select(['channel'])->where('dstchannel', 'like', "%$outboundTrunkString%")->get()->unique('channel')->pluck('channel');
            $agents = $agents->map(function ($value) {
                return DB::raw("case when channel like 'PJSIP/$value-%' then '$value' END");
            });
            $call_statuses = Cdr::query()->select(['disposition'])->distinct()->get()->pluck('disposition');
            $call_statuses = $call_statuses->map(function ($value) {
                return DB::raw("count(case disposition when '$value' then 1 else null end) as '$value'");
            });
            $cdr = Cdr::query()->leftJoin("users", function($join) {
                $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
            })
                 ->select(['users.username','channel', ...$call_statuses])
                ->where('lastapp', 'Dial')
                ->where('dstchannel', 'like', "%$outboundTrunkString%")
                ->whereDate('start', '>=', $date->startOfDay())
                ->whereDate('end', '<=', $date->endOfDay())
                ->groupBy(...$agents);
            return response()->json($cdr->get()->toArray());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
