<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('module_accessibilities', function (Blueprint $table) {
            $table->id();
            $table->string('module_name')->unique();
            $table->boolean('agent_accessibility')->default(0); // 0 = No, 1 = Yes
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('module_accessibilities');
    }
};
