<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\QueueLog;
use App\Models\SystemSetting;
use App\Models\WorkCode;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InboundDispositionSummaryController extends Controller
{
    public function getInboundDispositionSummaryOLD(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $cdr = Cdr::query()->where('lastapp', 'Queue')
                ->join('queue_log', 'uniqueid', '=', 'queue_log.callid')
                ->where('Event', 'WORKCODE')->join('work_codes', 'id', '=', 'queue_log.data1')
                ->select('id', 'name as call_status', DB::raw('count(*) as count'))
                ->groupBy('name');
            if ($request->has('date') && is_array($request->date)) {
                $start = Carbon::parse($request->date[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->date[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTime(), $end->toDateTime()]);
            }
            if ($request->has('queue')){
                $cdr->where('queue_log.queuename', 'like', "%{$request->queue}%");
            }
            return response()->json($cdr->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getInboundDispositionSummary(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $subQuery = DB::table('queue_log')->where('Event', '=', 'CONNECT')->select(
                'callid',
                'data2 as src',
                DB::raw("(CASE WHEN Event = 'CONNECT' THEN 'ANSWERED' ELSE 'NO ANSWER' END) as call_status")
            );

            $queueLog = DB::table('queue_log')
                ->where('Event', '=', 'WORKCODE')
                ->join('work_codes', 'queue_log.data1', '=', 'work_codes.id')
                ->leftJoinSub($subQuery, 'enterqueue', function($join) {
                    $join->on('queue_log.callid', '=', 'enterqueue.callid');
                })->join('cdr',function($join){
                    $join->on('queue_log.callid','=','cdr.uniqueid')
                        ->where('cdr.accountcode','=','queue');
                })
                ->select('work_codes.id', 'work_codes.name  as call_status', DB::raw('count(DISTINCT queue_log.callid) as count'));

            // Apply date range filter
            if ($request->has('date') && is_array($request->date)) {
                //$start = Carbon::parse($request->date[0])->timezone('Asia/Karachi');
                //$end = Carbon::parse($request->date[1])->timezone("Asia/Karachi");
                //$queueLog->whereBetween('queue_log.time', [$start->toDateTime(), $end->toDateTime()]);

                $start = Carbon::parse($request->date[0])->startOfDay();
                $end = Carbon::parse($request->date[1])->endOfDay();
                $queueLog->whereBetween('queue_log.time', [$start, $end]);
            }

            // Filter by destination queue
            if ($request->has('queue')) {
                $queueLog->where('queue_log.queuename', 'like', "%{$request->queue}%");
            }

            $result = $queueLog->groupBy('name')->get();

            return response()->json($result);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
