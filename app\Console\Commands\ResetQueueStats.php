<?php

namespace App\Console\Commands;

use App\Models\Queue;
use App\Models\SystemSetting;
use Illuminate\Console\Command;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueueResetAction;

class ResetQueueStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reset:queue-stats';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset queue stats every 24 hours';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    private function getAmiOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    /**
     * Execute the console command.
     *
     * @return int
     * @throws \PAMI\Client\Exception\ClientException
     */
    public function handle(): int
    {
        $client = new ClientImpl($this->getAmiOptions());
        $queues = Queue::query()->select(['name'])->get();
        $client->open();
        $response = [];
        foreach ($queues as $queue) {
            $action = new QueueResetAction($queue->name);
            $res = $client->send($action);
            $this->info("Queue {$queue->name}: {$res->getMessage()}");
        }
        $client->close();
        return 0;
    }
}
