<?php

namespace App\Models;

use App\Events\AgentlessRecordingEvent;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Recording extends Model
{
    use HasFactory;

    protected $fillable = ['file_name' , 'file_path'];

    public function getCreatedAtAttribute($value) {
        return Carbon::parse($value)->setTimezone('Asia/Karachi')->toDateTimeString(); 
    }


    
    protected static function boot()
    {
        parent::boot();

        static::created(function () {
            broadcast(new AgentlessRecordingEvent(Recording::all()));
        });

        static::updated(function () {
            broadcast(new AgentlessRecordingEvent(Recording::all()));
        });

        static::deleted(function () {
            broadcast(new AgentlessRecordingEvent(Recording::all()));
        });
    }

}
