<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// class CreateCustomNumbersTable extends Migration
return new class extends Migration

{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('custom_numbers', function (Blueprint $table) {
            $table->id();
            $table->string('retailer_name');
            $table->string('shop_name');
            $table->string('agent_id');
            $table->string('inactive_days');
            $table->string('target');
            $table->string('gmv');
            $table->unsignedBigInteger('number_of_unique_sku_sold');
            $table->unsignedBigInteger('attempts');
            $table->string('customer_phone');
            $table->unsignedBigInteger('campaign_id');
            $table->boolean('status')->default(false);
            $table->string('file');
            $table->foreign('campaign_id')->references('id')->on('campaigns')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('custom_numbers');
    }
};
