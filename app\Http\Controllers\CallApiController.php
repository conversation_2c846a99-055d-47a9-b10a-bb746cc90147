<?php

namespace App\Http\Controllers;

use App\Models\SystemSetting;
use Illuminate\Http\Request;
use PAMI\Client\Exception\ClientException;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\OriginateAction;

class CallApiController extends Controller
{
    public function click_to_call(Request $request): string
    {
        $request->validate([
            //'queue' => ['required', 'exists:queues,name'],
            'number' => ['required', 'digits_between:11,12'],
            'callerId' => ['required', 'numeric']
        ]);
        $queue = $request->queue;
        $number = $request->number;
        $callerId = $request->callerId;
        $client = new ClientImpl($this->getOptions());
        $action = new OriginateAction("PJSIP/{$number}@BykeyaPJSIP");
        $action->setCallerId($callerId);
        $action->setExtension($queue);
        $action->setPriority("1");
        $action->setContext("default");
        $action->setAsync(true);
        try {
            $client->open();
            $response = $client->send($action);
            $client->close();
            return response()->json($response->getMessage());
        } catch (\Exception $e) {
            return response()->json($e->getMessage());
        }
    }

    private function getOptions(): array
    {
        return [
            'host' => '127.0.0.1', //SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }
}
