<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Event\HangupEvent;
use PAMI\Message\Event\DialEvent;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;
use App\Models\SystemSetting;

class ListenAsteriskCalls extends Command
{
    protected $signature = 'app:listen-asterisk-calls';
    protected $description = 'Command description';
    protected $client;

    public function __construct()
    {
        parent::__construct();
        $this->client = new ClientImpl($this->getOptions());
    }

    protected function getOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    public function handle()
    {
        $consecutiveErrors = 0;
        $lastReconnect = 0;

        while (true) {
            try {
                $this->client = new ClientImpl($this->getOptions());
                $this->client->open();
                $this->info("Connected to Asterisk Manager Interface");
                $consecutiveErrors = 0;

                $this->client->registerEventListener(function ($event) {
                    if ($event instanceof DialEvent) {
                        Log::info("Call answered by agent: " . $event->getCallerIDNum());
                    }

                    if ($event instanceof HangupEvent) {
                        $uniqueid = $event->getUniqueID();
                        $date = date('Y/m/d');
                        $recordingDir = "/var/spool/asterisk/monitor/{$date}/";
                        $files = glob("{$recordingDir}*{$uniqueid}*.wav");

                        if (!empty($files)) {
                            $filename = $files[0];
                            Log::info("Recording file found: " . $filename);

                            $this->transcribeAndSave($filename, $uniqueid);
                        } else {
                            Log::warning("No recording file found for UniqueID: " . $uniqueid);
                        }
                    }
                });

                while (true) {
                    $this->client->process();
                    usleep(200000);

                    if (time() - $lastReconnect > 3600) {
                        $this->info("Periodic reconnection after 1 hour");
                        break;
                    }
                }
            } catch (\Exception $e) {
                $consecutiveErrors++;
                Log::error("AMI Connection error: " . $e->getMessage());

                if ($consecutiveErrors > 5) {
                    $this->error('Too many consecutive errors, sleeping for 60 seconds...');
                    sleep(60);
                    $consecutiveErrors = 0;
                } else {
                    sleep(5);
                }
                $lastReconnect = time();
            } finally {
                if (isset($this->client)) {
                    $this->client->close();
                }
            }
        }
    }

    function transcribeAndSave($filePath, $uniqueid)
    {
        $cleanedPath = str_replace('.wav', '_cleaned.wav', $filePath);

        $ffmpegCommand = [
            'ffmpeg',
            '-y',
            '-i',
            $filePath,
            '-ar',
            '16000', // Resample to 16kHz
            '-ac',
            '1',     // Convert to mono
            '-af',
            'highpass=f=200, lowpass=f=3000, dynaudnorm=p=0.5, silenceremove=1:0:-50dB',
            $cleanedPath
        ];

        $ffmpegProcess = new Process($ffmpegCommand);
        $ffmpegProcess->setTimeout(0);
        $ffmpegProcess->run();

        if (!$ffmpegProcess->isSuccessful()) {
            Log::error("FFmpeg failed to process audio: " . $ffmpegProcess->getErrorOutput());
            return;
        }

        Log::info("Audio preprocessed successfully: " . $cleanedPath);

        $pythonScript = base_path('app/Python/speech_to_text.py');
        $pythonCommand = ['python3', $pythonScript, $cleanedPath];

        $pythonProcess = new Process($pythonCommand);
        $pythonProcess->setTimeout(0);
        $pythonProcess->run();

        if (!$pythonProcess->isSuccessful()) {
            Log::error("Transcription failed: " . $pythonProcess->getErrorOutput());
            return;
        }

        $transcription = trim($pythonProcess->getOutput());

        DB::table('cdr')->where('uniqueid', $uniqueid)->update([
            'transcription' => $transcription,
        ]);

        Log::info("Transcription saved for UniqueID: " . $uniqueid);

        // Delete cleaned file after transcription
        if (file_exists($cleanedPath)) {
            unlink($cleanedPath);
        }
    }
}
