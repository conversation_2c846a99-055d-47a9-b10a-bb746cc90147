<?php

namespace App\Http\Controllers;

use App\Imports\CampaignNumbersImport;
use App\Models\Campaign;
use App\Models\CampaignNumber;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Matrix\Exception;
use App\Exports\CampaignNumberExport;
use Carbon\Carbon;

class CampaignNumberController extends Controller
{
    /**
     * @param Campaign $campaign
     * @param CampaignNumber $campaignNumber
     * @return JsonResponse
     */
    public function updateState(Campaign $campaign, CampaignNumber $campaignNumber) : JsonResponse
    {
        sleep(5);
        try {
            $record = DB::table('cdr')->where('dst', $campaignNumber->number)->latest('start')->first();
            if($record && $record->disposition === 'ANSWERED') {
                $campaignNumber->status = true;
                $campaignNumber->disposition = $record->disposition;
                $campaignNumber->channel = $record->channel;
                $campaignNumber->billsec = $record->billsec;
                $campaignNumber->duration = $record->duration;
                $campaignNumber->dial_time = $record->start;
                $campaignNumber->save();
            } elseif($record) {
                $campaignNumber->status = false;
                $campaignNumber->disposition = $record->disposition;
                $campaignNumber->channel = $record->channel;
                $campaignNumber->billsec = $record->billsec;
                $campaignNumber->duration = $record->duration;
                $campaignNumber->dial_time = $record->start;
                $campaignNumber->attempts--;
                $campaignNumber->save();
            }

            // sql serve fields update will be here
            // status, attempts

            return response()->json("Number response has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Fetch number from campaign
     * @param Campaign $campaign
     * @return JsonResponse
     */
    public function fetch(Campaign $campaign): JsonResponse
    {
        return response()->json($campaign->getNumber());
    }

    /**
     * Display a listing of the resource.
     *
     * @param Campaign $campaign
     * @return JsonResponse
     */
    public function index(Campaign $campaign, Request $request): JsonResponse
    {
        try{
            return response()->json($campaign->campaign_numbers()->paginate($record ?? 15));
        }catch(\Exception $exception) {
            return \response()->json($exception->getMessage(), 500);
        }
        
    }

    public function getFilteredCampaignNumber(Campaign $campaign, Request $request)
    {
        try{
            $record = $request->record;
            $data = $campaign->campaign_numbers();
            if ($request->has('number')) {
                $data = $data->where('number' , $request->number);
            }
            if ($request->has('disposition')) {
                $data = $data->where('disposition', $request->disposition);
            }
            if($request->has('range') && is_array($request->range)) {
                //dd($data);
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $data = $data->whereDate('dial_time' ,'>=',$start)->whereDate('dial_time', '<=', $end);
            }
            $data = $data->paginate($record ?? 15);
            return response()->json($data);
        }catch(\Exception $exception) {
            return \response()->json($exception->getMessage(), 500);
        }
    }


    public function test(){
        return \response()->json('ok ok ');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Campaign $campaign
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Campaign $campaign, Request $request): JsonResponse
    {
        $request->validate([
            'file' => ['required'],
        ]);
        try {
            $file = $request->file('file');
            $path = $request->file('file')->storeAs('uploads', $file->getClientOriginalName());
            if ($path) {
                Excel::import(new CampaignNumbersImport($path, $campaign), $path);
                return response()->json("file uploaded at path: {$path}");
            } else {
                return response()->json('file could not be uploaded.', 422);
            }
        } catch (Exception $exception) {
            return \response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param CampaignNumber $campaignNumber
     * @return JsonResponse
     */
    public function update(Campaign $campaign, CampaignNumber $campaignNumber, Request $request): JsonResponse
    {
        $request->validate([
            'number' => ['required', 'numeric']
        ]);

        try {
            $campaignNumber->update($request->all());
            return response()->json("Number has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Campaign $campaign
     * @param CampaignNumber $campaignNumber
     * @return JsonResponse
     */
    public function destroy(Campaign $campaign, CampaignNumber $campaignNumber): JsonResponse
    {
        try {
            $campaignNumber->delete();
            return response()->json("Number {$campaignNumber->number} has been deleted.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }
    public function campaignNumberExport(Campaign $campaign, Request $request)
    {
        try{
            $data = $campaign->campaign_numbers();
            if ($request->has('number')) {
                $data = $data->where('number' , $request->number);
            }
            if ($request->has('disposition')) {
                $data = $data->where('disposition', $request->disposition);
            }
            if($request->has('range') && is_array($request->range)) {
                //dd($data);
                $start = Carbon::parse($request->start)->timezone('Asia/Karachi');
                $end = Carbon::parse($request->end)->timezone("Asia/Karachi");
                $data = $data->whereDate('dial_time' ,'>=',$start)->whereDate('dial_time', '<=', $end);
            }
            $data = $data->get()->toArray();
            return Excel::download(new CampaignNumberExport($data), 'CampaignNumberExport.csv');
        }catch(\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
        
    }

}
