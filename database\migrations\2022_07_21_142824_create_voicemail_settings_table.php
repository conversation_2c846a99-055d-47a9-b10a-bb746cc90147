<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// class CreateVoicemailSettingsTable extends Migration
return new class extends Migration

{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('voicemail_settings', function (Blueprint $table) {
            $table->id();
            $table->json('weeks');
            $table->json('except')->nullable();
            $table->json('specificDates')->nullable();
            $table->time('specificDateStart')->nullable();
            $table->time('specificDateEnd')->nullable();
            $table->time('start');
            $table->time('end');
            $table->boolean('status');
            $table->unsignedBigInteger('voicemail_file_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('voicemail_settings');
    }
};
