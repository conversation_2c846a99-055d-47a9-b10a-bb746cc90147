<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;

class RingNoAnswerExport implements FromArray, WithMapping, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
   public function __construct(array $data) {

        $this->data = $data;
   }

   public function array(): array {

        return $this->data;
   }

   public function map($data): array {

        return[
            $data->queue,
            $data->partya,
            $data->agent,
            $data->time
        ];
   }

   public function headings(): array {

        return[
            'Queue',
            'Party A',
            'Agent',
            'Time'
        ];
   }
}
