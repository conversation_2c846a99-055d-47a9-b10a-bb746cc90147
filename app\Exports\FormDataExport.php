<?php

namespace App\Exports;

use App\Models\FormData;
use App\Models\FormField;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class FormDataExport implements FromArray,WithMapping,WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    // public function collection()
    // {
    //     return FormData::all();
    // }
    protected $data;
    protected $form_id;
    
    public function __construct(array $data, $form_id)
    {   
        $this->data = $data;
        $this->form_id = $form_id;

        
    }
    public function array(): array
    {
        return $this->data;
    }

    public function map($data): array
    {  
        $keys = FormField::where('form_id', $this->form_id)->select('name')->get()->pluck('name')->toArray();
        $result = [];
        foreach($keys as $key)
        {   if(array_key_exists($key, $data['data'])) {
                if(is_array($data['data'][$key])){
                    
                    $result[] =  implode(" ; " , $data['data'][$key]);
                }else{
                    $result[] = $data['data'][$key];
                }
            }else {
                $result[] = '';
            }   
        }
        return [
            $result
        ];
    }

    public function headings(): array
    {
        $headings = FormField::where('form_id', $this->form_id)->select('label')->get()->pluck('label')->toArray();
        
        return[
            $headings
        ];
    }
}
