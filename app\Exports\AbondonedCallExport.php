<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AbondonedCallExport implements FromArray, WithHeadings
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;

    }
    public function array(): array
    {
        return $this->data;

    }

    public function headings(): array
    {
        return ['Date', 'Time', 'Number', 'Position', 'Orig Position', 'Wait'];
    }
}