<?php

namespace App\Http\Controllers;

use App\Models\Remark;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RemarkController extends Controller
{
    public function store_backup(Request $request)
    {
        try {
            $data = $request->validate([
                'unique_id' => ['required', 'unique:remarks,unique_id'],
                'agent_id' => ['required', 'integer'],
                'remark' => ['required','string']
            ]);
            Remark::create($data );
            return response()->json(['message' => 'Remark Created Successfully.']);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }

    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'unique_id' => ['required', 'unique:remarks,unique_id'],
                'agent_id' => ['required', 'integer'],
                'remark' => ['required','string']
            ]);

            if(Remark::create($data)) {
                if (isset($request['var_id']) && !empty($request['var_id'])) {
                    DB::connection('sqlsrv')->table('staging.family_planning.call_center')->where('vr_id', $request['var_id'])->update(['followup_remarks' => $data['remark']]);
                }

                return response()->json(['message' => 'Remark Created Successfully.']);
            }

            return response()->json(['message' => 'Failed to create remark.']);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }


}
