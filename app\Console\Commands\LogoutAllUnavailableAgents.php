<?php

namespace App\Console\Commands;

use App\Models\Queue;
use App\Models\QueueLog;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Illuminate\Console\Command;
use PAMI\Client\Exception\ClientException;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueueRemoveAction;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Action\QueueUnpauseAction;
use PAMI\Message\Event\QueueMemberEvent;
use App\Events\{ IsReady, AgentLogin, AgentStatus };
use App\Models\User;

class LogoutAllUnavailableAgents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logout:unavailable-agents';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Logout all unavailable agents.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get PAMI client
     * @return ClientImpl
     */
    public function getClient($options): ClientImpl
    {
        return new ClientImpl($options);
    }

    /**
     * Get manager options
     * @return array
     */
    public function getManagerOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    /**
     * Get all queues
     * @return array
     */
    public function getAllQueues(): array
    {
        return Queue::query()->select(['name'])->pluck('name')->toArray();
    }

    /**
     * Get login time of agent
     * @return Carbon
     */
    public function getLoginTime($agent)
    {
        $q = QueueLog::query()->where('Agent', $agent)->where('Event', 'ADDMEMBER')->whereDate('time', now()->toDateString())->orderBy('time', 'desc')->first();
        return Carbon::parse($q->time);
    }

    /**
     * Execute the console command.
     *
     * @return int
     * @throws ClientException
     */
    public function handle()
    {
        $client = $this->getClient($this->getManagerOptions());
        $client->open();
        foreach ($this->getAllQueues() as $allQueue) {
            $action = new QueueStatusAction($allQueue);
            $res = $client->send($action);
            $events = $res->getEvents();
            foreach ($events as $event) {
                if($event instanceof QueueMemberEvent && $event->getKey('status') == 5) {
                    if($event->getKey('paused') == 1) // This means agent is paused then unpause him to get correct record in reports
                    {
                        $pauseAction = new QueueUnpauseAction($event->getKey('stateinterface'), $event->getKey('queue'), $event->getKey('pausedreason'));
                        $res = $client->send($pauseAction);
                    }

                    $removeAction = new QueueRemoveAction($event->getKey('queue'), $event->getKey('stateinterface'));
                    $res = $client->send($removeAction);

                    $userId = $this->getUserIdFromAgent($event->getKey('stateinterface'));

                    broadcast(new IsReady(['user_id' => $userId, 'status' => false]))->toOthers();
                    broadcast(new AgentLogin(['user_id' => $userId, 'status' => false]))->toOthers();
                    broadcast(new AgentStatus(['user_id' => $userId, 'status' => false]))->toOthers();
                }
            }
        }
        $client->close();
        return 0;
    }

    protected function getUserIdFromAgent(string $agentName): ?int
    {
        if (strpos($agentName, 'PJSIP/') === false) {
            return User::where('auth_username', $agentName)->value('id');
        }

        return User::where('auth_username', str_replace('PJSIP/', '', $agentName))->value('id');
    }
}
