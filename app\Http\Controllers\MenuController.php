<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Menu;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MenuController extends Controller
{

    public function __construct() {

        $this->middleware('permission:create_visualivr', ['only' => ['store']]);
        $this->middleware('permission:update_visualivr', ['only' => ['update']]);
        $this->middleware('permission:delete_visualivr', ['only' => ['destroy']]);
        $this->middleware('permission:read_visualivr', ['only' => ['index']]);
    }
    
    public function index()
    {
        return Menu::with('options')->get();
    }

    public function getAllMenu()
    {
        return Menu::all();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:menus,name',
            'prompt_text' => 'nullable|string',
            'media' => ['nullable', 'file', 'mimes:wav'],
            'priority' => 'boolean',
            'off_working' => 'boolean',
        ]);

        // Ensure only one of priority or off_working is true
        if ($request->boolean('priority') && $request->boolean('off_working')) {
            return response()->json(['error' => 'Only one of priority or off_working can be selected.'], 422);
        }

        if ($request->hasFile('media')) {
            $storedFilePath = $request->file('media')->storeAs('ivr', $request->file('media')->getClientOriginalName(), 'sounds');

            if(!$storedFilePath) {
                return response()->json(['error' => 'Failed to save media file'], 500);
            }

            $validated['media'] = explode('.', $storedFilePath)[0];
            $validated['path']  = $storedFilePath;
        }
        

        if ($validated['priority']) {
            Menu::where('priority', true)->update(['priority' => false]);
        }

        if ($validated['off_working']) {
            Menu::where('off_working', true)->update(['off_working' => false]);
        }

        return Menu::create($validated);
    }

    public function show(Menu $menu)
    {
        return $menu->load('options');
    }

    public function update(Request $request, Menu $menu)
    {
        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'prompt_text' => 'sometimes|string',
            'media' => ['nullable', 'file', 'mimes:wav'],
            'priority' => 'boolean',
            'off_working' => 'boolean',
        ]);

        if ($request->hasFile('media')) {
            $storedFilePath = $request->file('media_file')->storeAs('ivr', $request->file('media_file')->getClientOriginalName(), 'sounds');

            if(!$storedFilePath) {
                return response()->json(['error' => 'Failed to save media file'], 500);
            }

            $validated['media'] = explode('.', $storedFilePath)[0];
            $validated['path']  = $storedFilePath;
        }

        if (isset($validated['priority']) && $validated['priority']) {
            Menu::where('priority', true)->update(['priority' => false]);
        }

        if (isset($validated['off_working']) && $validated['off_working']) {
            Menu::where('off_working', true)->update(['off_working' => false]);
        }

        $menu->update($validated);
        return $menu;
    }

    public function destroy(Menu $menu)
    {
        try {
            // Delete or update menu options referencing this menu as target
            \DB::transaction(function () use ($menu) {
                // Update references to null (or any valid target menu_id)
                \DB::table('menu_options')
                    ->where('target_menu_id', $menu->id)
                    ->update(['target_menu_id' => null]);

                // Delete all associated options
                $menu->options()->delete();

                // Finally, delete the menu
                $menu->delete();
            });

            return response()->json(['message' => 'Menu deleted successfully!'], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to delete menu.', 'error' => $e->getMessage()], 500);
        }
    }


    public function updatePriority(Request $request, $id)
    {
        $request->validate([
            'priority' => 'required|boolean',
        ]);

        $priorityValue = $request->priority;

        try {
            DB::transaction(function () use ($id, $priorityValue) {
                $menu = Menu::findOrFail($id);

                if ($priorityValue && $menu->off_working) {
                    throw new \Exception('A menu cannot have both priority and off_working enabled.');
                }
                
                // If setting a menu as priority, clear other priorities
                if ($priorityValue == true) {
                    Menu::where('priority', true)->update(['priority' => false]);
                }

                // Update priority for the specified menu
                $menu->priority = $priorityValue;
                $menu->save();
            });

            return response()->json(['message' => 'Menu priority updated successfully!'], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to update menu priority. Please try again.', 'error' => $e->getMessage()], 500);
        }
    }

    public function updateOffWorking(Request $request, $id)
    {
        $request->validate([
            'off_working' => 'required|boolean',
        ]);

        $off_workingValue = $request->off_working;

        try {
            DB::transaction(function () use ($id, $off_workingValue) {
                $menu = Menu::findOrFail($id);
                
                if ($off_workingValue && $menu->priority) {
                    throw new \Exception('A menu cannot have both priority and off_working enabled.');
                }

                if ($off_workingValue == true) {
                    Menu::where('off_working', true)->update(['off_working' => false]);
                }

                // Update off_working for the specified menu
                $menu->off_working = $off_workingValue;
                $menu->save();
            });

            return response()->json(['message' => 'Menu off_working updated successfully!'], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to update menu off_working. Please try again.', 'error' => $e->getMessage()], 500);
        }
    }
}
