<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class WorkCode extends Model
{
    use HasFactory;
    protected $fillable = ['name'];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->diffForHumans();
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->diffForHumans();
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($workCode) {
            Cache::forget('work_codes');
        });

        static::deleted(function ($workCode) {
            Cache::forget('work_codes');
        });
    }
}
