<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// class CreateNavbarTable extends Migration
return new class extends Migration

{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if(!Schema::hasTable('navbar')){
            Schema::create('navbar', function (Blueprint $table) {
                $table->id('navbar_id');
                $table->string('menu_title');       // The title of the menu
                $table->string('route')->nullable();            // The route that the menu links to
                $table->unsignedBigInteger('parent_id')->nullable(); // For parent-child relationship
                $table->string('permission')->nullable();    // Permission to view the menu
                $table->boolean('module_accessibility')->default(true); // Determine if the menu is accessible (true/false)
                $table->timestamps();

                // Foreign key for parent_id (self-referencing for hierarchical structure)
                $table->foreign('parent_id')->references('navbar_id')->on('navbar')->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('navbar');
    }
};
