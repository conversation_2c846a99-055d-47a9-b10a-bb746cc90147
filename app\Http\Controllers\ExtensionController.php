<?php

namespace App\Http\Controllers;

use App\Models\Extension;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;

class ExtensionController extends Controller
{

    public function __construct() {

        $this->middleware('permission:create_extension', ['only' => ['create']]);
        $this->middleware('permission:update_extension', ['only' => ['update']]);
        $this->middleware('permission:delete_extension', ['only' => ['destroy']]);
        $this->middleware('permission:read_extension', ['only' => ['index']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response()->json(['data' => Extension::all()]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validate = Validator::make( $request->all() , $this->validation());
        if($validate->fails())
            return response()->json(['error' => $validate->messages()] , 422);
        else
        {
            $extension = Extension::create([
                "context" => $request->context,
                "exten" => $request->exten,
                "priority" => $request->priority,
                "app" => $request->app,
                "appdata" => $request->appdata
            ]);
            if(isset($extension))
                return response()->json(['success' => 1 , "data" => $extension]);
            else
                return response()->json(['error' => "data is not inserted"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Extension  $extension
     * @return \Illuminate\Http\Response
     */
    public function show(Extension $extension)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Extension  $extension
     * @return \Illuminate\Http\Response
     */
    public function edit(Extension $extension)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Extension  $extension
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request , Extension $extension)
    {
        $validator = Validator::make( $request->all() , $this->validation());
        if($validator->fails())
            return response()->json(['error' => $validator->messages()]);
        $data = $extension->update($request->all());
        if($data)
            return response()->json(['data' => $data , 'status' => '1']);
        else
            return response()->json(['data' => 'data is not inserted' , 'status' => '0']);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Extension  $extension
     * @return \Illuminate\Http\Response
     */
    public function destroy(Extension $extension)
    {
        $status = $extension->delete();
        if($status)
            return response()->json(['data'=> 'data deleted' , 'status' => '1']);

        return response()->json(['data'=> 'data not deleted' , 'status' => '0']);
    }

    private function validation()
    {
        return [
            "context" => "required",
            "exten" => "required",
            "priority" => "required",
            "app" => "required",
            "appdata" => "required"
        ];
    }

}
