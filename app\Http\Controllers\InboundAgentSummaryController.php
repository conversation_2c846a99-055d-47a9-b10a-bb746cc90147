<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InboundAgentSummaryController extends Controller
{
    public function getInboundAgentSummaryColumns(): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(Cdr::query()->select(['disposition'])->distinct()->get()->pluck('disposition'));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getFilteredInboundAgentSummary(Request $request): \Illuminate\Http\JsonResponse
    {
        try {

            $inboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $accountcode = ['Custom_Routing','Queue'];
            $date = [];
            $date[0] = Carbon::now()->startOfDay();
            $date[1] = Carbon::now()->endOfDay();
            if($request->time && is_array($request->time)) {
                $date[0] = Carbon::parse($request->time[0])->timezone('Asia/Karachi');
                $date[1] = Carbon::parse($request->time[1])->timezone('Asia/Karachi');
            }

            $query = DB::table('queue_log')
                        ->select(
                            DB::raw("agent as name"),
                            DB::raw("COUNT(CASE WHEN Event = 'CONNECT' THEN 1 END) as ANSWERED"),
                            DB::raw("'0' as BUSY"),
                            DB::raw("'0' as FAILED"),
                            DB::raw("COUNT(CASE WHEN Event = 'RINGNOANSWER' THEN 1 END) as 'NO ANSWER'")
                        )
                        ->whereIn('Event', ['CONNECT', 'RINGNOANSWER'])
                        ->whereDate('time', '>=', $date[0])
                        ->whereDate('time', '<=', $date[1])
                        ->groupBy('agent');

                        if (isset($request->queue) && !empty($request->queue)) {
                            $query->where('queuename', 'like', "%{$request->queue}%");
                        }

            $results = $query->get();

            return response()->json($results);


        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getInboundAgentSummary(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $inboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $accountcode = ['Queue','Custom_Routing'];
            $date = [];
            $date[0] = Carbon::now()->startOfDay();
            $date[1] = Carbon::now()->endOfDay();
            if($request->time && is_array($request->time)) {
                $date[0] = $request->time[0];
                $date[1] = $request->time[1];
            }
            $query = Cdr::query();
            $call_statuses = Cdr::query()->select(['disposition'])->distinct()->get()->pluck('disposition');
            $call_statuses = $call_statuses->map(function ($value) {
                return DB::raw("count(case disposition when '$value' then 1 else null end) as '$value'");
            });
            $query->join('ps_endpoints as p', 'cdr.dstchannel', 'like', DB::raw("CONCAT('%', p.id, '%')"));
            $query->join('users as u', 'p.id', '=', 'u.auth_username');
            $query->select(['p.id', 'u.name', ...$call_statuses]);
            $query->whereIn('cdr.accountcode', $accountcode);
            $query->where('cdr.channel', 'like', "%{$inboundTrunkString}%");
            $query->whereDate('cdr.start', '>=', $date[0])->whereDate('cdr.start', '<=', $date[1]);
            $query->groupBy('p.id');
            return response()->json($query->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
