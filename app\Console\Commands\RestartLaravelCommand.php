<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;


class RestartLaravelCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'restart:laravel-command-process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will restart the laravel process of custom services';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $process = new Process(['supervisorctl', 'restart laravel-command:*']);
        $process->run();

        $this->info("Command restarted successfully and the output is : {$process->getOutput()}");
    }
}
