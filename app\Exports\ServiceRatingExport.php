<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ServiceRatingExport implements FromCollection, WithHeadings
{
    private $data;
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'id',
            'uniqueid',
            'rating',
            'number',
            'agent',
            'created_at',
            'updated_at',
            'agent_name'
        ];
    }
}
