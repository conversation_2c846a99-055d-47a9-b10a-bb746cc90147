<?php
namespace App\Http\Controllers;
use App\Events\MessageDelieverd;
use App\Models\Message;
use Illuminate\Http\Request;
class MessageDelieveredController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke($id)
    {
        $conv = Message::find($id);
        if (!$conv) {
            return response()->json(['error' => 'Message not found'], 404);
        }
        $conv->status = '1';
        $conv->save();
        broadcast(new MessageDelieverd($conv));
        return response()->json(['message' => 'Message status updated to delivered']);
    }
}