<?php

namespace App\Http\Controllers;

use App\Models\SystemSetting;
use Doctrine\DBAL\Types\Type;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SystemSettingController extends Controller
{

    public function __construct() {

        $this->middleware('permission:create_settings', ['only' => ['store']]);
        $this->middleware('permission:update_settings', ['only' => ['update']]);
        $this->middleware('permission:delete_settings', ['only' => ['destroy']]);
        $this->middleware('permission:read_settings', ['only' => ['index']]);
        $this->middleware('permission:view_settings', ['only' => ['view']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
    //    return response()->json(SystemSetting::query()->get());
        $data = SystemSetting::query()->get();
        $setting=null;
        $myObj = new \stdClass();
        foreach ($data as $value)
        {
            $id= $value->id;
            $column[]=array('title'=> $value->key, 'dataIndex'=> $value->key, 'key'=> $value->key);
            $key= $value->key;
            $myObj->$key= $value->value;
        }
        $setting= array($myObj);
        $column[] = array('title'=> 'action', 'dataIndex'=> 'action', 'key'=>'action');

        return response()->json($setting);
    }


    public function getSettings()
    {
        return response()->json(SystemSetting::query()->get());
    }

    public function getAutoCall()
    {
        return response()->json(SystemSetting::query()->where('key', 'auto_call_answer')->get());
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param SystemSetting $systemSetting
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, SystemSetting $systemSetting): \Illuminate\Http\JsonResponse
    {
//        return response()->json(gettype($request->all()));
        $request->validate([
            'server_address' => ['required'],
            'wss_port' => ['required', 'numeric'],
            'manager_port' => ['required'],
            'username' => ['required'],
            'secret' => ['required'],
            'connection_timeout' => ['required', 'numeric'],
            'read_timeout' => ['required', 'numeric'],
        ]);
        try {
            foreach ($request->all() as $value => $index)
                SystemSetting::query()->where("key", "=", $value)->update([
                    "value" => $index
                ]);
           return response()->json("Settings has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }
}
