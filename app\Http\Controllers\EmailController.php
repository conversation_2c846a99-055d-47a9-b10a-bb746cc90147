<?php

namespace App\Http\Controllers;

use App\Services\MailboxService;
use Illuminate\Http\Request;
use App\Mail\SMTPReplyToEmail;
use Illuminate\Support\Facades\Mail;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class Email<PERSON>ontroller extends Controller
{

    public function index(Request $request)
    {
        $page = $request->query('page', 1);
        $pageSize = $request->query('pageSize', 5);

        $scriptPath = base_path('app/Scripts/fetch_emails.py');
        $process = new Process(['python3', $scriptPath, $page, $pageSize, config('mail.mailers.smtp.username'), config('mail.mailers.smtp.password')]);
        $process->run();

        if (!$process->isSuccessful()) {
            throw new ProcessFailedException($process);
        }

        $output = $process->getOutput();
        $result = json_decode($output, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return response()->json(['error' => 'Invalid JSON output from Python script'], 500);
        }

        return response()->json($result);
    }

    public function store(Request $request)
    {
        set_time_limit(0);

        $validated = $request->validate([
            'message_id' => 'required|string',
            'subject' => 'required|string',
            'email' => 'required|email',
            'body' => 'required|string',
            'references' => 'nullable|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:5120',
        ]);

        try {
            $mail = new SMTPReplyToEmail(
                $validated['message_id'],
                $validated['subject'],
                $validated['body'],
                $request->file('attachments') ?? [],
                $validated['references'] ?? null
            );

            Mail::to($validated['email'])->send($mail);

            return response()->json(['success' => true, 'message' => 'Reply sent successfully']);

        } catch (\Exception $e) {
            \Log::error('Email send failed:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => 'Failed to send email',
                'details' => $e->getMessage()
            ], 500);
        }
    }

}

// namespace App\Http\Controllers;

// use App\Services\MailboxService;
// use Illuminate\Http\Request;
// use App\Mail\SMTPReplyToEmail;
// use Illuminate\Support\Facades\Mail;

// use Symfony\Component\Process\Exception\ProcessFailedException;
// use Symfony\Component\Process\Process;


// class EmailController extends Controller
// {
//     public function index(Request $request)
//     {
//         $page = $request->query('page', 1);
//         $pageSize = $request->query('pageSize', 5);

//         $scriptPath = base_path('app/Scripts/fetch_emails.py');

//         $process = new Process(['python3', $scriptPath, $page, $pageSize]);
//         $process->run();

//         if (!$process->isSuccessful()) {
//             throw new ProcessFailedException($process);
//         }

//         $output = $process->getOutput();
//         $result = json_decode($output, true);

//         if (json_last_error() !== JSON_ERROR_NONE) {
//             return response()->json(['error' => 'Invalid JSON output from Python script'], 500);
//         }

//         return response()->json($result);
//     }

//     public function index_old(Request $request, MailboxService $mailbox)
//     {
//         set_time_limit(0);
//         $page = $request->get('page', 1);
//         $perPage = $request->get('perPage', 10);

//         try {
//             $emails = $mailbox->getEmails($page, $perPage);
//             //return response()->json($emails);

//             // Ensure everything is UTF-8 encoded before json_encode
//             array_walk_recursive($emails, function(&$item) {
//                 if (is_string($item) && !mb_check_encoding($item, 'UTF-8')) {
//                         $item = mb_convert_encoding($item, 'UTF-8', $mailbox->detectEncoding($item));
//                 }
//             });

//             return response()->json($emails, 200, [
//                 'Content-Type' => 'application/json; charset=UTF-8'
//              ], JSON_UNESCAPED_UNICODE);

//         } catch (\Exception $e) {
//             return response()->json(['error' => $e->getMessage()], 500);
//         }
//     }

//     public function store(Request $request, MailboxService $mailbox)
//     {
//         set_time_limit(0);

//         $validated = $request->validate([
//             'message_id' => 'required|string',
//             'subject' => 'required|string',
//             'email' => 'required|email',
//             'body' => 'required|string',
//             'attachments' => 'nullable|array',
//             'attachments.*' => 'file|max:5120',
//         ]);

//         $attachments = [];
//         $files = $request->file('attachments');

//         try {
//             if ($files) {
//                 if (!is_array($files)) {
//                     $files = [$files];
//                 }

//                 foreach ($files as $file) {
//                     if ($file && $file->isValid()) {
//                         $attachments[] = [
//                             'path' => $file->getPathname(),
//                             'name' => $file->getClientOriginalName(),
//                             'mime' => $file->getMimeType(),
//                         ];
//                     }
//                 }
//             }

//             Mail::to($validated['email'])->send(
//                 new SMTPReplyToEmail(
//                     $validated['message_id'],
//                     $validated['subject'],
//                     $validated['body'],
//                     $attachments
//                 )
//             );

//             // $emailThread = $mailbox->getMostRecentReplyByMessageId($validated['message_id']);
//             // return response()->json(['success' => true, 'message' => 'Reply sent successfully', 'email_thread'=> $emailThread]);

//             return response()->json(['success' => true, 'message' => 'Reply sent successfully']);

//         } catch (\Exception $e) {
//             return response()->json(['error' => $e->getMessage()], 500);
//         }
//     }

// }