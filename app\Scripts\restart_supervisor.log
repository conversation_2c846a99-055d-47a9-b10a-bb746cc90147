Warning: supervisor<PERSON><PERSON> returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
Warning: supervisorctl returned exit code 3 (likely due to a FATAL process)
Stopped Supervisor Processes:
agentless-campaign-command:agentless-campaign-command_00   FATAL     Exited too quickly (process log may have details)
Restarting agentless-campaign-command:agentless-campaign-command_00...
agentless-campaign-command:agentless-campaign-command_00: ERROR (not running)
agentless-campaign-command:agentless-campaign-command_00: ERROR (spawn error)
Failed to restart agentless-campaign-command:agentless-campaign-command_00. Exit code: 7
