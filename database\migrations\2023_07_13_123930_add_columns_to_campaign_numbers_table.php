<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// class AddColumnsToCampaignNumbersTable extends Migration
return new class extends Migration

{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('campaign_numbers', function (Blueprint $table) {
            $table->datetime('dial_time');
            $table->integer('duration');
            $table->integer('billsec');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('campaign_numbers', function (Blueprint $table) {
          
        });
    }
};
