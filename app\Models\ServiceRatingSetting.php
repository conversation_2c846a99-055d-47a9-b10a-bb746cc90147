<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ServiceRatingSetting extends Model
{
    use HasFactory;
    protected $fillable = ['best', 'worst', 'status'];

    public function serviceRatingFile(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(ServiceRatingFile::class, 'service_rating_file_id');
    }
}
