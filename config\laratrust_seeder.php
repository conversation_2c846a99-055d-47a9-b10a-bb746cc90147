<?php

return [
    /**
     * Control if the seeder should create a user per role while seeding the data.
     */
    'create_users' => true,

    /**
     * Control if all the laratrust tables should be truncated before running the seeder.
     */
    'truncate_tables' => true,

    'roles_structure' => [
        'superadministrator' => [
            'users' => 'c,r,u,d,v',
            'permissions' => 'c,r,u,d,v',
            'roles' => 'c,r,u,d,v',
            'agents' => 'c,r,u,d,v',
            'queues' => 'c,r,u,d,v',
            'visualivr' => 'c,r,u,d,v',
            'workcodes' => 'c,r,u,d,v',
            'pausedreason' => 'c,r,u,d,v',
            'audio' => 'c,r,u,d,v',
            'inbounds' => 'c,r,u,d,v',
            'outbounds' => 'c,r,u,d,v',
            'settings' => 'r,u,v',
            'agentmonitoring' => 'v',
            'dashboard' => 'v',
            'supervisors' => 'v',
            'campaigns' => 'v',
            'cid-lookup' => 'v',
            'forms' => 'v',
            'scripts' => 'v',
            'breaks' => 'v',
            'voicemails' => 'v',
            'contactflow' => 'v',
            'greetease' => 'v',
            'service-rating' => 'v',
            'dtmf-settings' => 'c,r,u,d,v',
            'bitrix' => 'v',
            'ticker' => 'v',
            'reports' => 'v',
            'prepaidSetting' => 'v',
            'sms' => 'c,r,u,d,v',
            'announcement' => 'c,r,u,d,v',
            'agentlesscampaign' => 'v',
            'callingserver' => 'c,r,u,d,v',
            'agentlessrecording' => 'c,r,u,d,v',
            'agentcampaign' => 'c,r,u,d,v',
            'agentLivemonitoring' => 'v',

//            'payments' => 'c,r,u,d',
//            'profile' => 'r,u'
        ],
        'agent' => [
            'workcodes' => 'v,r',
            'pausedreason' => 'v,r',
            'settings' => 'r,u,v',
        ],

        'supervisor' => [
           'agentmonitoring' => 'v', 
        ],

        'admin' => [
           'agentLivemonitoring' => 'v', 
        ],
    ],

    'permissions_map' => [
        'c' => 'create',
        'r' => 'read',
        'u' => 'update',
        'd' => 'delete',
        'v' => 'view'
    ]
];