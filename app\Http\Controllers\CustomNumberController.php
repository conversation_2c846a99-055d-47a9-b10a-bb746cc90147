<?php

namespace App\Http\Controllers;

use App\Imports\CustomNumberImport;
use App\Models\Campaign;
use App\Models\CustomNumber;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class CustomNumberController extends Controller
{
    /**
     * @param Campaign $campaign
     * @param CustomNumber $customNumber
     * @return JsonResponse
     */
    public function updateState(Campaign $campaign, CustomNumber $customNumber) : JsonResponse
    {
        try {
            $record = DB::table('cdr')->where('dst', $customNumber->customer_phone)->latest('start')->first();
            if ($record && $record->disposition === 'ANSWERED') {
                $customNumber->status = true;
            } else {
                $customNumber->attempts--;
            }
            $customNumber->save();

            return response()->json("Number response has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    /**
     * Display a listing of the resource.
     *
     * @param Campaign $campaign
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Campaign $campaign): \Illuminate\Http\JsonResponse
    {
        return response()->json($campaign->custom_numbers()->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Campaign $campaign
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Campaign $campaign, Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $file = $request->file('file');
            $path = $request->file('file')->storeAs('uploads', $file->getClientOriginalName());

            if($path) {
                Excel::import(new CustomNumberImport($path, $campaign), $path);
                return response()->json("file uploaded at path: {$path}");
            } else {
                return response()->json('file could not be uploaded.', 422);
            }
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Fetch number from campaign
     * @param Campaign $campaign
     * @return JsonResponse
     */
    public function fetch(Campaign $campaign, Request $request): JsonResponse
    {
        return response()->json($campaign->getCustomNumber($request->user()));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param Campaign $campaign
     * @param \App\Models\CustomNumber $customNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Campaign $campaign, CustomNumber $customNumber): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'customer_phone' => ['required', 'numeric'],
            'agent_id' => ['required', 'exists:ps_endpoints,id'],
        ]);

        try {
            $customNumber->update($request->all());
            return response()->json("Number has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Campaign $campaign
     * @param \App\Models\CustomNumber $customNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Campaign $campaign, CustomNumber $customNumber): \Illuminate\Http\JsonResponse
    {
        try {
            $customNumber->delete();
            return response()->json("Number {$customNumber->customer_number} has been deleted.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }
}
