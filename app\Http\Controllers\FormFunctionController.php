<?php

namespace App\Http\Controllers;

use App\Models\FormFunction;
use App\Models\FormField;
use App\Models\FormFieldType;
use App\Models\FormFunctionType;
use Illuminate\Http\Request;

class FormFunctionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function functionType()
    {
        $list = FormFunctionType::query()->get();
        return response()->json($list);
    }

    public function referenceField(Request $request)
    {
        $request->validate([
            'form_id' => 'required'
        ]);
        $field = FormField::where('form_id', $request->form_id)->whereHas('form_field_type' , function($q){
            $q->where('html','date');
        }
        )->get();
        
        return response()->json($field);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
     
        $request->validate([
            
            'label' => ['required', 'string'],
            'name' => ['required', 'string'],
            'function_type_id' => 'required',
            'reference_field_id' => 'required',
            'form_id' => 'required',
            
        ]);
        
        try{
            $function = FormFunction::create($request->all());
            return response()->json('Function has been created');
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\FormFunction  $formFunction
     * @return \Illuminate\Http\Response
     */
    public function show(FormFunction $formFunction)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\FormFunction  $formFunction
     * @return \Illuminate\Http\Response
     */
    public function edit(FormFunction $formFunction)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\FormFunction  $formFunction
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, FormFunction $formFunction)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\FormFunction  $formFunction
     * @return \Illuminate\Http\Response
     */
    public function destroy(FormFunction $formFunction)
    {
        try{
            $formFunction->delete();
            return response()->json('Function has been deleted');
        }catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
