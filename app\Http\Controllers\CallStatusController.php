<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CallStatusController extends Controller
{
    /**
     * Returns distinct dispostion
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            return response()->json(Cdr::query()->select('disposition')->distinct()->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getAccountCodes(): JsonResponse
    {
        try {
            return response()->json(Cdr::query()->select('accountcode')->where('accountcode', '!=', null)->distinct()->pluck('accountcode'));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
