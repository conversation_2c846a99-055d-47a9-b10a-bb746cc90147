<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;


class RemoveQueryParamsFromRequest
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if($request->isMethod('post') || $request->isMethod('put') || $request->isMethod('patch') ) {
            foreach (array_keys($request->query()) as $query) {
                if(Str::contains($query, '/api/')) {
                    $request->query->remove($query);
                }
            }
        }
        return $next($request);
    }
}
