<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Carbon\Traits\Date;
use Illuminate\Http\Request;

class OutboundActivityController extends Controller
{
    public function getOutboundActivity(): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $cdr = Cdr::query()->select(['uniqueid', 'channel', 'dst', 'start', 'duration', 'disposition', 'recordingfile'])
                ->whereDate('start', '=', $date->toDateString())
                ->where('dstchannel', 'like', "%$outboundTrunkString%")
                ->orderBy('start', 'desc')
                ->get();
            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    public function getOutboundActivityFiltered(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $outboundTrunkString = SystemSetting::GetSetting('outbound_string') ?? 'TCL-endpoint';
            $cdr = Cdr::query()->select(['uniqueid', 'channel','src', 'dst', 'start', 'duration', 'disposition', 'recordingfile'])
                ->where('dstchannel', 'like', "%$outboundTrunkString%");

            if ($request->has('range') && is_array($request->range)) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone("Asia/Karachi");
                $cdr->whereBetween("start", [$start->toDateTime(), $end->toDateTime()]);
            }

            if ($request->has('agent')) {
                $agents = $request->agent;
                if (is_array($agents)) {
                    $cdr->where(function ($query) use ($agents) {
                        foreach ($agents as $agent) {
                            $query->orWhere('channel', 'like', "%$agent%");
                        }
                    });
                } else {
                    $cdr->where('channel', 'like', "%$agents%");
                }
            }

            if ($request->has('dst'))
                $cdr->where('dst', $request->dst);
            if ($request->has('call_status'))
                $cdr->where('disposition', $request->call_status);
            $cdr->orderBy('start', 'desc');
            $cdr->get();
            return response()->json($cdr->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }
}
