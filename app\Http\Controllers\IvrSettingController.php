<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\IVR;
use App\Models\IVRSetting;
use App\Models\IVRFile;
use Illuminate\Support\Facades\DB;

class IvrSettingController extends Controller
{
    public function getIVRS(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $query = IVRS::query()->orderBy('created_at', 'desc');
            if($request->date)
                $query->whereDate('created_at', '>=', $request->date[0])
                    ->whereDate('created_at', '<=', $request->date[1]);
            if($request->number)
                $query->where('number', 'LIKE', "%$request->number%");
            return response()->json($query->paginate($request->perPage ?? 10));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getSettings(): \Illuminate\Http\JsonResponse
    {
        try {
            $settings = IVRSetting::query()->first();
            return response()->json($settings);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getIvrFiles(): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(IVRFile::query()->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function uploadFile(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'file' => ['required', 'file', 'mimes:wav']
        ]);

        try {
            $status = $request->file('file')->storeAs('ivr', $request->file('file')->getClientOriginalName(), 'sounds');
            if($status) {
                $model = new IVRFile;
                $model->name = $request->file('file')->getClientOriginalName();
                $model->path = $status;
                $model->fileloc = explode('.', $status)[0];
                $model->save();
            }
            return response()->json($status);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function updateSettings(Request $request): \Illuminate\Http\JsonResponse
    {
        $validatedData = $request->validate([
            'weeks' => 'required',
            'except' => 'nullable|array',
            'specificDates' => 'nullable',
            'specificDateStart' => 'nullable',
            'specificDateEnd' => 'nullable',
            'start' => 'required',
            'end' => 'required',
            'status' => 'required|boolean',
            'ivrFile' => 'nullable|integer',
        ]);

        try {
            $settings = IVRSetting::first();

            $data = [
                'weeks' => $validatedData['weeks'],
                'except' => isset($request->except) ? $validatedData['except'] : null,
                'specificDates' => isset($request->specificDates) ? $validatedData['specificDates'] : null,
                'specificDateStart' => isset($request->specificDateStart) ? $validatedData['specificDateStart'] : null,
                'specificDateEnd' => isset($request->specificDateEnd) ? $validatedData['specificDateEnd'] : null,
                'start' => $validatedData['start'],
                'end' => $validatedData['end'],
                'status' => $validatedData['status'],
                'ivr_file_id' => isset($request->ivrFile) ? $validatedData['ivrFile'] : $settings->ivr_file_id,
            ];

            if(!empty($data['ivr_file_id'])) {
                $file = IVRFile::find($data['ivr_file_id']);
                if(!$file) {
                    return response()->json(
                        [
                            "status" => 404,
                            "message" => "File not found"
                        ]
                    );
                }
            }

            if ($settings) {
                $settings->update($data);
                DB::table('ivr_settings')->where('id', $settings->id)->update([
                    'specificDateStart' => $data['specificDateStart'],
                    'specificDateEnd' => $data['specificDateEnd'],
                ]);

                return response()->json(
                    [
                        "status" => 200,
                        "message" => "IVR settings updated successfully."
                    ]
                );
            } else {
                $created = IVRSetting::create($data);

                if($created) {
                    return response()->json(
                        [
                            "status" => 200,
                            "message" => "IVR settings created successfully."
                        ]
                    );
                }
                else {
                    return response()->json(
                        [
                            "status" => 400,
                            "message" => "Something went wrong."
                        ]
                    );
                }
            }

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
