<?php

namespace App\Http\Controllers;

use App\Models\Campaign;
use App\Models\CampaignLog;
use App\Models\CampaignNumber;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use App\Models\CampaignSetting;
use App\Models\PrepaidSetting;

class CampaignController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        return response()->json(Campaign::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'unique:campaigns,name'],
            'start_time' => ['required', 'date'],
            'end_time' => ['required', 'date']
        ]);

        try {
            
            $prepaid = PrepaidSetting::first();

            if($prepaid->remaining_amount <= ($prepaid->amount * 0.01))
            {
                return response()->json("No amount left.",402);
            }

            $campaign = Campaign::query()->create($request->all());
            return response()->json("Campaign {$campaign->name} has been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Campaign  $campaign
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Campaign $campaign): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'name' => ['sometimes', 'required', 'string', Rule::unique('campaigns', 'name')->ignoreModel($campaign)],
            'start_time' => ['date'],
            'end_time' => ['date']
        ]);

        try {
            $campaign->update($request->all());
            return response()->json("Campaign {$campaign->name} has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Campaign  $campaign
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Campaign $campaign): \Illuminate\Http\JsonResponse
    {
        try {
            $campaign->delete();
            return response()->json("Campaign {$campaign->name} has been deleted.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getCampaignsMonitoring(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $agentMonitoring = DB::select("SELECT camp.name, u.username agent_name, u.auth_username agent_id, a.`event`, a.`updated_at` campaign_date FROM `campaign_logs` a JOIN users u ON (u.id = a.`user_id`) LEFT JOIN `campaigns` camp ON (camp.id = a.`campaign_id`),(SELECT MAX(id) pid FROM `campaign_logs` GROUP BY user_id, campaign_id) b
WHERE a.id = b.pid");
            $campaingsMonitoring = DB::select(" SELECT COUNT(*) AS total_calls, SUM(IF(STATUS=1,1,0)) total_dialed, SUM(IF(STATUS=0,1,0)) total_remaining FROM `campaign_numbers`");
            return response()->json(['campaign' => $campaingsMonitoring, 'agent' => $agentMonitoring]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getCampaignMonitoring($campaign): \Illuminate\Http\JsonResponse
    {
        try {
            $agentMonitoring = DB::select("SELECT u.username agent_name, u.auth_username agent_id, a.`event`, a.`updated_at` campaign_date FROM `campaign_logs` a JOIN users u ON (u.id = a.`user_id`) ,(SELECT MAX(id) pid FROM `campaign_logs` GROUP BY user_id, campaign_id) b
WHERE a.id = b.pid AND a.`campaign_id` = {$campaign}");
            $campaingsMonitoring = DB::select("SELECT COUNT(*) AS total_calls, SUM(IF(STATUS=1,1,0)) total_dialed, SUM(IF(STATUS=0,1,0)) total_remaining FROM `campaign_numbers` where campaign_id = {$campaign} GROUP BY `campaign_id`");
            return response()->json(['campaign' => $campaingsMonitoring, 'agent' => $agentMonitoring]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function campaignSetting(){

        try{
            $data = CampaignSetting::first();
            return response()->json($data);
        }catch(\Exception $e){
            return response()->json($e->getMessage(), 500);

        }

    }

    public function updateCampaignSetting(CampaignSetting $campaignSetting , Request $request) {
        $request->validate([
            'wait_seconds_during_calls' => [
                'required',
                'integer',
                function ($attribute, $value, $fail) {
                    if (! (($value >= 5 && $value <= 60) || $value == 120)) {
                        $fail('The '.$attribute.' must be between 5 and 60 or 120.');
                    }
                },
            ],
        ]);

        try{
            $campaignSetting->update($request->all());
            return response()->json('Setting has been updated');

        }catch(\Exception $e){
            return response()->json($e->getMessage(), 500);

        }
    }

}
