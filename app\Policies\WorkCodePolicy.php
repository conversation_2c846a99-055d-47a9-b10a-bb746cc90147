<?php

namespace App\Policies;

use App\Models\User;
use App\Models\WorkCode;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Auth\Access\Response;

class WorkCodePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\WorkCode  $workCode
     * @return mixed
     */
    public function view(User $user, WorkCode $workCode)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\WorkCode  $workCode
     * @return mixed
     */
    public function update(User $user, WorkCode $workCode)
    {
        return $user->id === $workCode->user_id ? Response::allow() : Response::deny("Access Denied. ".$user->id." user ID");
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\WorkCode  $workCode
     * @return mixed
     */
    public function delete(User $user, WorkCode $workCode)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\WorkCode  $workCode
     * @return mixed
     */
    public function restore(User $user, WorkCode $workCode)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\WorkCode  $workCode
     * @return mixed
     */
    public function forceDelete(User $user, WorkCode $workCode)
    {
        //
    }
}
