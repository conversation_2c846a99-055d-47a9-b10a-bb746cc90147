<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prepaid_settings', function (Blueprint $table) {
            $table->id();
            $table->timestamp('start_date')->nullable();
            $table->integer('pulse_duration'); // plus duration in seconds
            $table->integer('tariff'); // tariff amount per pulse
            $table->integer('amount')->default(0)->nullable();
            $table->integer('remaining_amount')->default(0)->nullable();
            $table->timestamp('pkg_update')->nullable();//this for checking new pakage
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prepaid_settings');
    }
};
