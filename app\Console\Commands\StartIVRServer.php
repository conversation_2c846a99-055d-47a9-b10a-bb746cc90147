<?php

namespace App\Console\Commands;

use App\Models\IVR;
use Illuminate\Console\Command;
use PA<PERSON>\Node\NodeController;
use PA<PERSON>\AsyncAgi\AsyncClientImpl;
use PA<PERSON>\Client\Exception\ClientException;
use PA<PERSON>\Client\Impl\ClientImpl;
use PAMI\Message\Event\AsyncAGIEndEvent;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\AsyncAGIEvent;
use PAMI\Message\Event\EventMessage;

class StartIVRServer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'start:ivr';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start IVR Server';

    private $client;
    private $agi;
    private $script;
    private $collection;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
//        $this->script = IVR::query()->firstOrFail()->first()->script;
//        $this->collection = collect($this->script);
    }

    protected function getOptions()
    {
        return [
            'host' => '***************',
            'scheme' => 'tcp://',
            'port' => '5038',
            'username' => 'defaultapp',
            'secret' => 'randomsecretstring',
            'connect_timeout' => 1000,
            'read_timeout' => 1000
        ];
    }

    public function handle()
    {
        $this->client = new ClientImpl($this->getOptions());
        $this->client->registerEventListener(function (EventMessage $eventMessage) {
            //$this->info(print_r($eventMessage->getKeys()));


            if ($eventMessage instanceof AsyncAGIStartEvent) {
                $this->agi = new AsyncClientImpl([
                    'pamiClient' => $this->client,
                    'asyncAgiEvent' => $eventMessage
                ]);
                //$this->agi->getData("custom/transferToAgent", '10000', 1);
                $script = IVR::query()->first()->script;
                $collection = collect($script)->sortBy('id');
                $target = $collection->where('id', $collection->first()['target']);
                $node = $this->agi->createNode($target['data']['text']);
                if($target['type'] === 'GetData') {

                }
            }

        }, function (EventMessage $eventMessage) {
            return $eventMessage instanceof AsyncAGIStartEvent || $eventMessage instanceof AsyncAGIEndEvent;
        });
        try {
            $this->client->open();
            while(true)
            {
                usleep(1000);
                $this->client->process();
            }
        } catch (ClientException $e) {
            $this->info(json_encode($e->getMessage()));
        }

        $this->client->close();
        return 0;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
//    public function handle()
//    {
//        $loop = \React\EventLoop\Factory::create();
//        $socket = new \React\Socket\Server('0.0.0.0:8080', $loop);
//
//        $socket->on('connection', function (\React\Socket\ConnectionInterface $connection) {
//            /*$connection->write("SAY DATE 1606748056 234\n");*/
//
//            $connection->on('data', function ($data) use ($connection) {
//                $this->info(print_r($data));
//                $connection->write("SAY DATE 1606748056 234\n");
//            });
//        });
//
//        $loop->run();
//        return 0;
//    }
}
