pipeline {
  agent any
  stages {
    stage('Deploy') {
      agent any
      environment {
        DB_HOST = 'localhost'
        DB_DATABASE = 'laraveldb'
        DB_USERNAME = 'jenkinsuser'
        DB_PASSWORD = '123456'
        DB_PORT = '3306'
        DB_CONNECTION = 'mysql'
        SANCTUM_STATEFUL_DOMAINS ='api.retailo-contactplus.com,admin.retailo-contactplus.com,dashboard.retailo-contactplus.com,agent.retailo-contactplus.com'
        SESSION_DOMAIN = '.retailo-contactplus.com'
        APP_NAME = 'Laravel'
        APP_ENV = 'local'
        APP_KEY = 'base64:Ij5DGZXDs2Cl/mlbLpAwwyWYM8duXzfue+WgELkzmT8='
        APP_DEBUG = 'true'
        APP_URL = 'https://api.retailo-contactplus.com'
        LOG_CHANNEL = 'daily'
        LOG_LEVEL = 'debug'
        BROADCAST_DRIVER = 'pusher'
        CACHE_DRIVER = 'file'
        QUEUE_CONNECTION = 'sync'
        SESSION_DRIVER = 'cookie'
        SESSION_LIFETIME = '120'
        REDIS_HOST = '127.0.0.1'
        REDIS_PASSWORD = null
        REDIS_PORT = '6379'
        MAIL_MAILER = 'smtp'
        MAIL_HOST = 'smtp.mailtrap.io'
        MAIL_PORT = '2525'
        MAIL_USERNAME = null
        MAIL_PASSWORD = null
        MAIL_ENCRYPTION = null
        MAIL_FROM_ADDRESS = null
        MAIL_FROM_NAME = '${APP_NAME}'
        PUSHER_APP_ID = '1'
        PUSHER_APP_KEY = 'websocket'
        PUSHER_APP_SECRET = 'abdullah'
        PUSHER_APP_CLUSTER = 'mt1'
        MIX_PUSHER_APP_KEY = '${PUSHER_APP_KEY}'
        MIX_PUSHER_APP_CLUSTER = '${PUSHER_APP_CLUSTER}'
      }
      steps {
        sh ''' printenv > $WORKSPACE/.env


        ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** rm -rf /var/www/html/api.retailo-contactplus.com/*
ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** mkdir -p /var/www/html/api.retailo-contactplus.com/dist/
scp -r $WORKSPACE/* jenkinsuser@**************:/var/www/html/api.retailo-contactplus.com/dist/
ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** mv /var/www/html/api.retailo-contactplus.com/dist/* /var/www/html/api.retailo-contactplus.com/
   ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** chmod -R 777 /var/www/html/api.retailo-contactplus.com/storage/
ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** chmod -R 777 /var/www/html/api.retailo-contactplus.com/storage/*
scp -r $WORKSPACE/.env jenkinsuser@**************:/var/www/html/api.retailo-contactplus.com/.env'''
        sh 'ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** php --version'
        sh 'ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** composer install -d /var/www/html/api.retailo-contactplus.com/'
        sh 'ssh -i $HOME/.ssh/id_rsa jenkinsuser@**************  php /var/www/html/api.retailo-contactplus.com/artisan key:generate'
        sh '''ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** php /var/www/html/api.retailo-contactplus.com/artisan cache:clear
ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** php /var/www/html/api.retailo-contactplus.com/artisan config:cache
ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** php /var/www/html/api.retailo-contactplus.com/artisan migrate
ssh -i $HOME/.ssh/id_rsa jenkinsuser@************** composer dump-autoload -d /var/www/html/api.retailo-contactplus.com/
'''
      }
    }

  }
}
