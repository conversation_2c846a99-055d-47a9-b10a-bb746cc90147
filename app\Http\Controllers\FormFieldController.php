<?php

namespace App\Http\Controllers;

use App\Models\Form;
use App\Models\FormField;
use App\Models\FormFieldOption;
use App\Models\FormFieldType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use App\Http\Requests\StoreFormFieldRequest;
class FormFieldController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Form $form): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(['form_fields' => $form->form_fields()->with('form_field_options')->get(), 'form_function' => $form->form_functions()->with(['reference_field', 'function_type'])->get()]);

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    // public function store(Form $form, Request $request): \Illuminate\Http\JsonResponse
    public function store(Form $form, Request $request): \Illuminate\Http\JsonResponse
    {
        // Remove query params from request as a possible bug with slash routing implementation

        foreach (array_keys($request->query()) as $query) {
            $request->query->remove($query);
        }

        $request->validate([
            '*.label' => ['required', 'string'],
            '*.name' => ['required', 'string'],
            '*.required' => ['required', 'boolean'],
            '*.type' => ['required', 'exists:form_field_types,html']
        ]);

        try {
            foreach ($request->all() as $item) {
                $fieldType = FormFieldType::query()->where('html', $item['type'])->first();
                $field = new FormField;
                $field->name = $item['name'];
                $field->label = $item['label'];
                $field->required = $item['required'] ?? false;
                $field->user()->associate($request->user());
                $field->form_field_type()->associate($fieldType);
                $field->appendable = $item['appendable'];
                $field->editable = $item['editable'];
                $field->hideable = $item['hideable'];
                $field->save();
                $form->form_fields()->save($field);
                if (array_key_exists('options', $item)) {
                    foreach ($item['options'] as $option) {
                        $formOption = new FormFieldOption;
                        $formOption->label = $option;
                        $formOption->form_field()->associate($field);
                        $formOption->owner()->associate($request->user());
                        $formOption->save();
                    }
                }
            }

            return response()->json("Form fields have been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\FormField  $formField
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, FormField $formField)
    {
        //
    }

    public function optionUpdate($formFieldId, Request $request)
    {
        try {
            // Delete previous options with the given form_field_id
            FormFieldOption::where('form_field_id', $formFieldId)->delete();

            // Insert new options with the given form_field_id
            $newOptions = [];
            foreach ($request->options as $optionData) {
                $newOptions[] = [
                    'label' => $optionData,
                    'form_field_id' => $formFieldId,
                    'created_by' => $request->user()->id, // Access user ID directly
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            FormFieldOption::insert($newOptions);
            return response()->json("Form field options has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\FormField  $formField
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Form $form, FormField $formField): \Illuminate\Http\JsonResponse
    {
        try {
            $formField->reference_field()->delete();
            $formField->delete();
            return response()->json("Form field has been deleted.");
        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }
}