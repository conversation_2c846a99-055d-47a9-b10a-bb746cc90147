<?php
namespace App\Events;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use App\Models\Message;
class UnreadMessageCountUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $receiverId;
    public $totalUnread;
    public $conversationId;
    public function __construct($receiverId, $conversationId)
    {
        $this->receiverId = $receiverId;
        $this->conversationId = $conversationId;
        $this->totalUnread = Message::where('conversation_id', $this->conversationId)
            ->where('user_id', '!=', $this->receiverId) 
            ->where('status', '!=', 'read')
            ->count();
    }
    public function broadcastOn()
    {
        return new Channel('unread_message_count.' . $this->receiverId);
    }
    public function broadcastWith()
    {
        return [
            'conversation_id' => $this->conversationId, 
            'total_unread' => $this->totalUnread,
        ];
    }
}