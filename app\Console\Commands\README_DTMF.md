# DTMF Capture Command

This command captures DTMF input from inbound calls before they connect and stores it in the `dtmf_input` table.

## Features

- Captures DTMF input before the call connects
- Plays recordings from the `dtmf_settings` table to prompt for input
- Removes previous DTMF records for the same phone number
- Stores the DTMF input in the database with the call's unique ID

## Usage in Asterisk Dialplan

To use this command in your Asterisk dialplan, add the following to your extensions.conf file:

```
[from-external]
exten => _X.,1,NoOp(Incoming call from ${CALLERID(num)})
exten => _X.,n,AGI(agi:async,StoreDtmf)
exten => _X.,n,Goto(inbound-routing,${EXTEN},1)

[inbound-routing]
; Your regular call routing logic here
; You can access the stored DTMF input via the API
```

## Starting the Command

Run the following command to start the DTMF capture service:

```bash
php artisan store:dtmf
```

It's recommended to run this as a service using Supervisor to ensure it stays running.

### Supervisor Configuration Example

Create a file at `/etc/supervisor/conf.d/dtmf-capture.conf` with the following content:

```ini
[program:dtmf-capture]
command=php /path/to/your/app/artisan store:dtmf
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/log/dtmf-capture.log
```

Then run:

```bash
supervisorctl reread
supervisorctl update
supervisorctl start dtmf-capture
```

## Accessing DTMF Input

You can access the stored DTMF input via the API endpoint:

```
POST /api/get-dtmf-input
{
    "unique_id": "call_unique_id"
}
```

This will return the value associated with the DTMF input from the `dtmf_settings` table.
