<?php

namespace App\Http\Controllers;

use App\Models\OutboundWorkcode;
use Illuminate\Http\Request;

class OutboundWorkcodeController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $workcode = new OutboundWorkcode;
            $workcode->channel_id = $request->channel;
            $workcode->workcode = $request->code;
            $workcode->save();

            return response()->json("Workcode has been saved.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
