<?php

namespace App\Http\Controllers;

use App\Models\WorkCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class   WorkCodeController extends Controller
{

    public function __construct() {

        $this->middleware('permission:create_workcodes', ['only' => ['store']]);
        $this->middleware('permission:update_workcodes', ['only' => ['update']]);
        $this->middleware('permission:delete_workcodes', ['only' => ['destroy']]);
        $this->middleware('permission:read_workcodes', ['only' => ['index']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        return response()->json(WorkCode::query()->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {

        $request->validate([
            'name' => ['required', 'string', 'unique:work_codes,name']
        ]);

        try {
            $workcode = WorkCode::query()->create($request->all());
            return response()->json("Workcode {$workcode->name} has been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }

    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param WorkCode $workCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, WorkCode $workCode): \Illuminate\Http\JsonResponse
    {
//        $this->authorize('update', $workCode);

        $request->validate([
            'name' => ['required', 'string', Rule::unique('work_codes', 'name')->ignoreModel($workCode)]
        ]);

        try {
            $status = $workCode->update($request->all());
            if($status) {
                return response()->json("Workcode {$workCode->name} has been updated.");
            } else {
                return response()->json("Workcode {$workCode->name} update has been failed.", 400);
            }
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param WorkCode $workCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(WorkCode $workCode): \Illuminate\Http\JsonResponse
    {
        try {
            $workCode->delete();
            return response()->json("Workcode {$workCode->name} has been deleted.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }
}
