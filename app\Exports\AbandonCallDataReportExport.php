<?php

namespace App\Exports;

use DateTime;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AbandonCallDataReportExport implements  WithHeadings, WithMapping,FromArray
{
    /**
     * @return \Illuminate\Support\Collection
     */


    function __construct(array $data)
    {
        $this->data= $data;
    }
    public function array(): array
    {
        return $this->data;
    }


    public function map($data): array
    {
        return [
            $data['date'],
            $data['time'],
            $data['src'],
            $data['position'],
            $data['origposition'],
            $data['waittime']
        ];
    }

    public function headings(): array
    {
        return [
            'date',
            'time',
            'src',
            'position',
            'origposition',
            'waittime'
        ];
    }
}
