<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class PersonController extends Controller
{

    public function __construct() {

        $this->middleware('permission:create_audio', ['only' => ['create']]);
        $this->middleware('permission:update_audio', ['only' => ['update']]);
        $this->middleware('permission:delete_audio', ['only' => ['destroy']]);
        $this->middleware('permission:read_audio', ['only' => ['index']]);
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response()->json([ 'data' => User::all()]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validate = Validator::make($request->all() , $this->validationStore());
            if($validate->fails())
                return response()->json(['error' , $validate->messages()],422);

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'type' => $request->type,
                'authUserName' => $request->authUserName,
                'authPassword' => Hash::make($request->authPassword),
                'password' => Hash::make($request->password)
            ]);

            if ($user->id > 0)
                return response()->json(['data' => $user, "success" => '1']);
            else
                return response()->json(['data' => "data not inserted", "success" => '0']);
        }
        catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()] , 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        try {
            $validate = Validator::make($request->all() , $this->validation());
            if($validate->fails())
                return response()->json(['error' , $validate->messages()] , 422);
            $data = User::query()->where('id' , $request->id)->update($request->all());
            if($data == 1)
                return response()->json(['success' => '1']);
            else
                return response()->json(['data' => "data is not updated" , 'success' => '0']);
        }
        catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()] , 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = User::find($id);
        $status = $data->delete();
        if($status)
            return response()->json(['status' => '1']);
        return response()->json(['message' => 'data is not deleted' , 'status' => '0']);
    }
    private function validation()
    {
        return [
            "id" => "required",
            "name" => "required",
            "email" => "required",
            "type" => "required"
        ];
    }
    private function validationStore()
    {
        return [
            "name" => "required",
            "type" => "required",
            "email" => ["required", 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['string' , 'confirmed' , 'required'],
        ];
    }
}
