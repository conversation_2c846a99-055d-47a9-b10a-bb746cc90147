<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laratrust\Traits\HasRolesAndPermissions;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use Lara<PERSON>\Jetstream\HasProfilePhoto;
use Laravel\Sanctum\HasApiTokens;
use Laratrust\Traits\LaratrustUserTrait;
// use Laratrust\Traits\LaratrustUserTrait;

use Laratrust\Contracts\LaratrustUser;

class User extends Authenticatable implements LaratrustUser
{
    // use LaratrustUserTrait;
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;
    use HasRolesAndPermissions;
    // use LaratrustUserTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'username',
        'type',
        'auth_username',
        'auth_password',
        'bitrix_id',
        'view_callback'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */

    public function getSmsModuleAttribute(): string
    {

        $module = \App\Models\ModuleAccessibility::where('module_name', 'sms')->first();

        return $module && $module->agent_accessibility === 1 ? 'yes' : 'no';
    }

    public function getEmailModuleAttribute(): string
    {

        $module = \App\Models\ModuleAccessibility::where('module_name', 'email')->first();

        return $module && $module->agent_accessibility === 1 ? 'yes' : 'no';
    }

    protected $appends = [
        'profile_photo_url',
        'sms_module',
        'email_module',
    ];

    public function queues(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Queue::class);
    }

    public function campaigns(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Campaign::class);
    }

    public function endpoint(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Endpoint::class, 'id', 'auth_username');
    }

    public function forms(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Form::class, 'created_by', 'id');
    }

    public function form_fields(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(FormField::class, 'created_by', 'id');
    }

    public function form_field_options(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(FormFieldOption::class, 'created_by');
    }
    public function schedule_calls(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ScheduleCalls::class);
    }

    public function getCreatedAtAttribute($value): string
    {
        return Carbon::parse($value)->diffForHumans();
    }

    public function getUpdatedAtAttribute($value): string
    {
        return Carbon::parse($value)->diffForHumans();
    }

    public static function boot()
    {
        parent::boot();

        static::roleAdded(function ($user, $role, $team = null) {
        });
        static::roleSynced(function ($user, $changes, $team = null) {
        });
    }
}