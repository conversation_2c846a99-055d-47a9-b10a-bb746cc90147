<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agentless_campaign_numbers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('campaign_id');
            $table->string('number');
            $table->string('dtmf')->default('');
            $table->string('status')->default('NO ANSWER');
            $table->string('uniqueId')->nullable();
            $table->timestamp('start_time')->nullable();
            $table->timestamp('answer_time')->nullable();
            $table->timestamp('end_time')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agentless_campaign_numbers');
    }
};
