<?php

namespace App\Jobs;

use App\Models\AgentlessCampaign;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use PAMI\Client\Exception\ClientException;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\OriginateAction;

class DispatchNumberJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private string $number;
    private array $server;
    private int $campaign;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($number, $server, $campaign)
    {
        $this->number = $number;
        $this->server = $server;
        $this->campaign = $campaign;
    }

    public function tags()
    {
        return ["server:{$this->server['id']}", "campaign:$this->campaign", "number:$this->number"];
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws ClientException
     */
    public function handle()
    {
        $this->dispatchCall($this->number, $this->server);
    }

    /**
     * @throws ClientException
     */
    private function dispatchCall($number, $server)
    {
        $campaign = AgentlessCampaign::query()->find($this->campaign);
        $playback = "sonus/$campaign->recording_name";
        $client = new ClientImpl($server);
        $action = new OriginateAction("PJSIP/0$number@{$server['trunk']}");
        $action->setCallerId($server['caller_id']);
        //$action->setAccount($this->campaign);
        $action->setAccount($campaign->unique_id);
        $action->setContext($server['context']);
        $action->setExtension($number);
        $action->setPriority(1);
        $action->setVariable('REC', $playback);
        $action->setTimeout($server['timeout'] ?? 45000);
        $action->setAsync(true);
        $action->setVariable('CDR(recordingfile)', $number);
        $client->open();
        return $client->send($action);
    }
}
