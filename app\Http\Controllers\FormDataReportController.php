<?php

namespace App\Http\Controllers;

use App\Models\FormData;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FormDataReportController extends Controller
{
    public function getFormDataReport(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $formData = FormData::query()->where('created_at', '>=', Carbon::now()->startOfDay())->where('created_at', '<=', Carbon::now()->endOfDay())->leftJoin('cdr', 'call_id', 'cdr.uniqueid')->orderBy('created_at', 'desc')->paginate($request->perPage ?? 10);
            //$data = FormData::query()->orderBy('created_at', 'desc')->get();
            return response()->json($formData);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getFormDataReportFiltered(Request $request): \Illuminate\Http\JsonResponse
    {

        try{
            $filterData = FormData::query()->leftJoin('cdr', 'call_id', '=', 'cdr.uniqueid');
            // TODO: Changing query in order to resolve float value bug
            // $filterData= FormData::query();
            if(isset($request->time)){
                $filterData->whereDate('created_at', '>=', $request->time[0])->whereDate('created_at', '<=', $request->time[1]);
            }

            if($request->has('time') && is_array($request->time)){
                $start = Carbon::parse($request->time[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->time[1])->timezone("Asia/Karachi");
                $filterData->whereBetween('created_at',  [$start->toDateTime(), $end->toDateTime()]);
            }

            if($request->has('destination')) {
                $filterData->where('cdr.dst', 'like', "%{$request->destination}%");
            }

            if($request->has('source')) {
                $filterData->where('cdr.src', 'like', "%{$request->source}%");
            }

            if($request->has('data')) {
                $filterData->where('data', 'like', "%{$request->data}%");
            }

            $data = $filterData->orderBy('created_at', 'desc')->paginate($request->perPage ?? 10);
            return response()->json($data);
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function downloadFormDataReport(Request $request): \Illuminate\Http\JsonResponse
    {
        //$filterData= FormData::query()->join('cdr', 'call_id', '=', 'cdr.uniqueid');
        // TODO: Changing query in order to resolve float value bug
        $filterData = FormData::query()->leftJoin('cdr', 'call_id', '=', 'cdr.uniqueid');

        if(isset($request->time)){
            $filterData->whereDate('created_at', '>=', $request->time[0])->whereDate('created_at', '<=', $request->time[1]);
        }

        if($request->has('destination')) {
            $filterData->where('cdr.dst', 'like', "%{$request->destination}%");
        }

        if($request->has('source')) {
            $filterData->where('cdr.src', 'like', "%{$request->source}%");
        }

        if($request->has('data')) {
            $filterData->where('data', 'like', "%{$request->data}%");
        }

        $data = $filterData->orderBy('created_at', 'desc')->get();
        return response()->json($data);
    }
}
