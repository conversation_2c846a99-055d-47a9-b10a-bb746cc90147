<?php
namespace App\Http\Controllers;
use App\Events\MessageSent;
use App\Events\UnreadMessageCountUpdated;
use App\Events\TypingEvent;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
class ConversationController extends Controller
{
    public function test()
    {
        $auth_id = 2;
            $new_data = Conversation::query()->with(['lastMsg'])
                ->where('user_one', $auth_id)
                ->orWhere('user_two', $auth_id)
                ->get()
                ->map(function ($row) use ($auth_id) {
                    if ($row['user_one'] === $auth_id) {
                        $row['username'] = User::query()->find($row['user_two'])->username;
                    } else
                        $row['username'] = User::query()->find($row['user_one'])->username;
                    return $row;
                });
            // $new_data = Conversation::query()->with(['lastMsg', 'from:id,username', 'to:id,username'])
            // ->where('user_one', $auth_id)
            // ->orWhere('user_two', $auth_id)
            // ->get()
            // ->map(function ($row) use ($auth_id) {
            //     $row['username'] = $row->user_one === $auth_id ? $row->userTwo->username : $row->userOne->username;
            //     return $row;
            // });
        //    $user_name = User::find($new_data->)
        $username = $new_data[0]->lastMsg;
        // if(!$username){
            dd($new_data->toArray());
            
        // }else{
        //     return response()->json([
        //         'message' => 'Data not exists!',
        //     ]);
        // }
        
    }
    // public function conversationList()
    // {
    //     $auth_id = Auth::id();
    //     // $data = Conversation::query()->with(['lastMsg'])
    //     //     ->where('user_one', $auth_id)
    //     //     ->orWhere('user_two', $auth_id)
    //     //     ->get()
    //     //     ->map(function ($row) use ($auth_id) {
    //     //         if ($row['user_one'] === $auth_id) {
    //     //             $row['username'] = User::query()->find($row['user_two'])->username;
    //     //         } else
    //     //             $row['username'] = User::query()->find($row['user_one'])->username;
    //     //         return $row;
    //     //     });
    //     // return response()->json($data);
    //     $data = Conversation::query()->with(['lastMsg'])
    //         ->where('user_one', $auth_id)
    //         ->orWhere('user_two', $auth_id)
    //         ->get()
    //         ->map(function ($row) use ($auth_id) {
    //             $row['username'] = ($row['user_one'] === $auth_id)
    //                 ? User::query()->find($row['user_two'])->username
    //                 : User::query()->find($row['user_one'])->username;
    //             // Ensure only second user is sent
    //             $row['user_id'] = ($row['user_one'] === $auth_id)
    //                 ? $row['user_two']
    //                 : $row['user_one'];
    //             return $row;
    //         });
    //     return response()->json($data);
    // }
    public function conversationList()
    {
        $auth_id = Auth::id();
        $data = Conversation::query()->with(['lastMsg'])
            ->where('user_one', $auth_id)
            ->orWhere('user_two', $auth_id)
            ->get()
            ->map(function ($row) use ($auth_id) {
                $row['username'] = ($row['user_one'] === $auth_id)
                    ? User::query()->find($row['user_two'])->username
                    : User::query()->find($row['user_one'])->username;
                // Ensure only second user is sent
                $row['user_id'] = ($row['user_one'] === $auth_id)
                    ? $row['user_two']
                    : $row['user_one'];
                $row['unread_count'] = Message::where('conversation_id', $row->id)
                    ->where('status', 'sent')  // Only count sent messages
                    ->where('user_id', '!=', $auth_id) // Only messages from other user
                    ->count();
                return $row;
            });
        return response()->json($data);
    }
    public function getAllUsers()
    {
        $users = User::where('id', '!=', auth()->user()->id)
        ->where('type', '!=', 'Normal')->get();
        return response()->json($users);
    }
    // public function getAllUsers()
    // {
    //     $userId = auth()->id();
    //     // Fetch all users excluding the authenticated user
    //     $users = User::where('id', '!=', $userId)->get();
    //     // Get unread counts for each user
    //     $unreadCounts = Message::whereHas('conversation', function ($query) use ($userId) {
    //             $query->where('user_one', $userId)->orWhere('user_two', $userId);
    //         })
    //         ->where('user_id', '!=', $userId) // Exclude the current user
    //         ->where('status', '!=', 'read')  // Only unread messages
    //         ->groupBy('user_id')
    //         ->selectRaw('user_id, COUNT(*) as count')
    //         ->pluck('count', 'user_id');
    //     // Add unread message count to each user
    //     $usersWithUnreadCount = $users->map(function ($user) use ($unreadCounts) {
    //         $user->unread_count = $unreadCounts->get($user->id, 0); // Default to 0 if no unread messages
    //         return $user;
    //     });
    //     return response()->json($usersWithUnreadCount);
    // }
    public function getConnectedUsers()
    {
        $authUserId = auth()->id();
        $users = User::whereIn('id', function ($query) use ($authUserId) {
            $query->select('user_one')
                ->from('conversations')
                ->where('user_two', $authUserId)
                ->union(
                    DB::table('conversations')
                    ->select('user_two')
                    ->where('user_one', $authUserId)
                );
        })
        // ->where('type', '!=', 'Normal')
        ->get();

        // dd($users);
        
        return response()->json($users);
    }
    public function sendMessage(Request $request)
    {
        
        $request->validate([
            'to' => 'required',
            'ipAddress' => 'required',
            'message' => 'required|string|min:1|max:1000'
        ]);
        $auth_id = Auth::id();
        $msg = new Message();
        $msg->conversation_id = $request->to;
        $msg->user_id = $auth_id;
        $msg->message = $request->message;
        $msg->ip_address = $request->ipAddress;
        $msg->status = 'sent';
        $msg->save();

        $receiverId = $msg->conversation->user_one == $msg->user_id
        ? $msg->conversation->user_two
        : $msg->conversation->user_one;
        
        broadcast(new MessageSent($msg))->toOthers(); 
        broadcast(new UnreadMessageCountUpdated($receiverId, $msg->conversation_id));
        return response()->json([
            'message' => 'message sent!',
            'conversation_id' => $msg->conversation->id
        ]);
        // broadcast(new MessageSent($msg));
        // return response()->json(['message' => 'message sent!', 'conversation_id' => $msg->conversation->id]);
    }
    public function createRoom($id)
    {
        // $auth_id = Auth::id();
        // $room = Conversation::query()
        //     ->where(function ($q) use ($auth_id, $id) {
        //         $q->where(function ($query) use ($id, $auth_id) {
        //             $query->where('user_one', $auth_id)
        //                 ->where('user_two', $id);
        //         })
        //             ->orWhere(function ($query) use ($id, $auth_id) {
        //                 $query->where('user_one', $id)
        //                     ->where('user_two', $auth_id);
        //             });
        //     })->select('id')->get();
        // if ($room->count() < 1) {
        //     $conv = new Conversation();
        //     $conv->user_one = \auth()->user()->id;
        //     $conv->user_two = $id;
        //     $conv->save();
        //     return response($conv->id);
        // }
        $auth_id = Auth::id();
        if ($auth_id == $id) {
            return response()->json([
                'error' => 'You cannot create a conversation with yourself'
            ], 400);
        }
    
        $room = Conversation::where(function ($q) use ($auth_id, $id) {
            $q->where('user_one', $auth_id)->where('user_two', $id)
              ->orWhere('user_one', $id)->where('user_two', $auth_id);
        })->first();
        // if (!$room) {
        //     $conv = new Conversation();
        //     $conv->user_one = $auth_id;
        //     $conv->user_two = $id;
        //     $conv->save();
    
        //     return response()->json(['conversation_id' => $conv->id]);
        // }
    
        // return response()->json(['conversation_id' => $room->id]);
        if (!$room) {
            $conv = new Conversation();
            $conv->user_one = $auth_id;
            $conv->user_two = $id;
            $conv->save();
        
            return response()->json([
                'message' => 'Conversation created successfully!',
                'conversation_id' => $conv->id
            ]);
        }
        
        return response()->json([
            'message' => 'Conversation already exists!',
            'conversation_id' => $room->id
        ]);
    }
    public function chatRoom($id)
    {
        // $auth_id = Auth::id();
        // $findRoom = Conversation::query()
        //     ->where(function ($q) use ($auth_id, $id) {
        //         $q->where(function ($query) use ($id, $auth_id) {
        //             $query->where('user_one', $auth_id)
        //                 ->where('user_two', $id);
        //         })
        //             ->orWhere(function ($query) use ($id, $auth_id) {
        //                 $query->where('user_one', $id)
        //                     ->where('user_two', $auth_id);
        //             });
        //     })->select('id')->get();
        // Message::query()->where('conversation_id', $id)->update([
        //     'status' => 2
        // ]);
        // $message = DB::table('messages')->where('conversation_id', $id)->get();
        // return response()->json($message);
        $auth_id = Auth::id();
        $findRoom = Conversation::where(function ($query) use ($auth_id, $id) {
            $query->where('id', $id)
                ->where(function ($q) use ($auth_id) {
                    $q->where('user_one', $auth_id)
                      ->orWhere('user_two', $auth_id);
                });
        })->first();
    
        if (!$findRoom) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }
    
        Message::where('conversation_id', $id)->update(['status' => 2]);
    
        $messages = Message::where('conversation_id', $id)->get();
    
        return response()->json($messages);
    }
    public function deleteMessage($id)
    {
        Conversation::find($id)->delete();
        return response()->json(['message' => 'message delete!']);
    }
    public function deleteConversation(Request $request)
    {
        //Conversation::where('to',$request->to)->orWhere('to',Auth::id())->orWhere('from',$request->to)->orWhere('from',Auth::id())->delete();
        // return response()->json(['message' => $request->to]);
        $auth_id = Auth::id(); 
        $conv_id = $request->conversation_id; 
        $conversation = Conversation::find($conv_id);
        if (!$conversation || 
            !($conversation->user_one == $auth_id || $conversation->user_two == $auth_id)) {
            return response()->json(['error' => 'Conversation not found or user is not part of this conversation'], 404);
        }
        Message::where('conversation_id', $conv_id)->delete();
        $conversation->delete();
        return response()->json(['message' => 'Conversation and associated messages deleted successfully']);
        
    }
   
    
    public function getUnreadMessageCount()
    {
        $userId = auth()->id();
        $unreadCounts = Message::whereHas('conversation', function ($query) use ($userId) {
                $query->where('user_one', $userId)->orWhere('user_two', $userId);
            })
            ->where('user_id', '!=', $userId) 
            ->where('status', 'sent') 
            ->selectRaw('conversation_id, COUNT(*) as count')
            ->groupBy('conversation_id')
            ->get()
            ->pluck('count', 'conversation_id'); 
           
            $totalUnread = $unreadCounts->sum();
        return response()->json([
            'total_unread' => $totalUnread,
            'unread_counts' => $unreadCounts
        ]);
    }
    // public function markAsRead(Request $request)
    // {
    //     $conversationId = $request->conversation_id;
    //     $userId = auth()->id();
    //     if (!$conversationId) {
    //         return response()->json([
    //             'success' => false,
    //             'message' => 'Conversation ID is required'
    //         ], 400);
    //     }
    //     Message::where('conversation_id', $conversationId)
    //         ->where('status', '!=', 'read')
    //         ->where('user_id', '!=', $userId)
    //         ->update(['status' => 'read']);
    //     $unreadCount = Message::where('conversation_id', $conversationId)
    //         ->where('status', '!=', 'read')
    //         ->where('user_id', '!=', $userId)
    //         ->count();
    //     return response()->json([
    //         'success' => true,
    //         'message' => 'Messages marked as read',
    //         'unread_count' => $unreadCount
    //     ]);
    // }
    public function markAsRead(Request $request)
    {

        $conversationId = $request->conversation_id;

        $userId = auth()->id();
        if (!$conversationId) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation ID is required'
            ], 400);
        }

        $conversation = Conversation::find($conversationId);

        if (!$conversation) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation not found'
            ], 404);
        }
       
        Message::where('conversation_id', $conversationId)
            ->where('status', '!=', 'read')
            ->where('user_id', '!=', $userId) 
            ->update(['status' => 'read']);

        $unreadCount = Message::where('conversation_id', $conversationId)
            ->where('status', '!=', 'read')
            ->count();

        return response()->json([
            'success' => true,
            'message' => 'Messages marked as read',
            'unread_count' => $unreadCount
        ]);
    }
}