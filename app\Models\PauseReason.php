<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class PauseReason extends Model
{
    use HasFactory;
    protected $fillable = ['name'];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->diffForHumans();
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->diffForHumans();
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($pauseReason) {
            Cache::forget('pause_reasons');
        });

        static::deleted(function ($pauseReason) {
            Cache::forget('pause_reasons');
        });
    }
}
