<?php

namespace App\Exports;

use App\Models\Cdr;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CdrExport implements FromQuery, WithHeadings, WithMapping
{
    protected Carbon $start;
    protected Carbon $end;
    protected bool $hasRemarks = false;

    public function __construct(?Carbon $start = null, ?Carbon $end = null)
    {
        $this->start = ($start ?? Carbon::now())->copy()->startOfDay();
        $this->end   = ($end   ?? Carbon::now())->copy()->endOfDay();

        // Decide once, up front, so headings() can use it
        $this->hasRemarks = DB::table('cdr')
            ->leftJoin('remarks', 'remarks.unique_id', '=', 'cdr.uniqueid')
            ->whereBetween('cdr.start', [$this->start, $this->end])
            ->whereNotNull('remarks.remark')
            ->exists();
    }

    public function query()
    {
        $queueLog = DB::table('queue_log')->select(
            'callid', 'agent',
            DB::raw("MAX(CASE WHEN Event = 'WORKCODE' THEN data1 END) as workcode")
        )
        ->where('agent', '!=', 'NONE')
        ->whereIn('queue_log.Event', ['WORKCODE','ENTERQUEUE'])
        ->whereBetween('time', [$this->start, $this->end])
        ->groupBy('callid','agent');

        return Cdr::query()
            ->select([
                'users.name',
                'uniqueid',
                DB::raw("(CASE WHEN accountcode = 'Queue' AND disposition != 'ANSWERED' THEN NULL ELSE work_codes.name END) as workcode"),
                'recordingfile','accountcode','src','dst','channel','dstchannel',
                'disposition','duration','transcription','start','end',
                'remarks.remark',
            ])
            ->leftJoin('users', function ($join) {
                $join->on('cdr.channel', 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                     ->orOn('cdr.dstchannel', 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
            })
            ->leftJoinSub($queueLog, 'q', function ($join) {
                $join->on('q.callid', '=', 'cdr.uniqueid')->whereColumn('q.agent', 'users.name');
            })
            ->leftJoin('work_codes', 'work_codes.id', '=', 'q.workcode')
            ->leftJoin('remarks', 'remarks.unique_id', '=', 'cdr.uniqueid')
            ->where('lastapp', '!=', 'AGI')
            ->whereBetween('start', [$this->start, $this->end])
            ->orderByDesc('start');
    }

    public function map($data): array
    {
        $row = [
            $data->name,
            $data->uniqueid,
            $data->recordingfile,
            $data->accountcode,
            $data->src,
            $data->dst,
            $data->channel,
            $data->dstchannel,
            $data->disposition,
            $data->duration,
            $data->transcription,
            $data->start,
            $data->end,
        ];

        if ($this->hasRemarks) {
            $row[] = $data->remark ?? '';
        }

        return $row;
    }

    public function headings(): array
    {
        $headings = [
            'Name',
            'Unique ID',
            'Recording File Name',
            'Call Type',
            'Source',
            'Destination',
            'Channel',
            'Dst Channel',
            'Call Status',
            'Duration',
            'Transcription',
            'Start',
            'End',
        ];

        if ($this->hasRemarks) {
            $headings[] = 'Remark';
        }

        return $headings;
    }
}
