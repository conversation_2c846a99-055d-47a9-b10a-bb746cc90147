<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormData extends Model
{
    use HasFactory;
    protected $fillable = ['call_id', 'data','form_id', 'phone_no'];

    public function getCreatedAtAttribute($value): string
    {
        return Carbon::parse($value)->timezone('Asia/Karachi')->toDayDateTimeString();
    }

    public function getDataAttribute($value)
    {
        return json_decode($value, true) ?? '';
    }

}
