<?php

namespace App\Models;

use App\Events\AgentlessCampaignUpdate;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AgentlessCampaign extends Model
{
    use HasFactory , HasUuids;


    protected $fillable = ['name', 'file_path', 'date_from', 'date_to', 'time_from', 'time_to', 'days', 'user_id', 'unique_id', 'recording_name', 'stage','status'];
    protected $appends = [ 'terminated'];
    //'running',


    public function campaignNumbers(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(AgentlessCampaignNumber::class,'campaign_id');
    }
    
    // if i need i will uncomment it
    // public function prepaidSetting()
    // {
    //     return $this->belongsTo(PrepaidSetting::class,'prepaid');
    // }

    public function getRunningAttribute()
    {
        return AgentlessCampaignLog::query()->where('campaign_id', $this->id)->orderBy('created_at', 'desc')->first()->log ?? 'idle';
    }

    public function getTerminatedAttribute()
    {
        return (bool) Signal::query()->where('campaign_id', $this->id)->where('signal', 'terminate')->orderBy('created_at', 'desc')->first();
    }

    public function uniqueIds()
    {
        return ['unique_id'];
    }
// if socket will be apply
    protected static function boot()
    {
        parent::boot();

        static::created(function () {
            broadcast(new AgentlessCampaignUpdate(AgentlessCampaign::orderBy('created_at' , 'desc')->get()));
        });

        static::updated(function () {
            broadcast(new AgentlessCampaignUpdate(AgentlessCampaign::orderBy('created_at' , 'desc')->get()));
        });

        static::deleted(function () {
            broadcast(new AgentlessCampaignUpdate(AgentlessCampaign::orderBy('created_at' , 'desc')->get()));
        });
    }

}
