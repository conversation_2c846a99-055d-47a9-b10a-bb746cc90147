## notes for bitrix integration

This branch will enable bitrix integration with contact+ on event base means there is no input required
from users, it will create lead of every new call in bitrix and append record in already created lead
and recording file.
we have to register these two commands in supervisor
custom-services:start  // for voicemail,service rating and custom-rating dependent on bitrix lead 
start:bitrix-integration // for capturing events and performing lead creation actions on them

## Service Rating feature

This feature will enable user to record rating of their service from customer.

### Installation

Create directory named `service_rating` in the folder `/var/lib/asterisk/sounds/en` and set permissions as follows:

    mkdir /var/lib/asterisk/sounds/en/service_rating
    chown -R www-data:www-data /var/lib/asterisk/sounds/en/service_rating

Add this following lines in extensions.conf to enable this module. Otherwise, this module will not work.

    // cd /etc/asterisk/extensions.conf

    [asyncagi_rating]
    exten => _X.,1,NoOp(Starting async agi on ${EXTEN})
    same => n,AGI(agi:async)
    same => n,Hangup()

Then, reload asterisk

    asterisk -rx "core reload"

Run command to install packages:

    composer install

Each of the repository (`contactplus-api-backend`, `contactplus-user-frontend`, `contactplus-admin-frontend`) has corresponding branch such as `feature/service_rating`

After that, run `db:seed` to seed initial settings to seeder:

    php artisan db:seed --class=ServiceRatingSettingSeeder

## VoiceMail Feature

This feature will enable users to add working timings in their call center. Only calls in those timings shall pass to queue. Calls that are coming after office hours shall be transferred to voicemail module where customers can record their message. Users can listen to voicemail in admin panel.

### Installation

Create directory named `voicemail` in `/var/lib/asterisk/sounds/en` if not created. The file will be stored as `voicemail/{file_name}` in the directory.

## Running

The command to run voicemail is `php artisan voicemail:start`. The command should be run through supervisor as it must be running all the time. 

