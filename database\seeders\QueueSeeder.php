<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class QueueSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('queues')->upsert(['name' => '100', 'musiconhold' => 'default', 'context'=> 'default', 'ringinuse' => 'no', 'monitor_format' => 'wav', 'wrapuptime'=> '10', 'monitor_type'=> 'MixMonitor', 'servicelevel'=> '30', 'strategy'=> 'rrmemory'], 'name');
    }
}
