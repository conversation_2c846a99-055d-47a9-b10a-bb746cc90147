<?php

namespace App\Imports;

use App\Models\FormData;
use App\Models\FormField;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use DB;
use Illuminate\Validation\Rule;
use Carbon\Carbon;


class FormDataImport implements ToModel,WithHeadingRow,WithValidation
{
   
    public function __construct($form_id)
    {
        $this->form_id = $form_id;
    }
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        
        $form_headings = FormField::with('form_field_type')->where('form_id' , $this->form_id)->get();
        $data = [];
        foreach ($form_headings as $key => $value) {
            $name = strtolower($value->name);
            if($value->appendable)
            {   if($value->form_field_type->html == 'date') {
                    // $data[$name] = ($row[$name] == null) ? [] : 
                    // [Carbon::parse($row[$name])->format('Y-m-d')];
                    // [\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row[$name])->format('Y-m-d')];

                } else {
                    $data[$name] =($row[$name] == null) ? [] :[$row[$name]];
                }
                
                
            }else{
                // if($name == 'phone_number'){
                //     $number = strval($row[$name]);
                //     if($number[0] == '0'){
                //         $array = explode('0', $number, 2);
                //         $phoneNumber = $array[1];
                //     }elseif($number[0] == '9' && $number[1] == '2' && $number[2] == '0'){
                //         $array = explode('920', $number, 2);
                //         $phoneNumber = $array[1];
                //     }
                //     elseif($number[0] == '9' && $number[1] == '2'){
                //         $array = explode('92', $number, 2);
                //         $phoneNumber = $array[1];
                        
                //     }else{
                //         $phoneNumber = $number;
                //     }
                //     $data[$name] = $phoneNumber;
                // }
                if($value->form_field_type->html == 'date'){
                    //dd($row[$name]);
                    $data[$name] = !empty($row[$name]) ? \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row[$name])->format('Y-m-d') : '' ;
                    //dd($data[$name]);
                }
                else{
                    $data[$name] = $row[$name];
                }
                
            }
           
        }

        $data_json = json_encode($data);
        
        $record = FormData::create([
            'phone_no' => $row['phone_number'],
            'form_id' => $this->form_id,
            'data' => $data_json
        ]);

    
    }

    public function prepareForValidation($data, $index)
    {
       
            //$number = strval($data['phone_number']);
            $number = (string) $data['phone_number'];
            if($number){
                if($number[0] == '0'){
                    $array = explode('0', $number, 2);
                    $data['phone_number'] = $array[1];
                }elseif($number[0] == '9' && $number[1] == '2' && $number[2] == '0'){
                    $array = explode('920', $number, 2);
                    $data['phone_number'] = $array[1];
                }
                elseif($number[0] == '9' && $number[1] == '2'){
                    $array = explode('92', $number, 2);
                    $data['phone_number'] = $array[1];
                    
                }else{
                    $data['phone_number'] = $number;
                }
            }  
        return $data;
    }
    public function rules(): array
    {
        return[
            //'phone_number' => ['unique:form_data,phone_no'], 
            'phone_number' => Rule::unique('form_data', 'phone_no')->where(function ($query) { $query->where('form_id', $this->form_id); })
            
        ];
    }
    public function customValidationMessages(){

        return [
            'phone_number'=>"Number required must be unique",
        ];
    }
}
