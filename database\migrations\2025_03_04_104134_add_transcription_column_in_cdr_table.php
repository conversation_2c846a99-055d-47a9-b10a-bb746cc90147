<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cdr', function (Blueprint $table) {
            $table->text('transcription')->nullable()->after('recordingfile');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cdr', function (Blueprint $table) {
            $table->dropColumn('transcription');
        });
    }
};
