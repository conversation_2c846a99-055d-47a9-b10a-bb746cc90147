<?php

namespace App\Console\Commands;

use App\Models\PauseReason;
use App\Models\QueueLog;
use App\Models\SystemSetting;
use App\Models\User;
use App\Models\WorkCode;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Event\QueueMemberEvent;
use PAMI\Message\Action\QueueSummaryAction;
use PAMI\Message\Action\CoreShowChannelsAction;
use PAMI\Message\Event\QueueParamsEvent;
use PAMI\Message\Event\QueueSummaryEvent;
use PAMI\Message\Event\CoreShowChannelEvent;

use App\Events\{
    DashboardOutbond,
    DashboardOutbondAgent,
    DashboardAgentQueue,
    DashboardDataQueue
};

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class PollingData extends Command
{
    protected $signature = 'pollingdata:start';
    protected $description = 'Get real-time data for socket';
    protected $users = [];

    public function __construct()
    {
        parent::__construct();
    }
    protected function getOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    public function handle()
    {
        $this->info("OPTIMIZED POLLING DATA RUNNING");
        $lastChangeCheck = 0;
        $consecutiveErrors = 0;

        while (true) {
            try {
                $this->users = $this->getLoggedInUsers();

                if (empty($this->users)) {
                    sleep(5);
                    continue;
                }

                $now = time();
                if ($now - $lastChangeCheck >= 5) {
                    $newChanges = DB::table('cdr_changes')
                        ->where('changed_at', '>=', Carbon::now()->subSeconds(30))
                        ->exists();

                    if ($newChanges) {
                        $this->info("DISPATCH START");
                        $this->broadcastOutboundData();
                        $this->info("DISPATCH COMPLETE");
                    }
                    $lastChangeCheck = $now;
                }

                $this->broadcastQueueData();
                $consecutiveErrors = 0;
                sleep($this->getPollingInterval());
            } catch (\Exception $e) {
                $consecutiveErrors++;
                Log::error("Polling data error: " . $e->getMessage());

                if ($consecutiveErrors > 3) {
                    $this->error('Too many consecutive errors, sleeping for 30 seconds...');
                    sleep(30);
                    $consecutiveErrors = 0;
                }
            }
        }
    }

    protected function getPollingInterval()
    {
        $userCount = count($this->users);
        if ($userCount === 0) return 5;
        if ($userCount < 3) return 2;
        return 1;
    }

    protected function broadcastOutboundData()
    {
        $outboundData = Cache::remember('outbound_stats', 60, fn() => $this->getStats());
        $outboundAgentData = Cache::remember('outbound_agent_stats', 60, fn() => $this->getAgentStats());

        foreach ($this->users as $user) {
            $userId = $user->id;
            $queue = cache("queue_user_{$userId}", 100);

            broadcast(new DashboardOutbond(['user_id' => $userId, 'data' => $outboundData, 'queue' => $queue]));
            broadcast(new DashboardOutbondAgent(['user_id' => $userId, 'data' => $outboundAgentData, 'queue' => $queue]));
        }
    }

    protected function broadcastQueueData()
    {
        foreach ($this->users as $user) {
            $userId = $user->id;
            $queue = cache("queue_user_{$userId}", 100);

            $agentQueueWiseData = Cache::remember("agent_queue_data_{$queue}", 30, fn() => $this->get_agents_data($queue));
            broadcast(new DashboardAgentQueue(['user_id' => $userId, 'data' => $agentQueueWiseData, 'queue' => $queue]));

            $agentDashboardQueueWiseData = Cache::remember("dashboard_queue_data_{$queue}", 30, fn() => $this->get_dashboard_data($queue));
            broadcast(new DashboardDataQueue(['user_id' => $userId, 'data' => $agentDashboardQueueWiseData, 'queue' => $queue]));
        }
    }

    public function getStats()
    {
        try {
            $query1 = DB::select("SELECT COUNT(*) as 'total_dialed_calls', SUM(IF(disposition = 'ANSWERED', 1, 0)) as 'total_answered_calls', Round(SUM(IF(disposition = 'ANSWERED', billsec, 0)) / 60, 2) as 'total_talk_time', ROUND((SUM(IF(disposition = 'ANSWERED', billsec, 0))/60)/COUNT(*), 2) as 'avg_talk_time', Round(SUM(duration) / 60 , 2) as 'total_dialed_duration', CONCAT(ROUND(SUM(IF(disposition = 'ANSWERED', 1, 0))/COUNT(*) * 100, 2), '%') as 'answered_rate', SEC_TO_TIME(ROUND(SUM(duration) / COUNT(*))) as 'avg_call_duration' FROM `cdr` WHERE accountcode = 'Outbound' AND date(`start`) = CURRENT_DATE");
            $query2 = DB::select("SELECT Round(COUNT(*)/(SELECT COUNT(*) from ps_endpoints), 2) as 'avg_dialed_per_agent', Round(SUM(IF(disposition = 'ANSWERED', 1, 0))/(SELECT COUNT(*) from ps_endpoints),2) as 'avg_answered_per_agent' FROM `cdr` join ps_endpoints as ps on cdr.channel like CONCAT('%', ps.id, '%') WHERE date(`start`) = CURRENT_DATE");
            $data = array_merge((array) $query1[0], (array) $query2[0]);

            return $data;
        } catch (\Exception $exception) {
            $this->error($exception->getMessage());
            return [];
        }
    }

    public function getAgentStats()
    {
        try {
            $query = DB::select("SELECT ps.id, us.name, count(*) as total_call, SUM(IF(disposition = 'ANSWERED', 1, 0)) as 'total_answered_calls', SEC_TO_TIME(SUM(duration)) as 'total_dialed_duration', SEC_TO_TIME(SUM(billsec)) as 'total_answered_duration', MAX(start) as 'last_dialed_call' FROM `cdr` join ps_endpoints as ps on cdr.channel like CONCAT('%PJSIP/', ps.id, '%') inner join users as us on ps.id = us.auth_username WHERE date(`start`) = CURRENT_DATE GROUP BY ps.id ORDER BY start desc");

            return $query;
        } catch (\Exception $exception) {
            $this->error($exception->getMessage());
            return [];
        }
    }

    public function get_agents_data($queue)
    {
        $client = new ClientImpl($this->getOptions());
        $action = new QueueStatusAction($queue);
        try {
            $client->open();
            $response = $client->send($action);
            $client->close();
            $events = [];
            $oKey = 0;
            foreach ($response->getEvents() as $event) {
                if ($event instanceof QueueMemberEvent) {
                    $events[$oKey] = $event->getKeys();
                    $events[$oKey]['agentId'] = explode("/", $event->getKey('stateinterface'))[1];
                    $events[$oKey]['lastcall'] = $event->getKey('lastcall') !== "0" ? $this->parse_time($event->getKey('lastcall')) : "N/A";
                    $events[$oKey]['lastpause'] = $event->getKey('lastpause') !== "0" ? $this->parse_time($event->getKey('lastpause')) : "N/A";
                    $events[$oKey]['status'] = $this->parse_agent_state((int) $event->getKey('status'));

                    $channelData = Cache::remember(
                        "channel_data_" . $event->getKey('stateinterface'),
                        30,
                        fn() => $this->get_data($event->getKey('stateinterface'))
                    );
                    $events[$oKey]['connected'] = $channelData["connectedlinenum"];
                    $events[$oKey]['application'] = $channelData["application"];
                    $oKey++;
                }
            }
            return $events;
        } catch (\Exception $e) {
            Log::error("Agent data error: " . $e->getMessage());
            return [];
        }
    }

    private function parse_time($data)
    {
        return Carbon::createFromTimestamp($data)->format('d-m-Y h:i:s A');
    }

    private function parse_agent_state(int $state)
    {
        switch ($state) {
            default:
                return $state;
            case 0:
                return 'DEVICE_UNKNOWN';
            case 1:
                return 'DEVICE_NOT_INUSE';
            case 2:
                return 'DEVICE_INUSE';
            case 3:
                return 'DEVICE_BUSY';
            case 4:
                return 'DEVICE_INVALID';
            case 5:
                return 'DEVICE_UNAVAILABLE';
            case 6:
                return 'DEVICE_RINGING';
            case 7:
                return 'DEVICE_RINGINUSE';
            case 8:
                return 'DEVICE_ONHOLD';
        }
    }

    private function get_data(string $interface)
    {
        try {
            $client = new ClientImpl($this->getOptions());
            $action = new CoreShowChannelsAction();
            $client->open();
            $response = $client->send($action);
            $client->close();

            $data = ['application' => null, 'connectedlinenum' => null];

            foreach ($response->getEvents() as $event) {
                if ($event instanceof CoreShowChannelEvent && Str::contains($event->getKey('channel'), $interface)) {
                    $data['application'] = $event->getKey('application');
                    $data['connectedlinenum'] = $event->getKey('connectedlinenum');
                    break;
                }
            }
            return $data;
        } catch (\Exception $e) {
            Log::error("Channel data error: " . $e->getMessage());
            return ['application' => null, 'connectedlinenum' => null];
        }
    }

    public function get_dashboard_data($queue)
    {
        $client = new ClientImpl($this->getOptions());
        $action = new QueueStatusAction($queue);
        $action2 = new QueueSummaryAction($queue);
        try {
            $client->open();
            $response = $client->send($action);
            $response2 = $client->send($action2);
            $client->close();
            $events = $response->getEvents();
            $events2 = $response2->getEvents();
            $members = 0;
            $paused = 0;
            $busy = 0;
            $idle = 0;
            $data = [];

            $date = Carbon::now();
            // $waitTime = QueueLog::query()
            //     ->from('queue_log')
            //     ->whereIn('EVENT', ['CONNECT'])
            //     ->where('queuename', '=', $queue)
            //     ->whereDate('time', '>=', $date->startOfDay())
            //     ->whereDate('time', '<=', $date->endOfDay())
            //     ->select(DB::raw("SUM(data1) as waittime"))
            //     ->value('waittime');

            foreach ($events as $key => $event) {
                if ($event instanceof QueueParamsEvent) {
                    $data['queue_params'] = $event->getKeys();
                    $data['queue_params']['totalcalls'] = (int) $event->getKey('completed') + (int) $event->getKey('abandoned');
                    $data['queue_params']['holdtime'] = gmdate("H:i:s", $event->getKey('holdtime'));
                    $data['queue_params']['talktime'] = gmdate("H:i:s", $event->getKey('talktime'));

                    $totalCalls = $data['queue_params']['totalcalls'];
                    $totalTalkTime = $event->getKey('talktime');
                    $totalHoldTime = $event->getKey('holdtime');

                    $data['queue_params']['avgHandlingTime'] = $totalCalls > 0 ? gmdate("H:i:s", ($totalTalkTime + $totalHoldTime) / $totalCalls) : '00:00:00';
                } elseif ($event instanceof QueueMemberEvent) {

                    $members++;

                    if ($event->getKey('paused') == "1") {
                        $paused++;
                    } elseif ($event->getKey('incall') == "1") {
                        $busy++;
                    } else {
                        $idle++;
                    }

                    $data['agents'][$key] = $event->getKeys();
                    $data['agents'][$key]['lastcall'] = $this->parse_time($event->getKey('lastcall'));
                    $data['agents'][$key]['lastpause'] = $this->parse_time($event->getKey('lastpause'));
                    $data['agents'][$key]['status'] = $this->parse_agent_state($event->getKey('status'));
                }
            }

            foreach ($events2 as $event2) {
                if ($event2 instanceof QueueSummaryEvent) {
                    $data['queue_params2'] = $event2->getKeys();
                    $data['queue_params2']['longestholdtime'] = gmdate("H:i:s", $event2->getKey('longestholdtime'));
                    $data['queue_params2']['total'] = $members;
                    $data['queue_params2']['paused'] = $paused;
                    $data['queue_params2']['idle'] = $idle;
                    $data['queue_params2']['busy'] = $busy;
                }
            }

            if (isset($data['queue_params'])) {
                $exitWithKeyCount = DB::table('queue_log')
                    ->where('queuename', $queue)
                    ->where('event', 'EXITWITHKEY')
                    ->whereBetween('time', [$date->copy()->startOfDay(), $date->copy()->endOfDay()])
                    ->count();

                $data['queue_params']['exitwithkey'] = $exitWithKeyCount;

                $data['queue_params']['abandoned'] = (int)$data['queue_params']['abandoned'] + $exitWithKeyCount;

                // keep totalcalls consistent: completed + (abandoned + exitwithkey)
                $data['queue_params']['totalcalls'] = (int)$data['queue_params']['completed'] + (int)$data['queue_params']['abandoned'];
            }

            return $data;
        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }

    protected function getLoggedInUsers()
    {
        $userIds = array_keys(Cache::get('logged_in_users', []));
        return User::whereIn('id', $userIds)->get();
        //return User::whereIn('id', $userIds)->whereDoesntHave('roles', function ($query) { $query->whereIn('name', ['agent', 'supervisor']); })->get();
    }
}
