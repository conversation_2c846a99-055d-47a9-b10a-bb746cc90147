<?php

namespace App\Console\Commands;

use App\Models\SystemSetting;
use App\Models\VoicemailSetting;
use Carbon\Carbon;
use Illuminate\Console\Command;
use PAGI\Exception\ChannelDownException;
use PA<PERSON>\AsyncAgi\AsyncClientImpl;
use PA<PERSON>\Client\Impl\ClientImpl;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\EventMessage;

class VoiceMail extends Command
{
    public string $recording = 'voicemail/for_voicemail';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'voicemail:start';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to start voicemail service daemon';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    protected function getAMIOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    private function checkTimings(): bool
    {
        $settings = VoicemailSetting::query()->first();
        $now = Carbon::now();

        if(!$settings->status)
            return false;

        // Step1 - compute special days
        if(in_array($now->format('Y-m-d'), $settings->specificDates)) {
            if($settings->specificDateStart && $settings->specificDateEnd && $now->format('H:i:s') >= $settings->specificDateStart && $now->format('H:i:s') <= $settings->specificDateEnd)
                return true;
        }

        // Step2 - compute week
        if(in_array($now->dayName, $settings->except))
            return true;
        elseif(in_array($now->dayName, $settings->weeks)) {
            // Step3 - compute timings
            $start = Carbon::parse($settings->start);
            $end = Carbon::parse($settings->end);
            if($now->between($start, $end))
                return true;
        }
        return false;
    }

    /**
     * Execute the console command.
     *
     * @return int
     * @throws \PAMI\Client\Exception\ClientException
     */
    public function handle(): int
    {
        $date = Carbon::now();
        $this->info("[{$date->toDateTimeString()}] INFO: Starting invoke script...");
        $client = new ClientImpl($this->getAMIOptions());
        $client->registerEventListener(function (EventMessage $event) use ($client, $date) {
            $agiClient = new AsyncClientImpl([
                'pamiClient' => $client,
                'asyncAgiEvent' => $event
            ]);
            $var = $agiClient->getChannelVariables();
            $arg = $var->getArgument(1);
            $callerId = $var->getCallerId();
            $uniqueId = $var->getUniqueId();
            $setting = VoicemailSetting::with('voicemailFile')->first();
            if($arg === 'voicemail' && $this->checkTimings()) {
                $logger = $agiClient->getAsteriskLogger();
                //$logger->notice('Starting sync execution of call...');
                $fileName = "voicemail-$callerId-{$date->format('Ymd-His')}-$uniqueId.wav";
                $fileLoc = "{$date->format('Y')}/{$date->format('m')}/{$date->format('d')}/$fileName";
                $filePath = "/var/spool/asterisk/recording/$fileLoc";
                $this->recording = $setting->voicemailFile->fileloc;
                try {
                    $agiClient->answer();
                    $agiClient->streamFile($setting->voicemailFile->fileloc);
                    //$result = $agiClient->record("/var/spool/asterisk/recording/voicemail-200", "wav", '#');
                    $result = $agiClient->exec("Record", ["$filePath", '0', '0', 'k']);
                    if($result->getResult()) {
                        $voicemail = new \App\Models\Voicemail();
                        $voicemail->filePath = $filePath;
                        $voicemail->fileName = $fileName;
                        $voicemail->fileLoc = $fileLoc;
                        $voicemail->uniqueid = $uniqueId;
                        $voicemail->number = $callerId;
                        $voicemail->save();
                    }
                    $agiClient->hangup(true);
                } catch (ChannelDownException $channelDownException) {
                    $this->info("[{$date->toDateTimeString()}] ERROR: Can't hangup channel, probably user has already hung up: $callerId");
                } catch (\Exception $exception) {
                    $this->info("[{$date->toDateTimeString()}] ERROR: {$exception->getMessage()}");
                }
                $this->info("[{$date->toDateTimeString()}] INFO: Ending invoke script...");
            } else
                $agiClient->asyncBreak();
        }, function ($event) use ($client) {
            return $event instanceof AsyncAGIStartEvent;
        });

        $client->open();

        while (true) {
            $client->process();
        }
    }
}
