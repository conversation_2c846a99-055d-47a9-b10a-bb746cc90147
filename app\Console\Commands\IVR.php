<?php

namespace App\Console\Commands;

use App\Models\IVRSetting;
use Illuminate\Console\Command;
use App\Models\SystemSetting;
use App\Models\VoicemailSetting;
use Carbon\Carbon;
use PAGI\Exception\ChannelDownException;
use PAMI\AsyncAgi\AsyncClientImpl;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\EventMessage;

class IVR extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ivr:start';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to start IVR';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    protected function getAMIOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    private function checkTimings(): bool
    {
        $settings = IVRSetting::query()->first();
        $now = Carbon::now();
        if(!$settings->status)
            return false;
         // Step 1 - compute special days (specific dates)
        if(in_array($now->toDateString(),$settings->specificDates)) {
        // Check if current time is between the specific start and end times
                if($settings->specificDateStart && $settings->specificDateEnd &&
                $now->format('H:i:s') >= $settings->specificDateStart &&
                $now->format('H:i:s') <= $settings->specificDateEnd) {
                return true; // If within specific date, no need to check further
         }
           return false;
        }
       // Step2 - compute week
        if(in_array($now->dayName, $settings->except))
            return true;
        elseif(in_array($now->dayName, $settings->weeks)) {
            // Step3 - compute timings

            $start = Carbon::parse($settings->start);
            $end = Carbon::parse($settings->end);
            if($now->between($start, $end))
                return true;
        }
        return false;
    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = Carbon::now();
        $this->info("[{$date->toDateTimeString()}] INFO: Starting invoke script...");
        $client = new ClientImpl($this->getAMIOptions());
        $client->registerEventListener(function (EventMessage $event) use ($client, $date) {
            $agiClient = new AsyncClientImpl([
                'pamiClient' => $client,
                'asyncAgiEvent' => $event
            ]);
            $var = $agiClient->getChannelVariables();
            $arg = $var->getArgument(1);
            $callerId = $var->getCallerId();
            $uniqueId = $var->getUniqueId();
            $setting = IVRSetting::with('ivrFile')->first();
            if($arg === 'ivr' && $this->checkTimings()) {
                $logger = $agiClient->getAsteriskLogger();

                try {
                    $agiClient->answer();
                    $agiClient->streamFile($setting->ivrFile->fileloc);
                    $agiClient->exec('hangup');
                } catch (ChannelDownException $channelDownException) {
                    $this->info("[{$date->toDateTimeString()}] ERROR: Can't hangup channel, probably user has already hung up: $callerId");
                } catch (\Exception $exception) {
                    $this->info("[{$date->toDateTimeString()}] ERROR: {$exception->getMessage()}");
                }
                $this->info("[{$date->toDateTimeString()}] INFO: Ending invoke script...");
            } else
                $agiClient->asyncBreak();
        }, function ($event) use ($client) {
            return $event instanceof AsyncAGIStartEvent;
        });

        $client->open();

        while (true) {
            $client->process();
        }
    }
}
