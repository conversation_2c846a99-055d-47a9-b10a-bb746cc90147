<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// class CreateCdrTable extends Migration
return new class extends Migration

{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cdr', function (Blueprint $table) {
            $table->string('accountcode', '255')->default(' ');
            $table->string('src', '255')->default(' ');
            $table->string('dst', '255')->default(' ');
            $table->string('dcontext', '255')->default(' ');
            $table->string('clid', '255')->default(' ');
            $table->string('channel', '255')->default(' ');
            $table->string('dstchannel', '255')->default(' ');
            $table->string('lastapp', '255')->default(' ');
            $table->string('lastdata', '255')->default(' ');
            $table->dateTime('start')->nullable();
            $table->dateTime('answer')->nullable();
            $table->dateTime('end')->nullable();
            $table->bigInteger('duration')->nullable();
            $table->bigInteger('billsec')->nullable();
            $table->string('disposition', '255')->default(' ');
            $table->string('amaflags', '255')->default(' ');
            $table->string('userfield', '255')->default(' ');
            $table->string('uniqueid', '255')->default(' ');
            $table->string('linkedid', '255')->default(' ');
            $table->string('peeraccount', '255')->default(' ');
            $table->bigInteger('sequence')->nullable();
            $table->string('recordingfile', '255')->default(' ');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cdr');
    }
};
