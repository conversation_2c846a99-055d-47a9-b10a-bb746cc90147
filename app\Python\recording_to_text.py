import os
import time
import pymysql
import noisereduce as nr
import numpy as np
from dotenv import load_dotenv
from io import BytesIO
from datetime import datetime
from elevenlabs.client import ElevenLabs
from pydub import AudioSegment
from pydub.utils import mediainfo

load_dotenv()
client = ElevenLabs(api_key=os.getenv("ELEVENLABS_API_KEY"))

def get_db_conn():
    return pymysql.connect(
        host=os.getenv("DB_HOST"),
        user=os.getenv("DB_USERNAME"),
        password=os.getenv("DB_PASSWORD"),
        database=os.getenv("DB_DATABASE"),
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

def log(msg):
    with open("/home/<USER>/storage/logs/transcriptions.log", "a") as f:
        f.write(f"{datetime.now()}: {msg}\n")

def get_audio_duration(file_path):
    try:
        audio_info = mediainfo(file_path)
        duration = audio_info.get('duration', '0')
        return float(duration)
    except Exception as e:
        log(f"Invalid duration found for file: {file_path} | Error: {e}")
        return 0.0

def denoise_audio(input_path):
    try:
        sound = AudioSegment.from_file(input_path)
        samples = np.array(sound.get_array_of_samples())
        reduced_noise = nr.reduce_noise(y=samples, sr=sound.frame_rate)
        clean_sound = AudioSegment(
            reduced_noise.tobytes(),
            frame_rate=sound.frame_rate,
            sample_width=sound.sample_width,
            channels=sound.channels
        )
        clean_path = input_path.replace(".wav", "_clean.wav")
        clean_sound.export(clean_path, format="wav")
        return clean_path
    except Exception as e:
        log(f"Noise reduction failed for {input_path}: {e}")
        return input_path  # fallback to original

def format_transcription(transcription):
    formatted_lines = []
    current_speaker = None
    current_text = ""

    for word in transcription.words:
        if word.type != 'word':
            continue

        speaker_id = word.speaker_id or "unknown"
        speaker_label = f"Speaker {int(speaker_id.split('_')[-1]) + 1}" if speaker_id.startswith("speaker_") else "Speaker Unknown"

        if speaker_id != current_speaker:
            if current_text:
                formatted_lines.append(f"{current_speaker_label}: {current_text.strip()}")
            current_speaker = speaker_id
            current_speaker_label = speaker_label
            current_text = word.text
        else:
            current_text += f" {word.text}"

    if current_text:
        formatted_lines.append(f"{current_speaker_label}: {current_text.strip()}")

    return "\n".join(formatted_lines)

# Main processing loop
while True:
    try:
        conn = get_db_conn()
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM transcription_jobs WHERE processed = 0 LIMIT 1")
            job = cursor.fetchone()

            if not job:
                time.sleep(1)
                continue

            audio_path = job["audio_path"]
            uniqueid = job["uniqueid"]

            if not os.path.exists(audio_path):
                log(f"Audio file not found: {audio_path}")
                cursor.execute("UPDATE transcription_jobs SET processed = 1 WHERE id = %s", (job["id"],))
                conn.commit()
                continue

            audio_duration = get_audio_duration(audio_path)
            if audio_duration < 1.0:
                log(f"Audio too short: {audio_path} ({audio_duration} sec)")
                cursor.execute("UPDATE transcription_jobs SET processed = 1 WHERE id = %s", (job["id"],))
                conn.commit()
                continue

            cursor.execute("SELECT transcription FROM cdr WHERE uniqueid = %s", (uniqueid,))
            cdr_entry = cursor.fetchone()
            if cdr_entry and cdr_entry["transcription"]:
                log(f"Transcription exists for uniqueid: {uniqueid}")
                cursor.execute("UPDATE transcription_jobs SET processed = 1 WHERE id = %s", (job["id"],))
                conn.commit()
                continue

            # Denoise audio
            cleaned_audio_path = denoise_audio(audio_path)
            with open(cleaned_audio_path, "rb") as f:
                audio_data = BytesIO(f.read())

            transcription = client.speech_to_text.convert(
                file=audio_data,
                model_id="scribe_v1",
                tag_audio_events=True,
                language_code="eng",
                diarize=True,
            )

            formatted_transcription = format_transcription(transcription)

            cursor.execute(
                "UPDATE cdr SET transcription = %s WHERE uniqueid = %s",
                (formatted_transcription, uniqueid)
            )
            cursor.execute("UPDATE transcription_jobs SET processed = 1 WHERE id = %s", (job["id"],))
            conn.commit()
            log(f"Transcription saved for uniqueid: {uniqueid}")
    except Exception as e:
        log(f"Error: {str(e)}")
    finally:
        try:
            conn.close()
        except:
            pass
