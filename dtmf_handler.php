#!/usr/bin/php -q
<?php

require 'vendor/autoload.php';

use PAGI\Client\Impl\ClientImpl;
use PAGI\Logger\Impl\FileLogger;
use Illuminate\Database\Capsule\Manager as Capsule;

// Set up logging
$logger = new FileLogger('/var/log/asterisk/dtmf_handler.log');

// Set up database connection
$capsule = new Capsule;
$capsule->addConnection([
    'driver'    => 'mysql',
    'host'      => 'localhost',
    'database'  => 'laravel',
    'username'  => 'root',
    'password'  => 'root12',
    'charset'   => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix'    => '',
]);
$capsule->setAsGlobal();
$capsule->bootEloquent();

// Function to log to both file and Asterisk verbose
function logVerbose($client, $logger, $message, $level = 3)
{
    $logger->info($message);
    $client->verbose($message, $level);
}

// Initialize PAGI client
try {
    $client = new ClientImpl([]);
    logVerbose($client, $logger, 'DTMF Handler: AGI script started');

    // Answer the call
    $client->answer();
    logVerbose($client, $logger, 'DTMF Handler: Call answered');

    // Get call variables
    $uniqueId = $client->getVariable('UNIQUEID')->getValue();
    $callerId = $client->getVariable('CALLERID(num)')->getValue();
    logVerbose($client, $logger, "DTMF Handler: Call from $callerId with uniqueID: $uniqueId");

    // Fetch DTMF settings from database
    $dtmfSettings = Capsule::table('dtmf_settings')->get();
    logVerbose($client, $logger, 'DTMF Handler: Retrieved ' . count($dtmfSettings) . ' DTMF settings');

    // Play each DTMF option recording
    foreach ($dtmfSettings as $setting) {
        $recordingFile = pathinfo($setting->recording, PATHINFO_FILENAME);
        logVerbose($client, $logger, "DTMF Handler: Playing recording: dtmf/$recordingFile");

        $result = $client->streamFile("dtmf/$recordingFile");

        // If user pressed a key during playback, use that as input
        if ($result->getDigits() !== '') {
            $dtmf = $result->getDigits();
            logVerbose($client, $logger, "DTMF Handler: User pressed $dtmf during playback");
            break;
        }
    }

    // If no key was pressed during playback, wait for input
    if (!isset($dtmf)) {
        logVerbose($client, $logger, "DTMF Handler: Waiting for DTMF input...");
        $result = $client->getDigits(1, 5000, '#');
        $dtmf = $result->getDigits();
    }

    // Process DTMF input
    if (!empty($dtmf)) {
        logVerbose($client, $logger, "DTMF Handler: Received DTMF: $dtmf");

        // Save to database
        Capsule::table('dtmf_input')->insert([
            'unique_id' => $uniqueId,
            'dtmf' => $dtmf,
            'phone_number' => $callerId,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        logVerbose($client, $logger, "DTMF Handler: DTMF input saved to database");

        // Play confirmation message
        $client->streamFile('beep');
    } else {
        logVerbose($client, $logger, "DTMF Handler: No DTMF input received");
    }

    // Hangup
    $client->hangup();
    logVerbose($client, $logger, "DTMF Handler: Call completed");
} catch (\Exception $e) {
    if (isset($client)) {
        $client->verbose("DTMF Handler ERROR: " . $e->getMessage(), 1);
    }
    $logger->error("Error: " . $e->getMessage());
    if (isset($client)) {
        $client->hangup();
    }
}
