<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Exports\CdrExport;
use App\Exports\CdrFilterExport;
use App\Models\QueueLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Pagination\Paginator;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\AbandonCallDataReportExport;

class CallDetailRecordController extends Controller
{

    public function getCallDetailRecordsBkp(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            $cdr = Cdr::query()
                ->select(['users.name', 'work_codes.name as workcode', 'uniqueid', 'recordingfile', 'accountcode', 'src', 'dst', 'channel', 'dstchannel', 'disposition', 'duration', 'start', 'end', 'transcription'])
                ->leftJoin("users", function ($join) {
                    $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                        ->orOn("cdr.dstchannel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
                })
                ->leftJoin('queue_log', function ($join) {
                    $join->on('queue_log.callid', '=', 'cdr.uniqueid')
                        ->where('queue_log.Event', '=', 'Workcode');
                })
                ->leftJoin('work_codes', 'work_codes.id', '=', 'queue_log.data1')
                ->where('lastapp', '!=', 'AGI')
                ->whereDate('start', '>=', $date->startOfDay())
                ->whereDate('end', '<=', $date->endOfDay())
                ->orderBy('start', 'desc')->paginate($request->records ?? 15);
            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getCallDetailRecords(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now();
            $queueLog = DB::table('queue_log')->select(
                'callid',
                'agent',
                DB::raw("Max(CASE WHEN Event = 'WORKCODE' THEN data1 END) as workcode"),
            )
                ->where('agent', '!=', 'NONE')
                ->whereIn('queue_log.Event', ['WORKCODE', 'ENTERQUEUE'])
                ->whereDate('time', '>=', $date->startOfDay())
                ->whereDate('time', '<=', $date->endOfDay())
                ->groupBy('callid', 'agent');
            $cdr = Cdr::query()
                ->select([
                    'users.name',
                    DB::raw("(CASE WHEN accountcode = 'Queue' AND disposition != 'ANSWERED' THEN NULL ELSE work_codes.name END) as workcode"),
                    'uniqueid',
                    'recordingfile',
                    'accountcode',
                    'src',
                    'dst',
                    'channel',
                    'dstchannel',
                    'disposition',
                    'duration',
                    'start',
                    'end',
                    'transcription',
                    'remarks.remark'
                ])
                ->leftJoin("users", function ($join) {
                    $join->on("cdr.channel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                        ->orOn("cdr.dstchannel", "LIKE", DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
                })
                ->leftJoinSub($queueLog, 'q', function ($join) {
                    $join->on('q.callid', '=', 'cdr.uniqueid')
                        ->whereColumn('q.agent', 'users.name');
                })
                ->leftJoin('work_codes', 'work_codes.id', '=', 'q.workcode')
                ->leftJoin('remarks', 'remarks.unique_id', '=', 'cdr.uniqueid')
                ->where('lastapp', '!=', 'AGI')
                ->whereDate('start', '>=', $date->startOfDay())
                ->whereDate('end', '<=', $date->endOfDay())
                ->orderBy('start', 'desc')->paginate($request->records ?? 15);
            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }


    public function getCallDetailRecordsFilteredBkp(Request $request): \Illuminate\Http\JsonResponse
    {
        ini_set('memory_limit', '-1');

        try {
            $cdr = Cdr::query()
                ->select([
                    'users.name',
                    'work_codes.name as workcode',
                    'queue_log.queuename',
                    'cdr.uniqueid',
                    'cdr.recordingfile',
                    'cdr.accountcode',
                    'cdr.src',
                    'cdr.dst',
                    'cdr.channel',
                    'cdr.dstchannel',
                    'cdr.disposition',
                    'cdr.duration',
                    'cdr.start',
                    'cdr.end',
                    'cdr.transcription'
                ])
                ->leftJoin('users', function ($join) {
                    $join->on(DB::raw("cdr.channel"), 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                        ->orOn(DB::raw("cdr.dstchannel"), 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
                })
                ->leftJoin('queue_log', function ($join) {
                    $join->on('queue_log.callid', '=', 'cdr.uniqueid')
                        ->whereIn('queue_log.Event', ['Workcode', 'ENTERQUEUE']);
                })
                ->leftJoin('work_codes', 'work_codes.id', '=', 'queue_log.data1')
                ->where('cdr.lastapp', '!=', 'AGI')

                ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
                    $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                    $end = Carbon::parse($request->range[1])->timezone('Asia/Karachi');
                    $query->whereBetween('cdr.start', [$start->toDateTimeString(), $end->toDateTimeString()]);
                })


                ->when($request->filled('source'), function ($query) use ($request) {
                    $query->where('cdr.src', $request->source);
                })


                ->when($request->filled('destination'), function ($query) use ($request) {
                    $query->where('cdr.dst', $request->destination);
                })


                ->when($request->has('agents') && is_array($request->agents), function ($query) use ($request) {
                    $query->where(function ($q) use ($request) {
                        foreach ($request->agents as $agent) {
                            $q->orWhere('cdr.channel', 'like', "%PJSIP/{$agent}%")
                                ->orWhere('cdr.dstchannel', 'like', "%PJSIP/{$agent}%");
                        }
                    });
                })


                ->when($request->filled('call_status'), function ($query) use ($request) {
                    $query->where('cdr.disposition', 'like', "%{$request->call_status}%");
                })


                ->when($request->filled('accountcode'), function ($query) use ($request) {
                    $query->where('cdr.accountcode', $request->accountcode);
                })


                ->when($request->filled('queue'), function ($query) use ($request) {
                    $query->where('queue_log.queuename', $request->queue);
                })

                ->orderByDesc('cdr.start')
                ->paginate($request->input('records', 15));

            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }


    public function getCallDetailRecordsFiltered(Request $request): \Illuminate\Http\JsonResponse
    {
        ini_set('memory_limit', '-1');

        $queueLog = DB::table('queue_log')->select(
            'callid',
            'agent',
            DB::raw("MAX(CASE WHEN Event = 'Workcode' THEN data1 END) as workcode")
        )
            ->where('agent', '!=', 'NONE')
            ->whereIn('queue_log.Event', ['Workcode'])
            ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
                $start = Carbon::parse($request->range[0])->startOfDay();
                $end = Carbon::parse($request->range[1])->endOfDay();
                $query->whereBetween('queue_log.time', [$start, $end]);
            })
            ->groupBy('callid', 'agent');

        $queueEnter = DB::table('queue_log')->select(
            'callid',
            DB::raw("MAX(queuename) as queuename")
        )
            ->where('event', 'ENTERQUEUE')
            ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
                $start = Carbon::parse($request->range[0])->startOfDay();
                $end = Carbon::parse($request->range[1])->endOfDay();
                $query->whereBetween('queue_log.time', [$start, $end]);
            })
            ->groupBy('callid');

        try {
            $cdr = Cdr::query()
                ->select([
                    'users.name',
                    DB::raw("(CASE WHEN accountcode = 'Queue' AND disposition != 'ANSWERED' 
                          THEN NULL ELSE work_codes.name END) as workcode"),
                    'qe.queuename',
                    'cdr.uniqueid',
                    'cdr.recordingfile',
                    'cdr.accountcode',
                    'cdr.src',
                    'cdr.dst',
                    'cdr.channel',
                    'cdr.dstchannel',
                    'cdr.disposition',
                    'cdr.duration',
                    'cdr.start',
                    'cdr.end',
                    'cdr.transcription',
                    'remarks.remark'
                ])
                ->leftJoin('users', function ($join) {
                    $join->on(DB::raw("cdr.channel"), 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                        ->orOn(DB::raw("cdr.dstchannel"), 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
                })
                ->leftJoinSub($queueLog, 'q', function ($join) {
                    $join->on('q.callid', '=', 'cdr.uniqueid')
                        ->whereColumn('q.agent', 'users.name');
                })
                ->leftJoinSub($queueEnter, 'qe', function ($join) {
                    $join->on('qe.callid', '=', 'cdr.uniqueid');
                })
                ->leftJoin('work_codes', 'work_codes.id', '=', 'q.workcode')
                ->leftJoin('remarks', 'remarks.unique_id', '=', 'cdr.uniqueid')
                ->where('cdr.lastapp', '!=', 'AGI')

                ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
                    $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                    $end = Carbon::parse($request->range[1])->timezone('Asia/Karachi');
                    $query->whereBetween('cdr.start', [$start->toDateTimeString(), $end->toDateTimeString()]);
                })

                ->when($request->filled('source'), function ($query) use ($request) {
                    $query->where('cdr.src', $request->source);
                })

                ->when($request->filled('destination'), function ($query) use ($request) {
                    $query->where('cdr.dst', $request->destination);
                })

                ->when($request->has('agents') && is_array($request->agents), function ($query) use ($request) {
                    $query->where(function ($q) use ($request) {
                        foreach ($request->agents as $agent) {
                            $q->orWhere('cdr.channel', 'like', "%PJSIP/{$agent}%")
                                ->orWhere('cdr.dstchannel', 'like', "%PJSIP/{$agent}%");
                        }
                    });
                })

                ->when($request->filled('call_status'), function ($query) use ($request) {
                    $query->where('cdr.disposition', 'like', "%{$request->call_status}%");
                })

                ->when($request->filled('accountcode'), function ($query) use ($request) {
                    $query->where('cdr.accountcode', $request->accountcode);
                })

                ->when($request->filled('queue'), function ($query) use ($request) {
                    $query->where('qe.queuename', $request->queue);
                })


                ->orderByDesc('cdr.start')
                ->paginate($request->input('records', 15));

            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }
    public function paginate($items, $perPage, $page = null)
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $total = count($items);
        $currentpage = $page;
        $offset = ($currentpage * $perPage) - $perPage;
        $itemstoshow = array_slice($items, $offset, $perPage);
        return new LengthAwarePaginator($itemstoshow, $total, $perPage);
    }

    public function holdTimeReport(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $date = Carbon::now()->format('Y-m-d');
            if (isset($request->date)) {
                $date1 = Carbon::parse($request->date[0])->format('Y-m-d');
                $date2 = Carbon::parse($request->date[1])->format('Y-m-d');
                $q = " AND DATE(TIME) >= '{$date1}' AND DATE(TIME) <= '{$date2}'";
            } else {
                $q = " and date(time) = '{$date}'";
            }

            if (isset($request->queue) && !empty($request->queue)) {
                $queueName = trim($request->queue);
                $q .= " AND queuename LIKE '%{$queueName}%'";
            }

            if (isset($request->status))
                echo "status";
            return response()->json(DB::select("select queuename as queue, COUNT(CASE WHEN data1 > 0 and data1 <= 10 then 1 ELSE NULL END) as '0-10', COUNT(CASE when data1 > 10 and data1 <= 20 then 1 else NULL end) as '11-20', COUNT(CASE when data1 > 20 and data1 <= 30 then 1 else NULL end) as '21-30', COUNT(case when data1 > 30 and data1 <= 40 then 1 else null end) as '31-40', COUNT(CASE when data1 > 40 and data1 <= 50 then 1 else NULL end) as '41-50', COUNT(CASE when data1 > 50 and data1 <= 60 then 1 else NULL end) as '51-60', COUNT(CASE when data1 > 60 then 1 else NULL end) as '61 >', ROUND(AVG(data1), 2) as 'avg_wait_time', MAX(CONVERT(data1, UNSIGNED)) as 'longest_wait_time', COUNT(*) as 'total' from queue_log where Event = 'CONNECT' {$q} group by queuename"));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getCallHistory(Request $request): JsonResponse
    {
        try {
            if (isset($request->src)) {
                if ($request->src[0] === "0") {
                    $request->src = ltrim($request->src, $request->src[0]);
                }
                $cdr = Cdr::query()->where('src', 'LIKE', "%$request->src%")->orWhere('dst', 'LIKE', "%$request->src%")->limit(5)->orderBy('start', 'desc')->get()->map(function ($record) {
                    if ($record->accountcode === 'Queue') {
                        // It will be dstchannel
                        $user = User::query()->where('auth_username', $record->dstchannel)->first();
                        if ($user)
                            $record->agent = $user->name;
                        else
                            $record->agent = $record->dstchannel;
                    } else if ($record->accountcode === 'Outbound') {
                        // It will be channel
                        $user = User::query()->where('auth_username', $record->channel)->first();
                        if ($user)
                            $record->agent = $user->name;
                        else
                            $record->agent = $record->channel;
                    }
                    return $record;
                });
                return response()->json($cdr);
            } else
                return response()->json([]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function abandonCallDataBKP(Request $request): JsonResponse
    {
        try {
            $data = QueueLog::query()->from('queue_log as q')->join('cdr as c', function ($join) {
                $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
            })
                ->select(DB::raw('DATE(q.time) date, TIME(q.time) time, c.src, q.data1 as position, q.data2 as origposition, q.data3 as waittime'))
                ->distinct();

            if (isset($request->range) && is_array($request->range)) {
                $date1 = Carbon::parse($request->range[0])->format("Y-m-d");
                $date2 = Carbon::parse($request->range[1])->format("Y-m-d");
                $data->whereDate('q.time', '>=', $date1)->whereDate('q.time', '<=', $date2);
            }
            if (isset($request->dst) && $request->dst != null)
                $data->where('c.src', 'LIKE', "%{$request->dst}%");
            return response()->json($data->paginate($request->pageSize));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function abandonCallData(Request $request): JsonResponse
    {
        try {

            $data = QueueLog::query()
                ->from('queue_log as q')
                ->join('cdr as c', function ($join) {
                    $join->on('c.uniqueid', '=', 'q.callid')
                        ->where(function ($query) {
                            $query->where('q.EVENT', '=', 'ABANDON')
                                ->orWhere('q.EVENT', '=', 'EXITWITHKEY');
                        });
                })
                ->select(DB::raw('DATE(q.time) as date, TIME(q.time) as time'), 'c.src', 'q.queuename', 'q.data1 as position', 'q.data2 as origposition', 'q.data3 as waittime')
                ->distinct();
            if (isset($request->range) && is_array($request->range)) {
                $date1 = Carbon::parse($request->range[0])->format("Y-m-d");
                $date2 = Carbon::parse($request->range[1])->format("Y-m-d");
                $data->whereBetween('q.time', [$date1 . ' 00:00:00', $date2 . ' 23:59:59']);
            }

            if (!empty($request->dst)) {
                $data->where('c.src', 'LIKE', "%{$request->dst}%");
            }

            if (!empty($request->queue)) {
                $data->where('q.queuename', $request->queue);
            }

            return response()->json($data->paginate($request->pageSize));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }


    public function exportAbandonCallData(Request $request)
    {
        try {
            // Define the date range
            $date1 = Carbon::now()->startOfDay();
            $date2 = Carbon::now()->endOfDay();
            if ($request->has('range') && is_array($request->range)) {
                $date1 = Carbon::parse($request->range[0])->timezone('Asia/Karachi')->startOfDay();
                $date2 = Carbon::parse($request->range[1])->timezone('Asia/Karachi')->endOfDay();
            }
            if (!$request->filled('queue')) {
                return Excel::download(new AbandonCallDataReportExport([]), 'abandoned.xlsx');
            }


            $data = QueueLog::query()
                ->from('queue_log as q')
                ->select([
                    DB::raw('DATE(q.time) as date'),
                    DB::raw('TIME(q.time) as time'),
                    DB::raw('(SELECT c.src FROM cdr as c WHERE c.uniqueid = q.callid LIMIT 1) as src'),
                    'q.data1 as position',
                    'q.data2 as origposition',
                    'q.data3 as waittime'
                ])
                ->where(function ($query) {
                    $query->where('q.EVENT', '=', 'ABANDON')
                        ->orWhere('q.EVENT', '=', 'EXITWITHKEY');
                })
                ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2]);
            if ($request->filled('queue')) {
                $data->where('q.queuename', $request->queue);
            }

            // Process data in chunks to handle large datasets
            $chunkSize = 1000; // Adjust based on your memory and performance requirements
            $dataArray = [];
            $data->orderBy('q.time', 'asc')->chunk($chunkSize, function ($rows) use (&$dataArray) {
                foreach ($rows as $row) {
                    $dataArray[] = $row;
                }
            });

            return Excel::download(new AbandonCallDataReportExport($dataArray), 'abandoned.xlsx');

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function Last10AbandonCallData(Request $request): JsonResponse
    {
        try {
            $data = QueueLog::query()->from('queue_log as q')->join('cdr as c', function ($join) {
                $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
            })->select(DB::raw('DATE(q.time) date, TIME(q.time) time, c.src, q.data1 as position, q.data2 as origposition, q.data3 as waittime'));
            return response()->json($data->orderByDesc('time')->limit(10)->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function abandonCallDataHours(): JsonResponse
    {
        try {
            $data = QueueLog::query()->from('queue_log as q')->join('cdr as c', function ($join) {
                $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
            })->whereRaw('TIME BETWEEN DATE_SUB(NOW(), INTERVAL 3 HOUR) AND NOW()')->
                select(DB::raw('DATE(q.time) date, TIME(q.time) time, c.src, q.data1 as duration'))->get();
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function abandonCallDifferenceReport(Request $request): JsonResponse
    {
        $request->validate([
            "date" => ["required", "date"],
        ]);

        try {
            $queueFilter = "";
            if (isset($request->queue) && !empty($request->queue)) {
                $queueFilter = " AND queue_log.queuename = '{$request->queue}'";
            }

            $query = "SELECT
                a.src AS number,
                DATE_FORMAT(a.time, '%d %M %Y %r') AS abandon_time,
                b.accountcode AS CallDirection,
                DATE_FORMAT(b.start, '%d %M %Y %r') AS followupTime,
                TIME_FORMAT(TIMEDIFF(b.start, a.time), '%T') AS difference
            FROM
                (SELECT * FROM cdr
                    INNER JOIN queue_log
                    ON (queue_log.callid = cdr.uniqueid
                        AND queue_log.event IN ('ABANDON', 'EXITWITHKEY')
                        AND DATE(cdr.start) = '{$request->date}'
                        $queueFilter)
                    ORDER BY cdr.start DESC) AS a
            LEFT JOIN
                (SELECT * FROM cdr
                    WHERE (cdr.accountcode = 'Queue'
                        AND cdr.disposition = 'ANSWERED'
                        AND cdr.dstchannel IS NOT NULL
                        AND DATE(cdr.start) = '{$request->date}')
                    OR (cdr.accountcode = 'Outbound'
                        AND DATE(cdr.start) = '{$request->date}')
                    ORDER BY cdr.start DESC) AS b
            ON (b.src LIKE CONCAT('%', a.src, '%')
                AND a.time < b.start
                OR b.dst LIKE CONCAT('%', a.src, '%')
                AND a.time < b.start)
            GROUP BY a.uniqueid;";

            $data = DB::select($query);
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function cdrExport()
    {
        return Excel::download(new cdrExport(), 'CallDetailReport.csv');
    }


    public function cdrExportFilter_backup(Request $request)
    {
        $queueLog = DB::table('queue_log')->select('callid','agent',
            DB::raw("Max(CASE WHEN Event = 'WORKCODE' THEN data1 END) as workcode")
            )
            ->where('agent', '!=', 'NONE')
            ->whereIn('queue_log.Event', ['WORKCODE'])
                ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
                    $start = Carbon::parse($request->range[0])->startOfDay();
                    $end = Carbon::parse($request->range[1])->endOfDay();
                    $query->whereBetween('queue_log.time', [$start,$end]);
                })
            ->groupBy('callid','agent');

        $queueEnter = DB::table('queue_log')
                ->select('callid',DB::raw("MAX(queuename) as queuename"))
                ->where('event', 'ENTERQUEUE')
                                ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
                                    $start = Carbon::parse($request->range[0])->startOfDay();
                                    $end = Carbon::parse($request->range[1])->endOfDay();
                                    $query->whereBetween('queue_log.time', [$start,$end]);
                                })
                ->groupBy('callid');

        $cdr = Cdr::query()
            ->select([
                'users.name',
                DB::raw("(CASE WHEN accountcode = 'Queue' AND disposition != 'ANSWERED' THEN NULL ELSE work_codes.name END) as workcode"),
                'qe.queuename',
                'cdr.uniqueid',
                'cdr.recordingfile',
                'cdr.accountcode',
                'cdr.src',
                'cdr.dst',
                'cdr.channel',
                'cdr.dstchannel',
                'cdr.disposition',
                'cdr.duration',
                'cdr.start',
                'cdr.end',
                'cdr.transcription'
            ])
            ->leftJoin('users', function ($join) {
                $join->on(DB::raw("cdr.channel"), 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                    ->orOn(DB::raw("cdr.dstchannel"), 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
            })
            ->leftJoinSub($queueLog, 'q', function ($join) {
                $join->on('q.callid', '=', 'cdr.uniqueid')->whereColumn('q.agent', 'users.name');
            })
            ->leftJoinSub($queueEnter, 'qe', function ($join) {
                $join->on('qe.callid', '=', 'cdr.uniqueid');
            })
            ->leftJoin('work_codes', 'work_codes.id', '=', 'q.workcode')
            ->where('cdr.lastapp', '!=', 'AGI')
            ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end = Carbon::parse($request->range[1])->timezone('Asia/Karachi');
                $query->whereBetween('cdr.start', [$start->toDateTimeString(), $end->toDateTimeString()]);
            })
            ->when($request->filled('source'), function ($query) use ($request) {
                $query->where('cdr.src', $request->source);
            })
            ->when($request->filled('destination'), function ($query) use ($request) {
                $query->where('cdr.dst', $request->destination);
            })
            ->when($request->has('agents') && is_array($request->agents), function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    foreach ($request->agents as $agent) {
                        $q->orWhere('cdr.channel', 'like', "%PJSIP/{$agent}%")
                            ->orWhere('cdr.dstchannel', 'like', "%PJSIP/{$agent}%");
                    }
                });
            })
            ->when($request->filled('call_status'), function ($query) use ($request) {
                $query->where('cdr.disposition', 'like', "%{$request->call_status}%");
            })
            ->when($request->filled('accountcode'), function ($query) use ($request) {
                $query->where('cdr.accountcode', $request->accountcode);
            })
            ->when($request->filled('queue'), function ($query) use ($request) {
                $query->where('qe.queuename', $request->queue);
            })

            ->orderByDesc('cdr.start')->get();

        return Excel::download(new cdrFilterExport($cdr->toArray()), 'FilteredCallDetailReport.csv');
    }



    public function abandonCallReport(Request $request): JsonResponse
    {
        try {

            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            if (isset($request->range) && is_array($request->range)) {
                $date1 = Carbon::parse($request->range[0])->format("Y-m-d");
                $date2 = Carbon::parse($request->range[1])->format("Y-m-d");
            }

            $data = QueueLog::query()
                ->from('queue_log as q')
                // ->join('cdr as c', function ($join) {
                //     $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
                // })
                ->join('cdr as c', 'c.uniqueid', '=', 'q.callid')
                ->select(
                    DB::raw('DATE(q.time) as date'),
                    DB::raw('TIME(q.time) as time'),
                    'c.uniqueid',
                    'q.callid',
                    'c.src',
                    'c.dst',
                    'q.data3 as waittime',
                    'q.EVENT',
                    'q.status'
                )
                // ->whereDate('q.time', '>=', $date1)
                // ->whereDate('q.time', '<=', $date2)
                // ->where('q.status', '=', '0')
                // ->groupBy('c.uniqueid');
                ->where('q.EVENT', 'ABANDON')
                ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2])
                ->where('q.status', '0');

            return response()->json($data->paginate($request->pageSize));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function cdrExportFilter(Request $request)
    {
        $queueLog = DB::table('queue_log')->select('callid','agent',
            DB::raw("Max(CASE WHEN Event = 'WORKCODE' THEN data1 END) as workcode")
        )
        ->where('agent', '!=', 'NONE')
        ->whereIn('queue_log.Event', ['WORKCODE'])
        ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
            $start = Carbon::parse($request->range[0])->startOfDay();
            $end   = Carbon::parse($request->range[1])->endOfDay();
            $query->whereBetween('queue_log.time', [$start,$end]);
        })
        ->groupBy('callid','agent');

        $queueEnter = DB::table('queue_log')
            ->select('callid', DB::raw("MAX(queuename) as queuename"))
            ->where('event', 'ENTERQUEUE')
            ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
                $start = Carbon::parse($request->range[0])->startOfDay();
                $end   = Carbon::parse($request->range[1])->endOfDay();
                $query->whereBetween('queue_log.time', [$start,$end]);
            })
            ->groupBy('callid');

        $cdr = Cdr::query()
            ->select([
                'users.name',
                DB::raw("(CASE WHEN accountcode = 'Queue' AND disposition != 'ANSWERED' THEN NULL ELSE work_codes.name END) as workcode"),
                'qe.queuename',
                'cdr.uniqueid',
                'cdr.recordingfile',
                'cdr.accountcode',
                'cdr.src',
                'cdr.dst',
                'cdr.channel',
                'cdr.dstchannel',
                'cdr.disposition',
                'cdr.duration',
                'cdr.start',
                'cdr.end',
                'cdr.transcription',
                'remarks.remark as remark',
            ])
            ->leftJoin('users', function ($join) {
                $join->on(DB::raw("cdr.channel"), 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"))
                    ->orOn(DB::raw("cdr.dstchannel"), 'LIKE', DB::raw("CONCAT('%PJSIP/', users.auth_username, '%')"));
            })
            ->leftJoinSub($queueLog, 'q', function ($join) {
                $join->on('q.callid', '=', 'cdr.uniqueid')->whereColumn('q.agent', 'users.name');
            })
            ->leftJoinSub($queueEnter, 'qe', function ($join) {
                $join->on('qe.callid', '=', 'cdr.uniqueid');
            })
            ->leftJoin('work_codes', 'work_codes.id', '=', 'q.workcode')
            ->leftJoin('remarks', 'remarks.unique_id', '=', 'cdr.uniqueid')
            ->where('cdr.lastapp', '!=', 'AGI')
            ->when($request->has('range') && is_array($request->range), function ($query) use ($request) {
                $start = Carbon::parse($request->range[0])->timezone('Asia/Karachi');
                $end   = Carbon::parse($request->range[1])->timezone('Asia/Karachi');
                $query->whereBetween('cdr.start', [$start->toDateTimeString(), $end->toDateTimeString()]);
            })
            ->when($request->filled('source'), fn($q) => $q->where('cdr.src', $request->source))
            ->when($request->filled('destination'), fn($q) => $q->where('cdr.dst', $request->destination))
            ->when($request->has('agents') && is_array($request->agents), function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    foreach ($request->agents as $agent) {
                        $q->orWhere('cdr.channel', 'like', "%PJSIP/{$agent}%")
                        ->orWhere('cdr.dstchannel', 'like', "%PJSIP/{$agent}%");
                    }
                });
            })
            ->when($request->filled('call_status'), fn($q) => $q->where('cdr.disposition', 'like', "%{$request->call_status}%"))
            ->when($request->filled('accountcode'), fn($q) => $q->where('cdr.accountcode', $request->accountcode))
            ->when($request->filled('queue'), fn($q) => $q->where('qe.queuename', $request->queue))
            ->orderByDesc('cdr.start')
            ->get();

        return Excel::download(new CdrFilterExport($cdr->toArray()), 'FilteredCallDetailReport.csv');
    }


    public function updateStatus(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'callid' => 'required|string',
                'status' => 'required|boolean'
            ]);

            $callid = $validated['callid'];
            $status = $validated['status'];

            $affectedRows = DB::update("UPDATE queue_log SET status = ? WHERE callid = ? AND EVENT = 'ABANDON'", [$status, $callid]);

            if ($affectedRows === 0) {
                return response()->json(['error' => 'Record not found or not an ABANDON event'], 404);
            }

            if ($request->userid) {
                $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
                $date2 = Carbon::now()->endOfDay()->format("Y-m-d");
                $page = 1;
                $size = 10;

                $data = QueueLog::query()->from('queue_log as q')
                    // ->join('cdr as c', function ($join) {
                    //     $join->on('c.uniqueid', '=', 'q.callid')->where('q.EVENT', '=', 'ABANDON');
                    // })
                    ->join('cdr as c', 'c.uniqueid', '=', 'q.callid')
                    ->select(
                        DB::raw('DATE(q.time) as date'),
                        DB::raw('TIME(q.time) as time'),
                        'c.uniqueid',
                        'q.callid',
                        'c.src',
                        'c.dst',
                        'q.data3 as waittime',
                        'q.EVENT',
                        'q.status'
                    )
                    // ->whereDate('q.time', '>=', $date1)
                    // ->whereDate('q.time', '<=', $date2)
                    // ->where('q.status', '=', '0')
                    // ->groupBy('c.uniqueid');
                    ->where('q.EVENT', 'ABANDON')
                    ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2])
                    ->where('q.status', '0');


                $page = $request->current;
                $size = $request->pageSize;

                // Paginate the grouped records
                $paginatedData = $data->paginate($size, ['*'], 'page', $page);

                $response = [
                    'user_id' => $request->userid,
                    'currentPage' => $paginatedData->currentPage(),
                    'pageSize' => $paginatedData->perPage(),
                    'total' => $paginatedData->total(),
                    'data' => ['data' => $paginatedData->items()],
                ];

                // Log::info("success dispatch from controller: " . json_encode($response, JSON_PRETTY_PRINT));
                broadcast(new \App\Events\GetAbandonCallReport($response));
            }

            return response()->json(['message' => 'Status updated successfully']);

        } catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }
}
