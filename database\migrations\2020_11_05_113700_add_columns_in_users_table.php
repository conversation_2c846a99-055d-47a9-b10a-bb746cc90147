<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// class AddColumnsInUsersTable extends Migration
return new class extends Migration

{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('auth_username')->nullable()->after('password');
            $table->string('auth_password')->nullable()->after('password');
            $table->string('type')->default('Normal')->after('password');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('auth_username');
            $table->dropColumn('auth_password');
            $table->dropColumn('type');
        });
    }
};
