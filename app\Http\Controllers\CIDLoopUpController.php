<?php

namespace App\Http\Controllers;

use App\Models\CIDLoopUp;
use Illuminate\Http\Request;

class CIDLoopUpController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response()->json(CIDLoopUp::query()->get());
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'url' => ['required', 'url'],
            'key' => ['required', 'string'],
        ]);
        try{
            $cid = new CIDLoopUp();
            $cid->url = $request->url;
            $cid->key = $request->key;
            if(isset($request->required))
                $cid->required = $request->required;
            $cid->save();
            return response()->json("CID Loop-up has been created");
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\CIDLoopUp  $cIDLoopUp
     * @return \Illuminate\Http\Response
     */
    public function show(CIDLoopUp $cIDLoopUp)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\CIDLoopUp  $cIDLoopUp
     * @return \Illuminate\Http\Response
     */
    public function edit(CIDLoopUp $cIDLoopUp)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\CIDLoopUp  $cIDLoopUp
     * @return \Illuminate\Http\Response
     */
    public function update(CIDLoopUp $cIDLoopUp, Request $request)
    {
        // return response()->json([
        //     'message' => 'Update Problems',
        // ]);


        $request->validate([
            'url' => ['required', 'url'],
            'key' => ['required', 'string'],
        ]);
        try{
            $cIDLoopUp->url = $request->url;
            $cIDLoopUp->key = $request->key;
            $cIDLoopUp->required = $request->required;
            $cIDLoopUp->save();
            return response()->json("CID Loop-up has been updated");
        }catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\CIDLoopUp  $cIDLoopUp
     * @return \Illuminate\Http\Response
     */
    public function destroy(CIDLoopUp $cIDLoopUp)
    {
        //
    }
}
