<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormFunction extends Model
{
    use HasFactory;

    protected $table = 'form_functions';
    protected $fillable = ['form_id','name', 'label', 'function_type_id', 'reference_field_id'];
    
    public function form(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Form::class);
    }

    public function reference_field(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(FormField::class ,'reference_field_id');
    }

    public function function_type(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(FormFunctionType::class ,'function_type_id');
    }
}
