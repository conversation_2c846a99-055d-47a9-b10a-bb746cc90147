<?php

namespace App\Http\Controllers;

use App\Models\SystemSetting;
use Illuminate\Http\Request;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\OriginateAction;

class CallController extends Controller
{
    protected function getOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    public function dispatchCall(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'number' => ['required'],
            'agent' => ['required', 'exists:users,auth_username']
        ]);

        try {
            $response = $this->dialCall($request->number, $request->agent);
            return response()->json($response);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * @throws \PAMI\Client\Exception\ClientException
     */
    private function dialCall($number, $agent): string
    {
        $client = new ClientImpl($this->getOptions());
        $action = new OriginateAction("PJSIP/$agent");
        $action->setCallerId($number);
        $action->setExtension($number);
        $action->setContext("default");
        $action->setPriority(1);
        $client->open();
        $response = $client->send($action);
        $client->close();
        return $response->getMessage();
    }
}
