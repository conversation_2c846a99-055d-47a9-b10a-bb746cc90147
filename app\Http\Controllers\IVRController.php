<?php

namespace App\Http\Controllers;

use App\Models\IVR;
use Illuminate\Http\Request;

class IVRController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return IVR::query()->get();
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => ['required', 'string', 'max:255'],
                'script' => ['required']
            ]);
            $ivr = new IVR;
            $ivr->fill($request->all());
            $ivr->save();
            return response()->json(['status' => 1]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\IVR  $iVR
     * @return \Illuminate\Http\Response
     */
    public function show(IVR $iVR)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\IVR  $iVR
     * @return \Illuminate\Http\Response
     */
    public function edit(IVR $iVR)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\IVR  $iVR
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, IVR $iVR)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\IVR  $iVR
     * @return \Illuminate\Http\Response
     */
    public function destroy(IVR $iVR)
    {
        //
    }
}
