<?php

namespace App\Http\Controllers;

use App\Models\ServiceRating;
use App\Models\ServiceRatingSetting;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use PAMI\AsyncAgi\AsyncClientImpl;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\BlindTransferAction;
use PAMI\Message\Action\CoreShowChannelsAction;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\CoreShowChannelEvent;

class AGIController extends Controller
{
    public int $responseWaitTime = 10000; // In milliseconds
    public array $responseArray = [1, 2];
    public int $responseTries = 3;
    public string $capturedDigits = '';
    public string $recording = 'trax/for_rating';

    protected function getOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    /**
     * @throws \PAMI\Client\Exception\ClientException
     */
    public function getServiceRating(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'agentId' => ['required'],
            'extension' => ['required']
        ]);
        $context = "asyncagi_rating";
        $extension = $request->extension;
        $agentId = $request->agentId;
        $settings = ServiceRatingSetting::query()->first();
        $file = $settings->serviceRatingFile;
        $this->recording = $file->fileloc;
        try {
            $channel = $this->getChannel($request->agentId);
            $client = new ClientImpl($this->getOptions());
            $action = new BlindTransferAction($channel, $extension, $context);
            $client->open();
            $response = $client->send($action);
            $client->close();
            return response()->json($response->getMessage());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * @throws \PAMI\Client\Exception\ClientException
     */
    private function getChannel(string $agentId): ?string
    {
        $client = new ClientImpl($this->getOptions());
        $action = new CoreShowChannelsAction();
        $client->open();
        $result = $client->send($action);
        $client->close();
        $events = $result->getEvents();
        foreach ($events as $event) {
            if($event instanceof CoreShowChannelEvent && Str::contains($event->getKey('channel'), $agentId)) {
                return $event->getKey('channel');
            }
        }
        return null;
    }
}
