<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Models\SystemSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PAGI\Exception\ChannelDownException;
use PA<PERSON>\AsyncAgi\AsyncClientImpl;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\EventMessage;

class StoreDtmf extends Command
{
    protected $signature = 'dtmf:start';
    protected $description = 'Capture DTMF input from inbound calls before they connect and store in dtmf_input table';

    public function __construct()
    {
        parent::__construct();
    }

    protected function getAMIOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    public function handle()
    {
        $this->info("DTMF CAPTURE SERVICE RUNNING");
        $client = new ClientImpl($this->getAMIOptions());

        $client->registerEventListener(function (EventMessage $event) use ($client) {
            if (!$event instanceof AsyncAGIStartEvent) {
                return;
            }

            $env = $event->getKey('env');
            $this->info("ARG : $env");
            preg_match('/agi_arg_1:\s*(\S+)/', $env, $matches);
            $scriptArg = $matches[1] ?? null;
            $this->info("ARG_VALUE : $scriptArg");
            if ($scriptArg !== 'StoreDtmf') {
                return;
            }

            $channel = $event->getChannel();
            $extension = $event->getExten();

            $agiClient = new AsyncClientImpl([
                'pamiClient' => $client,
                'asyncAgiEvent' => $event
            ]);

            $this->processDtmfCapture($agiClient, $channel, $extension);
        }, function ($event) {
            return $event instanceof AsyncAGIStartEvent;
        });

        $client->open();

        while (true) {
            $client->process();
        }
    }

    protected function processDtmfCapture($agiClient, $channel, $extension)
    {
        try {
            $this->info("DTMF Capture Processing");


            $vars = $agiClient->getChannelVariables();
            $uniqueId = $vars->getUniqueId();
            $callerNumber = $vars->getCallerId();

            $this->info("Call from {$callerNumber} with uniqueID: {$uniqueId}");

            $this->info("Waiting for DTMF input...");
            $result = $agiClient->getData('', 5000, 1);
            $dtmf = $result->getDigits();

            if (!empty($dtmf)) {
                $this->info("Received DTMF: {$dtmf}");

                DB::table('dtmf_input')
                    ->where('phone_number', $callerNumber)
                    ->delete();

                DB::table('dtmf_input')->insert([
                    'unique_id' => $uniqueId,
                    'dtmf' => $dtmf,
                    'phone_number' => $callerNumber,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                $this->info("DTMF input saved to database");

                $agiClient->streamFile('beep');
            } else {
                DB::table('dtmf_input')
                    ->where('phone_number', $callerNumber)
                    ->delete();
                $this->info("No DTMF input received");
            }

            $agiClient->asyncBreak();
        } catch (ChannelDownException $e) {
            $this->error("Channel down: {$e->getMessage()}");
        } catch (\Exception $e) {
            $this->error("Error processing DTMF capture: {$e->getMessage()}");
            Log::error("DTMF Capture Error: " . $e->getMessage());
        }
    }
}
