<?php

namespace App\Http\Controllers;

use App\Events\AgentlessCampaignUpdate;
use App\Exports\AgentlessCampaignExport;
use App\Imports\AgentlessCampaignNumberImport;
use App\Models\AgentlessCampaign;
use App\Models\CallingServer;
use App\Models\Cdr;
use App\Models\Signal;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\CommandAction;
use App\Models\PrepaidSetting;
use Illuminate\Support\Facades\Log;

class AgentlessCampaignController extends Controller
{
    public function getCampaigns(Request $request)
    {
        try {
            // if (Auth::user()->is_supervisor == 1) {
            //     return response()->json(AgentlessCampaign::query()->orderBy('created_at', 'desc')->get());
            // } else {
            //     return response()->json(AgentlessCampaign::query()->where('user_id', Auth::user()->id)->orderBy('created_at', 'desc')->get());
            // }

            $campaigns = AgentlessCampaign::orderBy('created_at', 'desc')->get();
            event(new AgentlessCampaignUpdate($campaigns));
            return response()->json($campaigns);
        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    public function stopCampaign(AgentlessCampaign $campaign)
    {
        try {
            // Check if signal already exists
            $check = Signal::query()->where('campaign_id', $campaign->id)->where('signal', 'terminate')->orderBy('created_at', 'desc')->first();
            if (!$check) {
                $campaign->stage = 'cancelled';
                $campaign->save();
                $signal = Signal::query()->create(['campaign_id' => $campaign->id, 'signal' => 'terminate']);
                return response()->json("Signal: $signal->signal sent to campaign.");
            } else {
                return response()->json("Signal: $check->signal already exists.", 400);
            }
        } catch (Exception $e) {
            return response()->json([$e->getMessage()], 500);
        }
    }

    public function restartCampaign(AgentlessCampaign $campaign)
    {
        try {

            $check = Signal::query()->where('campaign_id', $campaign->id)->where('signal', 'terminate')->orderBy('created_at', 'desc')->first();
            if ($check) {
                $now = Carbon::now();
                $check->delete();
                // Update campaign parameters so that schedule will automatically pick it up.
                $campaign->update(['date_from' => $now->toDateString(), 'time_from' => $now->addSeconds(30)->toTimeString(), 'stage' => 'scheduled']);
                return response()->json("Campaign will be restarted after 30 seconds.", 200);
            } else {
                return response()->json("Signal: $check->signal does not exists.", 400);
            }
        } catch (Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    public function activateCampaign(AgentlessCampaign $campaign): \Illuminate\Http\JsonResponse
    {
        if ($campaign->status) {

            $campaign->status = false;
            $campaign->stage = 'cancelled';
            $campaign->save();
            return response()->json("Campaign has been deactivated.");
        } else {
            if (AgentlessCampaign::query()->where('status', true)->count() === 1) {
                return response()->json("Only 1 campaign is allowed to execute. Deactivate the active campaign and try again.", 500);
            } else {
                // Run Asterisk reload command
                $server = CallingServer::first()->toArray();
                $client = new ClientImpl($server);
                $action = new CommandAction('reload');
                $client->open();
                $res = $client->send($action);
                if (!$res->isSuccess()) {
                    return response()->json($res, 500);
                }
                $client->close();
                $campaign->status = true;
                $campaign->stage = 'scheduled';
                $campaign->save();
                return response()->json("Campaign has been activated. Now, it will start according to the schedule.");
            }
        }
    }

    public function getSingleCampaign(AgentlessCampaign $campaign): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return response()->download(storage_path("app/$campaign->file_path"));
    }
    // working on campaign create method 
    public function createCampaign(Request $request): \Illuminate\Http\JsonResponse
    {

        $request->validate([
            'name' => ['required', 'unique:agentless_campaigns,name'],
            'file' => ['required', 'file', 'mimes:csv,txt'],
            'date_between' => ['required'],
            'time_between' => ['required'],
            'time_to' => ['required'],
            'days' => ['required'],
            'recording' => ['required'],
        ]);

        try {

            $csv = count(file($request->file));
            if ($csv > 5000) {
                return response()->json(['message' => 'Numbers cannot exceed 5000'], 422);
            }

            $prepaid = PrepaidSetting::first();

            if ($prepaid) {
                if ($prepaid->remaining_amount <= $prepaid->amount * 0.02) {
                    return response()->json(['message' => 'No  amount left.'], 422);
                }
            }

            $dateArray = explode(',', $request->date_between);
            $path = $request->file('file')->store('local');

            $campaign = AgentlessCampaign::create([
                'name' => $request->name,
                'file_path' => $path,
                'date_from' => Carbon::parse($dateArray[0])->toDateString(),
                'date_to' => Carbon::parse($dateArray[1])->toDateString(),
                'time_from' => Carbon::parse($request->time_between)->toTimeString(),
                'time_to' => Carbon::parse($request->time_to)->toTimeString(),
                'days' => $request->days,
                'user_id' => Auth::user()->id,
                'recording_name' => $request->recording,
                'stage' => "created",
                // 'prepaid_id' => $prepaid->id ?? null
            ]);
            // importing numberes
            Excel::import(new AgentlessCampaignNumberImport($campaign->id), $path);
                       
            return response()->json([
                "message" => "Campaign Created successfully",
                "campaginId" => $campaign->id,
                "campaignName" => $campaign->name
            ]);


        } catch (Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    public function campaignExport($campaign) 
{
    try {
        
        $campaign = AgentlessCampaign::query()->findOrFail($campaign);
        $data1 = Cdr::where('accountcode', $campaign->unique_id)->get()->toArray();
        $export = new AgentlessCampaignExport($data1);
        
        $fileName = 'CampaignNumberReport_' . time() . '.csv';
        $tempPath = tempnam(sys_get_temp_dir(), 'export');
        
        Excel::store($export, $fileName, 'local', \Maatwebsite\Excel\Excel::CSV);
        $filePath = storage_path('app/' . $fileName);
        $fileContent = file_get_contents($filePath);

        unlink($filePath);
       
        return response($fileContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');
            
    } catch (\Exception $e) {
        Log::error("Export failed: " . $e->getMessage());
        return response()->json(["error" => $e->getMessage()], 500);
    }
}

    // public function campaignExportOldBk(Request $request)
    // {

    //     try {

    //         $campaign = AgentlessCampaign::query()->find($request->campaign_id);
    //         $data1 = Cdr::where('accountcode', $campaign->unique_id)->get()->toArray();
    //         return Excel::download(new AgentlessCampaignExport($data1), 'CampaignNumberReport.csv');
    //     } catch (\Exception $e) {
    //         return response()->json("File is unable to download");
    //     }
    // }

    public function viewReportSummary($id)
    {
        try {
            $campaign = AgentlessCampaign::query()->find($id);
            $accountcode = $campaign->unique_id;
            // Fetch data from Server A
            $data1 = CDR::where('accountcode', $accountcode)
                ->groupBy('disposition')
                ->selectRaw('disposition, COUNT(*) as count')
                ->get()
                ->toArray();
            $data2 = [];
            $data3 = [];
            $mergedData = array_merge($data1, $data2, $data3);
            $sums = [];

            // Iterate through the original array
            foreach ($mergedData as $item) {
                // If the status is "FAILED," merge it with "NO ANSWER"
                if ($item['disposition'] === 'FAILED') {
                    $item['disposition'] = 'NO ANSWER';
                }

                // Check if the status is already in the sums array
                if (isset($sums[$item['disposition']])) {
                    // If yes, add the count to the existing sum
                    $sums[$item['disposition']] += $item['count'];
                } else {
                    // If not, initialize the sum for the status
                    $sums[$item['disposition']] = $item['count'];
                }
            }

            // If both "FAILED" and "NO ANSWER" exist, merge their counts
            if (isset($sums['FAILED']) && isset($sums['NO ANSWER'])) {
                $sums['NO ANSWER'] += $sums['FAILED'];
                unset($sums['FAILED']); // Remove the separate count for "FAILED"
            }


            return response()->json($sums);
        } catch (\Exception $e) {
            return response()->json($e);
        }
    }

    public function destroy(AgentlessCampaign $campaign)
    {
        try {
            $campaign->campaignNumbers()->delete();
            $campaign->delete();
            return response()->json("Campaign has been deleted.");
        } catch (Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }
}
