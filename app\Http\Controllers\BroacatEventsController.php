<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Event\QueueMemberEvent;
use App\Events\IsReady;
use App\Events\AgentLogin;
use App\Events\AgentStatus;

class BroacatEventsController extends Controller
{

    protected $client;

    public function __construct()
    {
        $this->client = new ClientImpl($this->getOptions());
    }

    protected function getOptions(): array
    {
        return [
                'host' => SystemSetting::GetSetting('server_address'),
                'scheme' => 'tcp://',
                'port' => SystemSetting::GetSetting('manager_port'),
                'username' => SystemSetting::GetSetting('username'),
                'secret' => SystemSetting::GetSetting('secret'),
                'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
                'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    public function broadcast(Request $request) {
        try {
            $user = $request->user();

            if($user) {
                $this->client->open();

                $queues = $user->queues;
                $response = null;
                foreach ($queues as $queue) {
                    $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                    $response = $this->client->send($action);
                }

                if ($response) {
                    $event = $response->getEvents()[1];
                    $readyStatus = $event->getKey('paused');
                    $agentStatus = $event->getKey('event');
                    $loginStatus = $response->getEvents()[1] instanceof QueueMemberEvent;

                    sleep(3);

                    broadcast(new IsReady(['user_id' => $user->id, 'status' => $readyStatus == 1 ? false : true]))->toOthers();
                    broadcast(new AgentLogin(['user_id' => $user->id, 'status' => $agentStatus === 'QueueMember' ? true : false]))->toOthers();
                    broadcast(new AgentStatus(['user_id' => $user->id, 'status' => $loginStatus]))->toOthers();

                    \Log::info('IsReady : ' . var_export($readyStatus == 1 ? false : true, true));
                    \Log::info('AgentLogin : ' . var_export($agentStatus === 'QueueMember' ? true : false, true));
                    \Log::info('AgentStatus : ' . var_export($loginStatus, true));

                }

                unset($response);
                $this->client->close();
                unset($this->client);

                return response()->json("events broadcast successfully.", 200);
            }

            return response()->json("user not found.", 200);
        }
        catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
?>
