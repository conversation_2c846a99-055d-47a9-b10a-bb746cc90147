<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VoicemailSetting extends Model
{
    use HasFactory;

    protected $casts = [
        'weeks' => 'json',
        'except' => 'json',
        'specificDates' => 'json',
    ];

    protected $fillable = ['weeks', 'except', 'start', 'end', 'status', 'specificDates', 'specificDateStart', 'specificDateEnd'];

    public function setStartAttribute($value)
    {
        $this->attributes['start'] = Carbon::parse($value)->addHours(5);
    }

    public function setEndAttribute($value)
    {
        $this->attributes['end'] = Carbon::parse($value)->addHours(5);
    }

    public function setSpecificDateStartAttribute($value)
    {
        if ($value)
            $this->attributes['specificDateStart'] = Carbon::parse($value)->addHours(5);
    }

    public function setSpecificDateEndAttribute($value)
    {
        if($value)
            $this->attributes['specificDateEnd'] = Carbon::parse($value)->addHours(5);
    }

    public function voicemailFile(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(VoicemailFile::class, 'voicemail_file_id');
    }
}
