<?php

namespace App\Http\Controllers;

use App\Jobs\AgentlessCampaignHandlerJob;
use App\Models\AgentlessCampaign;
use App\Models\AgentlessCampaignLog;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class AgentlessTestController extends Controller
{
    public function command()
    {
        try{
            $campaign = AgentlessCampaign::where('status', true)->orderBy('created_at', 'desc')->first();
            if($campaign) {
                $campaignLogs = AgentlessCampaignLog::query()->where('campaign_id', $campaign->id)->where('log', 'started')->orderBy('created_at', 'desc')->first();
                $now = Carbon::now();
                if($now->toDateString() >= $campaign->date_from && $now->toDateString() <= $campaign->date_to && $now->between(Carbon::createFromTimeString($campaign->time_from), Carbon::createFromTimeString($campaign->time_to)) && in_array(strtoupper($now->dayName), explode(',', strtoupper($campaign->days)))) {
                    Artisan::call('horizon:status');
                    $output = Artisan::output();
                    if ($output === 'Horizon is paused.') {
                        Artisan::call('horizon:continue');
                    }
                    if(!$campaignLogs) {
                        // AgentlessCampaignHandlerJob::dispatch($campaign->id)->onQueue('default');
                    }
    
                } else {
                    if($campaignLogs && $campaignLogs->log === 'started') {
                        // Then stop it
                        // Check horizon status
                        Artisan::call('horizon:status');
                        $output = Artisan::output();
                        if($output === 'Horizon is running.') {
                            Artisan::call('horizon:pause');
                        }
                    }
                }
            }
        }catch(Exception $e){
            return response()->json(['message' => $e->getMessage()]);
        }
    }
}
