<?php

namespace App\Http\Controllers;

use App\Models\Campaign;
use App\Models\Cdr;
use App\Models\Form;
use App\Models\PauseReason;
use App\Models\PrepaidSetting;
use App\Models\Queue;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StatsController extends Controller
{
    public function getStats()
    {

        $total_campaigns = Campaign::query()->count();
        $total_forms = Form::query()->count();
        $total_users = User::query()->count();
        $total_agents = User::query()->where('type', '!=', 'Normal')->where('type', '!=', 'supervisor')->count();
        $outbound_activity = Cdr::query()->count();
        $total_break = PauseReason::query()->count();
        $total_queues = Queue::query()->count();
       $prepaid =  PrepaidSetting::first();
        $widgets_data = [
            'total_campaigns' => $total_campaigns,
            'total_forms' => $total_forms,
            'total_users' => $total_users,
            'total_agents' => $total_agents,
            'outbound_activity' => $outbound_activity,
            'total_break' => $total_break,
            'total_queues' => $total_queues,
            'prepaid' => [
                'amount' => $prepaid->amount,
                'remaining_amount' => $prepaid->remaining_amount
            ]

        ];

        $Inbound = DB::select("SELECT Date(start) as Date, sum(CASE WHEN disposition ='answered' THEN 1 ELSE 0 END) AS 'Success', sum(CASE WHEN disposition !='answered' THEN 1 ELSE 0 END) AS 'Failed' FROM `cdr` WHERE month(start) = month(CURDATE()) and accountcode='Queue' GROUP by date(start)");

        $Outbound = DB::select("SELECT Date(start) as Date, sum(CASE WHEN disposition ='answered' THEN 1 ELSE 0 END) AS 'Success', sum(CASE WHEN disposition !='answered' THEN 1 ELSE 0 END) AS 'Failed' FROM `cdr` WHERE month(start) = month(CURDATE()) and accountcode='Outbound' GROUP by date(start)");

        // return response()->json($Inbound);
        return response()->json(['widgets_data' => $widgets_data, 'inbound_call_summary' => $Inbound, 'outbound_call_summary' => $Outbound]);
    }
}
