<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FileController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        if($request->hasFile('fileName')) {

            $request->validate([
                'fileName' => ['required', 'mimes:csv'],
            ]);

            $allowedExtensions = ['csv'];
            $file = $request->file('fileName');
            $extension = $file->getClientOriginalExtension();

            if(in_array($extension, $allowedExtensions)) {
                $path = $file->storeAs('files', $file->getClientOriginalName());

                if($path) {
                    return response()->json($path);
                }

                return response()->json('file_could_not_be_uploaded', 422);
            } else {
                return response()->json('invalid_file_format', 422);
            }
        } else {
            return response()->json('upload_file_not_found', 400);
        }
    }
}
