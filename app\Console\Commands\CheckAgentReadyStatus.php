<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\{
    SystemSetting, User, PauseReason, WorkCode, QueueLog, CallbackRequest, ScheduleCallBack
};
use App\Events\{
    GetPauseReason, GetWorkCode, GetQueues, GetUserCampagin, GetUser,
    GetSystemSetting, GetAbandonCallReport, GetCallbackRequest, GetScheduleCallback, GetScheduleCallBackReport
};
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Event\QueueMemberEvent;

class CheckAgentReadyStatus extends Command
{
    protected $signature = 'app:check-agent-ready-status';
    protected $description = 'Real-time Agent Status Tracker';

    protected $client;
    protected $users = [];
    protected $lastBroadcastTime = 0;
    protected $staticDataReloadInterval = 3600;

    public function handle()
    {
        $this->listenForRealTimeEvents();
    }

    protected function listenForRealTimeEvents()
    {
        while (true) {
            try {
                $this->broadcastUpdatesIfNeeded();
                usleep(100_000);
            } catch (\Exception $e) {
                Log::error("AMI error: " . $e->getMessage());
            }
        }
    }

    protected function broadcastUpdatesIfNeeded()
    {
        $now = time();
        if ($now - $this->lastBroadcastTime < 5) return;

        $this->users = $this->getLoggedInUsers();
        $this->broadcastCommonData();
        $this->lastBroadcastTime = $now;
    }

    protected function broadcastCommonData()
    {
        $settings = SystemSetting::query()->get();

        foreach ($this->users as $user) {
            broadcast(new GetPauseReason(['user_id' => $user->id, 'data' => PauseReason::all()]));
            broadcast(new GetWorkCode(['user_id' => $user->id, 'data' => WorkCode::all()]));

            if ($user->queues->isNotEmpty()) {
                broadcast(new GetQueues([
                    'user_id' => $user->id,
                    'data' => $user->queues->pluck('name')
                ]));
            }


            broadcast(new GetUser(['user_id' => $user->id, 'data' => $user]));

            // Campaigns
            $campaignData = $user->campaigns()->withCount(['campaign_numbers as count' => function($query) {
                                $query->where('status', false)->where('attempts', '>', 0);
                            }])->get();

            broadcast(new GetUserCampagin(['user_id' => $user->id, 'data' => $campaignData]));

            // Syetem Settings
            $setting=null;
            $myObj = new \stdClass();
            foreach ($settings as $value)
            {
                $id= $value->id;
                $column[]=array('title'=> $value->key, 'dataIndex'=> $value->key, 'key'=> $value->key);
                $key= $value->key;
                $myObj->$key= $value->value;
            }
            $setting= array($myObj);
            $column[] = array('title'=> 'action', 'dataIndex'=> 'action', 'key'=>'action');

            $settingData = ['user_id' => $user->id, 'data' => $setting];

            broadcast(new GetSystemSetting($settingData));


            $this->abandonCallReport($user);
            $this->callbackRequestReport($user);
            $this->broadcastScheduleCallbacks($user);
            $this->scheduleCallBackReport($user);
        }
    }

    protected function getLoggedInUsers()
    {
        $userIds = array_keys(Cache::get('logged_in_users', []));
        return User::whereIn('id', $userIds)->with('queues')->get();
    }

    public function abandonCallReport($user)
    {
        if($user) {
            $users = Cache::get('logged_in_users', []);
            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            $pagination = $users[$user->id]['pagination']['abandonCalls'] ?? ['current' => 1, 'pageSize' => 15];
            $current = $pagination['current'];
            $pageSize = $pagination['pageSize'];

            $data = QueueLog::query()->from('queue_log as q')
                    ->join('cdr as c', 'c.uniqueid', '=', 'q.callid')
                    ->select(
                        DB::raw('DATE(q.time) as date'),
                        DB::raw('TIME(q.time) as time'),
                        'c.uniqueid',
                        'q.callid',
                        'c.src',
                        'c.dst',
                        'q.data3 as waittime',
                        'q.EVENT',
                        'q.status'
                    )
                    ->where('q.EVENT', 'ABANDON')
                    ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2])
                    ->where('q.status', '0');

            $paginatedData = $data->paginate($pageSize, ['*'], 'page', $current);

            $response = [
                'user_id' => $user->id,
                'current' => $paginatedData->currentPage(),
                'pageSize' => $paginatedData->perPage(),
                'total' => $paginatedData->total(),
                'data' => ['data' => $paginatedData->items()],
            ];

            broadcast(new GetAbandonCallReport($response));
        }
    }

    public function callbackRequestReport($user)
    {
        if($user) {
            $users = Cache::get('logged_in_users', []);
            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            $pagination = $users[$user->id]['pagination']['callbackRequests'] ?? ['current' => 1, 'pageSize' => 15];
            $current = $pagination['current'];
            $pageSize = $pagination['pageSize'];

            $data = CallbackRequest::query()->from('callback_requests as c')
                    ->select(
                        'c.id',
                        'c.caller_id',
                        'c.queue',
                        'c.request_time',
                        DB::raw('DATE(c.request_time) as date'),
                        DB::raw('TIME(c.request_time) as time'),
                        'c.filePath',
                        'c.fileName',
                        'c.fileLoc'
                    )
                    ->whereDate('c.request_time', '>=', $date1)
                    ->whereDate('c.request_time', '<=', $date2)
                    ->where('c.status', '=', '0');

            $paginatedData = $data->paginate($pageSize, ['*'], 'page', $current);

            $response = [
                'user_id' => $user->id,
                'currentPage' => $paginatedData->currentPage(),
                'pageSize' => $paginatedData->perPage(),
                'total' => $paginatedData->total(),
                'data' => ['data' => $paginatedData->items()],
            ];

            broadcast(new \App\Events\GetCallbackRequest($response));
        }
    }

    protected function broadcastScheduleCallbacks_backup($user): void
    {
        $now  = Carbon::now('Asia/Karachi');
        $from = $now->copy()->subMinutes(5);

        $callbacks = ScheduleCallBack::query()->where('status', 'pending')->where('created_by', $user->id)->whereBetween('date', [$from, $now])->orderBy('date')->get();

        foreach ($callbacks as $cb) {
            $cbDate = $cb->date instanceof Carbon ? $cb->date : Carbon::parse($cb->date);

            broadcast(new GetScheduleCallback([
                'user_id'  => $user->id,
                'callback' => [
                    'id'       => $cb->id,
                    'number'   => $cb->number,
                    'date'     => $cbDate->timezone('Asia/Karachi')->toDateTimeString(),
                    'status'   => $cb->status,
                    'uniqueid' => $cb->uniqueid,
                ],
            ]));
        }
    }

    protected function broadcastScheduleCallbacks($user): void
    {
        $now  = Carbon::now('Asia/Karachi');
        $from = $now->copy()->subMinutes(5);

        $callbacks = ScheduleCallBack::query()->where('status', 'pending')->where('created_by', $user->id)->whereBetween('date', [$from, $now])->orderBy('date')->get();

        $data = ['user_id'  => $user->id];

        foreach ($callbacks as $cb) {
            $cbDate = $cb->date instanceof Carbon ? $cb->date : Carbon::parse($cb->date);

            $data['callback'][$cb->id] = [
                    'id'       => $cb->id,
                    'number'   => $cb->number,
                    'date'     => $cbDate->timezone('Asia/Karachi')->toDateTimeString(),
                    'status'   => $cb->status,
                    'uniqueid' => $cb->uniqueid,
            ];
        }

        if(isset($data['callback'])) {
	    \Log::info('schedule callback : ', $data);
            broadcast(new GetScheduleCallback($data));
        }
    }

    public function scheduleCallBackReport($user): void
    {
        if (!$user) return;

        $users = Cache::get('logged_in_users', []);
        $pagination = $users[$user->id]['pagination']['scheduleCallBack'] ?? ['current' => 1, 'pageSize' => 15];
        $current   = max(1, (int)($pagination['current']  ?? 1));
        $pageSize  = max(1, (int)($pagination['pageSize'] ?? 15));

        $tz = 'Asia/Karachi';
        $todayDate     = Carbon::today($tz)->toDateString();
        $yesterdayDate = Carbon::yesterday($tz)->toDateString();

        $base = ScheduleCallBack::query()->where('status', 'pending')->where('created_by', $user->id);


        $upcoming = (clone $base)->whereDate('date', '>', $todayDate)->select([
            'id', 'number', 'date', 'status', 'uniqueid', 'created_by',
            DB::raw("'upcoming' as category"),
        ]);


        $overdueQ = (clone $base)->whereDate('date', '<', $todayDate)->select([
            'id', 'number', 'date', 'status', 'uniqueid', 'created_by',
            DB::raw("'overdue' as category"),
        ]);

        $yesterdayQ = (clone $base)->whereDate('date', $yesterdayDate)->select([
                'id', 'number', 'date', 'status', 'uniqueid', 'created_by',
                DB::raw("'yesterday' as category"),
            ]);

        $todayQ = (clone $base)->whereDate('date', $todayDate)->select([
                'id', 'number', 'date', 'status', 'uniqueid', 'created_by',
                DB::raw("'today' as category"),
            ]);


        $union = $todayQ->unionAll($yesterdayQ)->unionAll($overdueQ)->unionAll($upcoming);

        $categoryOrder = "
            CASE scb.category
                WHEN 'today' THEN 1
                WHEN 'yesterday' THEN 2
                WHEN 'overdue' THEN 3
		WHEN 'upcoming' THEN 4
                ELSE 4
            END";

        $combined = DB::query()
        ->fromSub($union, 'scb')
        ->orderByRaw("$categoryOrder ASC")
        ->orderBy('date', 'asc');

        $page = $combined->paginate($pageSize, ['*'], 'page', $current);
        $records = $page->items();

        $response = [
            'user_id' => $user->id,
            'meta' => [
                'current'  => $page->currentPage(),
                'pageSize' => $page->perPage(),
                'total'    => $page->total(),
            ],
            'records' => $records,
        ];

        broadcast(new GetScheduleCallBackReport($response));
    }

}
