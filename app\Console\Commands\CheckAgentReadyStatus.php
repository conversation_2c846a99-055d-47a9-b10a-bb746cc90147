<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\{
    SystemSetting,
    User,
    PauseReason,
    WorkCode,
    QueueLog,
    CallbackRequest,
    ScheduleCallBack
};
use App\Events\{
    GetPauseReason,
    GetWorkCode,
    GetQueues,
    GetUserCampagin,
    GetUser,
    GetSystemSetting,
    GetAbandonCallReport,
    GetCallbackRequest,
    GetScheduleCallback,
    GetScheduleCallBackReport
};
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Event\QueueMemberEvent;

class CheckAgentReadyStatus extends Command
{
    protected $signature = 'app:check-agent-ready-status';
    protected $description = 'Real-time Agent Status Tracker';

    protected $client;
    protected $users = [];
    protected $lastBroadcastTime = 0;
    protected $staticDataReloadInterval = 3600;
    protected $cachedData = [];
    protected $consecutiveErrors = 0;
    protected $lastDataHashes = [];

    public function handle()
    {
        $this->info('Starting optimized agent status tracker...');
        $this->listenForRealTimeEvents();
    }

    protected function listenForRealTimeEvents()
    {
        while (true) {
            try {
                $this->broadcastUpdatesIfNeeded();
                $this->consecutiveErrors = 0;
                usleep($this->getAdaptivePollingInterval());
            } catch (\Exception $e) {
                $this->consecutiveErrors++;
                Log::error("Agent status error: " . $e->getMessage());

                if ($this->consecutiveErrors > 5) {
                    $this->error('Too many consecutive errors, sleeping for 30 seconds...');
                    sleep(30);
                    $this->consecutiveErrors = 0;
                }
            }
        }
    }

    protected function getAdaptivePollingInterval()
    {
        $activeUsers = count($this->getLoggedInUsers());
        if ($activeUsers === 0) return 5000000;
        if ($activeUsers < 5) return 1000000;
        return 500000;
    }

    protected function broadcastUpdatesIfNeeded()
    {
        $now = time();
        if ($now - $this->lastBroadcastTime < 3) return;

        $this->users = $this->getLoggedInUsers();
        if (empty($this->users)) {
            $this->lastBroadcastTime = $now;
            return;
        }

        $this->broadcastCommonData();
        $this->lastBroadcastTime = $now;
    }

    protected function broadcastCommonData()
    {
        $staticData = $this->getCachedStaticData();
        $userIds = $this->users->pluck('id')->toArray();
        $allCampaignData = $this->getBatchCampaignData($userIds);

        foreach ($this->users as $user) {
            $userId = $user->id;

            if ($this->shouldBroadcastToUser($userId, 'pause_reasons', $staticData['pauseReasons'])) {
                broadcast(new GetPauseReason(['user_id' => $userId, 'data' => $staticData['pauseReasons']]));
            }

            if ($this->shouldBroadcastToUser($userId, 'work_codes', $staticData['workCodes'])) {
                broadcast(new GetWorkCode(['user_id' => $userId, 'data' => $staticData['workCodes']]));
            }

            if ($user->queues->isNotEmpty()) {
                $queueData = $user->queues->pluck('name');
                if ($this->shouldBroadcastToUser($userId, 'queues', $queueData)) {
                    broadcast(new GetQueues(['user_id' => $userId, 'data' => $queueData]));
                }
            }

            if ($this->shouldBroadcastToUser($userId, 'user', $user)) {
                broadcast(new GetUser(['user_id' => $userId, 'data' => $user]));
            }

            $campaignData = $allCampaignData[$userId] ?? collect();
            if ($this->shouldBroadcastToUser($userId, 'campaigns', $campaignData)) {
                broadcast(new GetUserCampagin(['user_id' => $userId, 'data' => $campaignData]));
            }

            if ($this->shouldBroadcastToUser($userId, 'settings', $staticData['settings'])) {
                broadcast(new GetSystemSetting(['user_id' => $userId, 'data' => $staticData['settings']]));
            }

            $this->broadcastReportsIfNeeded($user);
        }
    }

    protected function getLoggedInUsers()
    {
        $userIds = array_keys(Cache::get('logged_in_users', []));
        return User::whereIn('id', $userIds)->with('queues')->get();
    }

    protected function getCachedStaticData()
    {
        return [
            'pauseReasons' => Cache::remember('pause_reasons_data', 1800, fn() => PauseReason::all()),
            'workCodes' => Cache::remember('work_codes_data', 1800, fn() => WorkCode::all()),
            'settings' => Cache::remember('system_settings_formatted', 900, function () {
                $settings = SystemSetting::query()->get();
                $myObj = new \stdClass();
                foreach ($settings as $value) {
                    $myObj->{$value->key} = $value->value;
                }
                return [$myObj];
            })
        ];
    }

    protected function getBatchCampaignData($userIds)
    {
        if (empty($userIds)) return [];

        return Cache::remember("campaigns_batch_" . md5(implode(',', $userIds)), 300, function () use ($userIds) {
            $campaigns = DB::table('campaigns')
                ->join('campaign_user', 'campaigns.id', '=', 'campaign_user.campaign_id')
                ->leftJoin('campaign_numbers', function ($join) {
                    $join->on('campaigns.id', '=', 'campaign_numbers.campaign_id')
                        ->where('campaign_numbers.status', false)
                        ->where('campaign_numbers.attempts', '>', 0);
                })
                ->whereIn('campaign_user.user_id', $userIds)
                ->select('campaign_user.user_id', 'campaigns.*', DB::raw('COUNT(campaign_numbers.id) as count'))
                ->groupBy('campaign_user.user_id', 'campaigns.id')
                ->get()
                ->groupBy('user_id');

            return $campaigns;
        });
    }

    protected function shouldBroadcastToUser($userId, $dataType, $newData)
    {
        $cacheKey = "user_data_hash_{$userId}_{$dataType}";
        $currentHash = md5(serialize($newData));
        $lastHash = Cache::get($cacheKey);

        if ($currentHash !== $lastHash) {
            Cache::put($cacheKey, $currentHash, 300);
            return true;
        }
        return false;
    }

    protected function broadcastReportsIfNeeded($user)
    {
        if (rand(1, 4) === 1) {
            $this->abandonCallReport($user);
        }
        if (rand(1, 4) === 2) {
            $this->callbackRequestReport($user);
        }
        if (rand(1, 3) === 1) {
            $this->broadcastScheduleCallbacks($user);
        }
        if (rand(1, 4) === 3) {
            $this->scheduleCallBackReport($user);
        }
    }

    public function abandonCallReport($user)
    {
        if (!$user) return;

        $cacheKey = "abandon_calls_report_{$user->id}_" . Carbon::now()->format('Y-m-d');
        $response = Cache::remember($cacheKey, 300, function () use ($user) {
            $users = Cache::get('logged_in_users', []);
            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            $pagination = $users[$user->id]['pagination']['abandonCalls'] ?? ['current' => 1, 'pageSize' => 15];
            $current = $pagination['current'];
            $pageSize = $pagination['pageSize'];

            $data = QueueLog::query()->from('queue_log as q')
                ->join('cdr as c', 'c.uniqueid', '=', 'q.callid')
                ->select(
                    DB::raw('DATE(q.time) as date'),
                    DB::raw('TIME(q.time) as time'),
                    'c.uniqueid',
                    'q.callid',
                    'c.src',
                    'c.dst',
                    'q.data3 as waittime',
                    'q.EVENT',
                    'q.status'
                )
                ->where('q.EVENT', 'ABANDON')
                ->whereBetween(DB::raw('DATE(q.time)'), [$date1, $date2])
                ->where('q.status', '0');

            $paginatedData = $data->paginate($pageSize, ['*'], 'page', $current);

            return [
                'user_id' => $user->id,
                'current' => $paginatedData->currentPage(),
                'pageSize' => $paginatedData->perPage(),
                'total' => $paginatedData->total(),
                'data' => ['data' => $paginatedData->items()],
            ];
        });

        broadcast(new GetAbandonCallReport($response));
    }

    public function callbackRequestReport($user)
    {
        if (!$user) return;

        $cacheKey = "callback_request_report_{$user->id}_" . Carbon::now()->format('Y-m-d');
        $response = Cache::remember($cacheKey, 300, function () use ($user) {
            $users = Cache::get('logged_in_users', []);
            $date1 = Carbon::now()->startOfDay()->format("Y-m-d");
            $date2 = Carbon::now()->endOfDay()->format("Y-m-d");

            $pagination = $users[$user->id]['pagination']['callbackRequests'] ?? ['current' => 1, 'pageSize' => 15];
            $current = $pagination['current'];
            $pageSize = $pagination['pageSize'];

            $data = CallbackRequest::query()->from('callback_requests as c')
                ->select(
                    'c.id',
                    'c.caller_id',
                    'c.queue',
                    'c.request_time',
                    DB::raw('DATE(c.request_time) as date'),
                    DB::raw('TIME(c.request_time) as time'),
                    'c.filePath',
                    'c.fileName',
                    'c.fileLoc'
                )
                ->whereDate('c.request_time', '>=', $date1)
                ->whereDate('c.request_time', '<=', $date2)
                ->where('c.status', '=', '0');

            $paginatedData = $data->paginate($pageSize, ['*'], 'page', $current);

            return [
                'user_id' => $user->id,
                'currentPage' => $paginatedData->currentPage(),
                'pageSize' => $paginatedData->perPage(),
                'total' => $paginatedData->total(),
                'data' => ['data' => $paginatedData->items()],
            ];
        });

        broadcast(new \App\Events\GetCallbackRequest($response));
    }

    protected function broadcastScheduleCallbacks($user): void
    {
        if (!$user) return;

        $cacheKey = "schedule_callbacks_{$user->id}_" . Carbon::now()->format('Y-m-d_H');
        $data = Cache::remember($cacheKey, 180, function () use ($user) {
            $now  = Carbon::now('Asia/Karachi');
            $from = $now->copy()->subMinutes(5);

            $callbacks = ScheduleCallBack::query()
                ->where('status', 'pending')
                ->where('created_by', $user->id)
                ->whereBetween('date', [$from, $now])
                ->orderBy('date')
                ->get();

            $data = ['user_id' => $user->id];

            foreach ($callbacks as $cb) {
                $cbDate = $cb->date instanceof Carbon ? $cb->date : Carbon::parse($cb->date);

                $data['callback'][$cb->id] = [
                    'id'       => $cb->id,
                    'number'   => $cb->number,
                    'date'     => $cbDate->timezone('Asia/Karachi')->toDateTimeString(),
                    'status'   => $cb->status,
                    'uniqueid' => $cb->uniqueid,
                ];
            }

            return $data;
        });

        if (isset($data['callback'])) {
            broadcast(new GetScheduleCallback($data));
        }
    }

    public function scheduleCallBackReport($user): void
    {
        if (!$user) return;

        $cacheKey = "schedule_callback_report_{$user->id}_" . Carbon::now()->format('Y-m-d');
        $response = Cache::remember($cacheKey, 600, function () use ($user) {
            $users = Cache::get('logged_in_users', []);
            $pagination = $users[$user->id]['pagination']['scheduleCallBack'] ?? ['current' => 1, 'pageSize' => 15];
            $current   = max(1, (int)($pagination['current']  ?? 1));
            $pageSize  = max(1, (int)($pagination['pageSize'] ?? 15));

            $tz = 'Asia/Karachi';
            $todayDate     = Carbon::today($tz)->toDateString();
            $yesterdayDate = Carbon::yesterday($tz)->toDateString();

            $base = ScheduleCallBack::query()->where('status', 'pending')->where('created_by', $user->id);

            $upcoming = (clone $base)->whereDate('date', '>', $todayDate)->select([
                'id',
                'number',
                'date',
                'status',
                'uniqueid',
                'created_by',
                DB::raw("'upcoming' as category"),
            ]);

            $overdueQ = (clone $base)->whereDate('date', '<', $todayDate)->select([
                'id',
                'number',
                'date',
                'status',
                'uniqueid',
                'created_by',
                DB::raw("'overdue' as category"),
            ]);

            $yesterdayQ = (clone $base)->whereDate('date', $yesterdayDate)->select([
                'id',
                'number',
                'date',
                'status',
                'uniqueid',
                'created_by',
                DB::raw("'yesterday' as category"),
            ]);

            $todayQ = (clone $base)->whereDate('date', $todayDate)->select([
                'id',
                'number',
                'date',
                'status',
                'uniqueid',
                'created_by',
                DB::raw("'today' as category"),
            ]);

            $union = $todayQ->unionAll($yesterdayQ)->unionAll($overdueQ)->unionAll($upcoming);

            $categoryOrder = "
                CASE scb.category
                    WHEN 'today' THEN 1
                    WHEN 'yesterday' THEN 2
                    WHEN 'overdue' THEN 3
                    WHEN 'upcoming' THEN 4
                    ELSE 4
                END";

            $combined = DB::query()
                ->fromSub($union, 'scb')
                ->orderByRaw("$categoryOrder ASC")
                ->orderBy('date', 'asc');

            $page = $combined->paginate($pageSize, ['*'], 'page', $current);

            return [
                'user_id' => $user->id,
                'meta' => [
                    'current'  => $page->currentPage(),
                    'pageSize' => $page->perPage(),
                    'total'    => $page->total(),
                ],
                'records' => $page->items(),
            ];
        });

        broadcast(new GetScheduleCallBackReport($response));
    }
}
