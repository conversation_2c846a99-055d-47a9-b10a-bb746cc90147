<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomNumber extends Model
{
    use HasFactory;
    protected $fillable = ['retailer_name', 'shop_name', 'agent_id', 'inactive_days', 'target', 'gmv', 'number_of_unique_sku_sold', 'attempts', 'customer_phone', 'file', 'status'];

    public function campaign(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    public function getCustomerPhoneAttribute($value): string
    {
        return $value[0] === "0" ? $value : "0{$value}";
    }
}
