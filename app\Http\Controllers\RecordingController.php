<?php

namespace App\Http\Controllers;

use App\Events\AgentlessRecordingEvent;
use App\Models\Cdr;
use App\Models\Recording;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use ZipArchive;
use Zip;

class RecordingController extends Controller
{
    public function get($recordingFile)
    {
        try {
            if (!$recordingFile || $recordingFile === '') return response()->json("Invalid file name or path.", 500);
            $date = Carbon::createFromFormat("Ymd", explode("-", $recordingFile)[3]);
            return Storage::disk('recordings')->download("{$date->year}/{$date->format('m')}/{$date->format('d')}/$recordingFile");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }
//this Call agentlesscampaign file
    public function getFile($recordingFile)
    {
        try {
            $filePath = $recordingFile;
            if (!Storage::disk('agentlessCampaign')->exists($filePath)) {
                return response()->json("Recording file not found.",400);
            }
            return Storage::disk('agentlessCampaign')->download($filePath);
        } catch (\Exception $exception) {
           return response()->json($exception->getMessage(),500);
        }
    }
    
    //this Call Detail Report file
    public function monitorFile($recordingFile)
    {
        try {
            $date = Carbon::createFromFormat("Ymd", explode("-", $recordingFile)[3]);
            return Storage::disk('recordings')->download("{$date->year}/{$date->format('m')}/{$date->format('d')}/$recordingFile");
        }catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }


    public function getFiles(Request $request)
    {
        try {
            if (isset($request->date1) && isset($request->date2)) {
                $data = Cdr::query()->where('start', '>=', $request->date1)->where('end', '<=', $request->date2)->select(['recordingfile'])->get();
                $zip = new ZipArchive();
                $path = "recording_files.zip";
                if ($zip->open(public_path($path), ZipArchive::CREATE)) {
                    foreach ($data as $datum) {
                        $date = explode("-", $datum->recordingfile);
                        if (count($date) > 2) {
                            $tempDate = Carbon::createFromFormat("Ymd", Carbon::parse($date[3])->format('Ymd'));
                            if (Storage::disk('recordings')->exists("{$tempDate->year}/{$tempDate->format('m')}/{$tempDate->format('d')}/$datum->recordingfile")) {
                                $zip->addFile(Storage::disk('recordings')->path("{$tempDate->year}/{$tempDate->format('m')}/{$tempDate->format('d')}/$datum->recordingfile"));
                            }
                        }
                    }
                    $zip->close();
                    header('Content-disposition: attachment; filename=' . $path);
                    header('Content-type: application/zip');
                    readfile(public_path($path));
                } else {
                    return response()->json('Could not open ZIP file.');
                }
            } else {
                return response()->json("Invalid date provided.", 500);
            }
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    /*This for agentless campaign
    *****Recording of sonus
    */
    public function recordingStore(Request $request)
    {
        $request->validate([
            'file' => ['required', 'file', 'mimes:wav']
        ]);

        try {
            $filename = $request->file('file')->getClientOriginalName();
            $file = pathinfo($filename, PATHINFO_FILENAME); // for getting filename without extension
            if (Storage::disk('agentlessCampaign')->exists($filename)) {
                return response()->json('Recording already available', 500);
            } else {
                $data1 =  $request->file('file')->storeAs('', $request->file('file')->getClientOriginalName(), 'agentlessCampaign');
                if (Storage::disk('agentlessCampaign')->exists($filename)) {
                    $new = new Recording();
                    $new->file_name = $file;
                    $new->file_path = $data1;
                    $new->save();
                    return response()->json('Recording Uploaded', 200);
                } else {
                    return response()->json('Unable to upload. Please try again', 500);
                }
            }
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function index()
    {
        try {
            $data = Recording::all();
            broadcast(new AgentlessRecordingEvent($data));
            return response()->json($data,200);
        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    public function destroy($id)
    {
        try {
            $recording= Recording::findOrFail($id);
            if (Storage::disk('agentlessCampaign')->exists($recording->file_path)) {
                Storage::disk('agentlessCampaign')->delete($recording->file_path);
                $recording->delete();
                return response()->json('Recording deleted successfully', 200);
            }
            return response()->json("{$recording->file_name} has been deleted");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
