<?php

namespace App\Http\Controllers;

use App\Models\Outbound;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OutboundController extends Controller
{

    public function __construct() {

        $this->middleware('permission:create_outbounds', ['only' => ['create']]);
        $this->middleware('permission:update_outbounds', ['only' => ['update']]);
        $this->middleware('permission:delete_outbounds', ['only' => ['destroy']]);
        $this->middleware('permission:read_outbounds', ['only' => ['index']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response()->json([ 'data' => Outbound::all()]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $data = Outbound::create($request->all());
            if ($data != "")
                return response()->json(['data' => $data, "success" => '1']);
            else
                return response()->json(['data' => "data not inserted", "success" => '0']);
        }
        catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Outbound  $outbound
     * @return \Illuminate\Http\Response
     */
    public function show(Outbound $outbound)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Outbound  $outbound
     * @return \Illuminate\Http\Response
     */
    public function edit(Outbound $outbound)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Outbound  $outbound
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request , Outbound $outbound)
    {
        try{
            $status = $outbound->update($request->all());
            if($status)
                return response()->json(['data' => "data updated" , 'success' => '1']);
            else
                return response()->json (['data' => "data is not updated" , 'success' => '0']);
        }
        catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Outbound  $outbound
     * @return \Illuminate\Http\Response
     */
    public function destroy(Outbound $outbound)
    {
        try {
            $status = $outbound->delete();
            if($status)
                return response()->json(['data' => "deleted" , 'success' => '1']);
            else
                return response()->json(['data' => "data is not deleted" , 'success' => '0']);
        }
        catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }
}
