<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use Carbon\Carbon;
use Carbon\Traits\Creator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CdrController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(Cdr::query()->orderBy('end', 'desc')->get(['src', 'dst', 'clid', 'channel', 'dstchannel', 'lastapp', 'start', 'answer', 'end', 'duration', 'billsec', 'disposition', 'recordingfile']));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    //CDR report Filter with post method.
    public function CdrReportFilter(Request $request)
    {
//        return response()->json($request->all());

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(): \Illuminate\Http\JsonResponse
    {
        try {
            $cdr = Cdr::query();
            if(isset($request->values['time']) && $request->values['time'] !== '') {
                $cdr->whereBetween('start', [new Carbon($request->values['time'][0]), new Carbon($request->values['time'][1])]);
            }
            if(isset($request->values['src']) && $request->values['src'] !== '') {
                $cdr->where('src', $request->values['src']);
            }
            if(isset($request->values['dst']) && $request->values['dst'] !== '') {
                $cdr->where('dst', $request->values['dst']);
            }
            return response()->json([ 'data'=> $cdr->orderBy('end','desc')->get(), 'paginateReport'=> $cdr->orderBy('end','desc')->paginate($request->pagination['pageSize'] ?? 10)]);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    public function minutesOfMeeting(): \Illuminate\Http\JsonResponse
    {
        $data= DB::select("SELECT dst Unique_Party_B,COUNT(*) Total_Call_Attempts,SUM(IF(disposition ='ANSWERED',1,0)) Answered_Calls,SUM(IF(disposition = 'ANSWERED',duration,0)) Total_Talk_Time FROM cdr WHERE accountcode='Outbound' AND disposition IN ('ANSWERED','NO ANSWER') AND START>= CURDATE() AND END<= NOW() GROUP BY dst");
        return response()->json($data);
    }

    public function minutesOfMeetingFiltered(Request $request): \Illuminate\Http\JsonResponse
    {
        $data = DB::select("SELECT dst Unique_Party_B,COUNT(*) Total_Call_Attempts,SUM(IF(disposition ='ANSWERED',1,0)) Answered_Calls,SUM(IF(disposition = 'ANSWERED',duration,0)) Total_Talk_Time FROM cdr WHERE accountcode='Outbound' AND disposition IN ('ANSWERED','NO ANSWER') AND START >= '" . Carbon::parse($request->range[0])->timezone("Asia/Karachi") . "' AND END<= '" . Carbon::parse($request->range[1])->timezone("Asia/Karachi") . "' GROUP BY dst");
        return response()->json($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
