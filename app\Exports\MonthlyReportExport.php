<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class MonthlyReportExport implements FromQuery,WithHeadings,WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */
    function __construct($date) {
        $this->date = $date;
        
    }
    public function query()
    {
        $cdr = DB::table('queue_log')->where('time', 'LIKE', "{$this->date}%")
        ->groupBy(DB::raw('Date(time)'))
        ->orderBy(DB::raw('Date(time)'))
        ->select(
            DB::raw("
            SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
            SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
            SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) as totalAnswer20Sec,
            SUM(CASE WHEN (Event = 'CONNECT' AND data1 >= 20) THEN 1 ELSE 0 END) as totalAnswerAfter20Sec,
            SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
            SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalCallsInQueue,
            SUM(CASE WHEN (Event = 'ABANDON' AND data3 <=30) THEN 1 ELSE 0 END) as totalAbandon30Sec,
            SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) as totalAbandonAfter30Sec,
            IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),100) AS GOS,
            IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),0) AS ACR,
            Date(time) AS 'Date'
            ")
        );
        return $cdr;
    }
    public function map($cdr): array
     {
        
        return[
            $cdr->Date,
            $cdr->totalInbounCalls,
            $cdr->totalAnswerCalls,
            $cdr->totalAnswer20Sec,
            $cdr->totalAnswerAfter20Sec,
            $cdr->totalAbandonCalls,
            $cdr->totalCallsInQueue,
            $cdr->totalAbandon30Sec,
            $cdr->totalAbandonAfter30Sec,
            $cdr->GOS,
            $cdr->ACR

        ];
     }
     public function headings(): array
    { return[
        'Date',
        'Total Inbound Calls',
        'Total Ans. Calls',
        'Ans. In 20 Sec',
        'Ans. After 20 Sec',
        'Total Abandoned',
        'Abandoned In 30 Sec',
        'Abandoned After 30 Sec',
        'Total In Queued',
        'GOS',
        'ASR'
    ];
    }
}
