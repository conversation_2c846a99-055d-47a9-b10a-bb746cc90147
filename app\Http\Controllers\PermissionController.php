<?php

namespace App\Http\Controllers;

use App\Models\Extension;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Models\Role;
use App\Models\Permission;

class PermissionController extends Controller
{
    public function __construct() {

        $this->middleware('permission:create_permissions', ['only' => ['store']]);
        $this->middleware('permission:update_permissions', ['only' => ['update']]);
        $this->middleware('permission:delete_permissions', ['only' => ['destroy']]);
        $this->middleware('permission:read_permissions', ['only' => ['index']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        $permissions = Permission::query()->get();
        $modules = [];
        $model=[];
        foreach ($permissions as $permission) {
            $perm = explode("_", $permission->name);
            $modules[$perm[1]][] = $perm[0]." ".$permission->id;
        }

        foreach ($modules as $key => $value)
        {
            $id=[];
            $per=[];
            foreach ($value as $permission)
            {
                $data = explode(' ', $permission);
                $id[]= $data[1];
                $per[] = $data[0];
            }
//            $object = (object) ['name' => $key , 'permissions' =>  $per];
            $object = (object) ['name' => $key , 'permissions' =>  $per, 'id' => $id];
            $model[] = $object;
        }
        return response()->json($model);
    }

    public function getpermissionsInRole()
    {
        return response()->json(Permission::query()->get());
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    // public function store(Request $request)
    // {
    //         $status=Permission::create($request->all());
    //         if($status->name !="")
    //             return response()->json("Permission {$status->name} has been created.");
    //         else
    //             return response()->json("Permission {$status->name} create has been failed.");
    // }

    public function store(Request $request)
    {   
        $name = $request->name;
    
        // First check if permission already exists
        $existingPermission = Permission::where('name', $name)->first();
        
        if ($existingPermission) {
            return response()->json("Permission '{$name}' already exists.");
        }
    
        $cleanName = str_replace('_', ' ', $name);
        
        $words = explode(' ', $cleanName);
        $words[0] = ucfirst($words[0]);
        $words[count($words) - 1] = ucfirst($words[count($words) - 1]); 
        
        $displayName = implode(' ', $words);
        
        $description = "{$displayName}"; 
    
        $status = Permission::create([
            'name' => $name, 
            'display_name' => $displayName,
            'description' => $description, 
        ]);
    
        if($status) {
            return response()->json("Permission '{$status->name}' has been created successfully.");
        } else {
            return response()->json("Failed to create permission '{$name}'.", 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Permission $permission
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request,Permission $permission)
    {
        $request->validate([
            'name' => ['required', 'string', Rule::unique('permissions', 'name')->ignoreModel($permission)]
        ]);
        $status = $permission->update($request->all());
        if($status)
            return response()->json("Permission {$permission->name} has been updated.");
        else
            return response()->json("Permission {$permission->name} update has been failed.", 400);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Permission $permission)
    {
//        return response()->json($permission);
        $status = $permission->delete();
        if($status)
            return response()->json("Permisison {$permission->name} has been deleted.");
        else
            return response()->json("Permission {$permission->name} delete has benn failed.");

    }
}
