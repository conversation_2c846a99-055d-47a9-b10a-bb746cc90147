<?php

namespace App\Console\Commands;

use App\Http\Traits\BitrixIntegrationTrait;
use App\Models\SystemSetting;
use Carbon\Carbon;
use Illuminate\Console\Command;
use PAMI\AsyncAgi\AsyncClientImpl;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\MixMonitorAction;
use PAMI\Message\Action\PJSIPShowEndpointAction;
use PAMI\Message\Action\RedirectAction;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\EventMessage;

class CustomRouting extends Command
{
    use BitrixIntegrationTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'start:custom-routing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'The command will start a daemon that will listen to incoming calls and route to agents if there is any lead of them in their bitrix.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    protected function getAMIOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = Carbon::now();
        $this->info("[{$date->toDateTimeString()}] INFO: Starting custom call routing invoke script...");
        $client = new ClientImpl($this->getAMIOptions());
        $client->registerEventListener(function (EventMessage $event) use ($client, $date) {
            $agiClient = new AsyncClientImpl([
                'pamiClient' => $client,
                'asyncAgiEvent' => $event
            ]);
            $var = $agiClient->getChannelVariables();
            $arg = $var->getArgument(1);
            $callerId = $var->getCallerId();
            $uniqueId = $var->getUniqueId();
            $this->info("[{$date->toDateTimeString()}] INFO: Incoming call from $callerId");
            if($arg === 'custom-routing') {
                $agent = $this->customCallRouting($callerId);
                $this->info("[{$date->toDateTimeString()}] INFO: Found agent: $agent");
                if($agent) {
                    $action = new PJSIPShowEndpointAction($agent);
                    try {
                        $response = $client->send($action);
                        if($response->getKey('response') !== 'Error') {
                            $deviceState = $response->getEvents()[0]->getKey('DeviceState');
                            if($deviceState === "Not in use") {
                                $channel = $var->getChannel();
                                $cdr = $agiClient->getCDR();
                                $exten = $cdr->getDestination();
                                $date = Carbon::now();
                                $fileName = "custom_routing-$exten-$callerId-{$date->format('Ymd-His')}-$uniqueId.wav";
                                $fileLoc = "{$date->format('Y')}/{$date->format('m')}/{$date->format('d')}/$fileName";
                                $cdr->setAccountCode("Custom_Routing");
                                $cdr->setCustom('recordingfile', $fileName);
                                $recordAction = new MixMonitorAction($channel);
                                $recordAction->setOptions(['b']);
                                $recordAction->setFile($fileLoc);
                                $action = new RedirectAction($channel, $agent, "default", 1);
                                $response = $client->send($action);
                                $response = $client->send($recordAction);
                                $date = Carbon::now();
                                $this->info("[{$date->toDateTimeString()}] INFO: [$callerId] Call routed to designated user @ {$agent}...");
                            }
                        }
                    } catch (\Exception $exception) {
                        $this->info("[{$date->toDateTimeString()}] INFO: Exception occurred: {$exception->getMessage()}");
                    }
                }
            }
            $agiClient->break();
        }, function ($event) use ($client) {
            return $event instanceof AsyncAGIStartEvent;
        });

        $client->open();

        while (true) {
            $client->process();
        }
    }
}
