<?php

namespace App\Http\Controllers;

use App\Models\Inbound;
use App\Models\IVR;
use App\Models\Queue;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\In;

class InboundController extends Controller
{

    public function __construct() {

        $this->middleware('permission:create_inbounds', ['only' => ['create']]);
        $this->middleware('permission:update_inbounds', ['only' => ['update']]);
        $this->middleware('permission:delete_inbounds', ['only' => ['destroy']]);
        $this->middleware('permission:read_inbounds', ['only' => ['index']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        $inbound = Inbound::with('module')->get();
        $inbound->transform(function($item, $key) {
            $item->module_type = explode("\\", $item->module_type)[2];
            return $item;
        });
        return response()->json($inbound);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $request->validate([
                'module_type' => ['required', 'string'],
                'module_id' => ['required', 'integer'],
                'number' => ['required', 'string', 'unique:inbounds,number']
            ]);
            $type = $request->module_type;
            //return response()->json($request->all());
            $inbound = new Inbound;
            $inbound->number = $request->number;
            $status = 0;
            switch ($type) {
                case 'Queue':
                    $queue = Queue::query()->findOrFail($request->module_id);
                    $status = $inbound->module()->associate($queue);
                    $inbound->save();
                    break;
                case 'IVR':
                    $ivr = IVR::query()->findOrFail($request->module_id);
                    $status = $inbound->module()->associate($ivr);
                    $inbound->save();
                    break;
                default:
                    break;
            }
            return response()->json("Inbound route for {$inbound->number} has been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Inbound  $inbound
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Inbound $inbound): \Illuminate\Http\JsonResponse
    {
        try {
            $request->validate([
                'module_type' => ['required', 'string'],
                'module_id' => ['required', 'integer'],
                'number' => ['required', 'string', Rule::unique('inbounds', 'number')->ignoreModel($inbound, 'id')]
            ]);
            $type = $request->module_type;
            $inbound->number = $request->number;
            $status = 0;
            $model = null;
            switch ($type) {
                case 'Queue':
                    $model = Queue::query()->findOrFail($request->module_id);
                    $inbound->module()->associate($model);
                    $status = $inbound->save();
                    break;
                case 'IVR':
                    $model = IVR::query()->findOrFail($request->module_id);
                    $inbound->module()->associate($model);
                    $status = $inbound->save();
                    break;
                default:
                    break;
            }
            return response()->json("Inbound route for {$inbound->number} has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Inbound  $inbound
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Inbound $inbound): \Illuminate\Http\JsonResponse
    {
        try {
            $inbound->delete();
            return response()->json("Inbound route for {$inbound->number} has been deleted.");
        }
        catch (\Exception $e) {
            return response()->json($e->getMessage(), 400);
        }
    }
}
