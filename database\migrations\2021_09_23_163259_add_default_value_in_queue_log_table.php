<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// class AddDefaultValueInQueueLogTable extends Migration
return new class extends Migration

{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('queue_log', function (Blueprint $table) {
            $table->string('time')->default('')->change();
            $table->string('callid')->default('')->change();
            $table->string('queuename')->default('')->change();
            $table->string('Agent')->default('')->change();
            $table->string('Event')->default('')->change();
            $table->string('data')->default('')->change();
            $table->string('data1')->default('')->change();
            $table->string('data2')->default('')->change();
            $table->string('data3')->default('')->change();
            $table->string('data4')->default('')->change();
            $table->string('data5')->default('')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('queue_log', function (Blueprint $table) {
            //
        });
    }
};
