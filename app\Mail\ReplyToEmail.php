<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ReplyToEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $replyBody;
    public $originalMessageId;
    public $subject;
    public $attachments;

    public function __construct($replyBody, $originalMessageId, $subject = null, $attachments = [])
    {
        $this->replyBody = $replyBody;
        $this->originalMessageId = $originalMessageId;
        $this->subject = $subject ? 'Re: ' . $subject : 'Re: Your message';
        $this->attachments = $attachments;

        Log::info("ReplyToEmail Mailable initialized.", [
            'subject' => $this->subject,
            'originalMessageId' => $this->originalMessageId,
            'attachments_count' => count($attachments)
        ]);
    }

    public function build()
    {
        Log::info("Building email message...");

        $email = $this->from('<EMAIL>')
            ->subject($this->subject)
            ->html($this->replyBody);

        // Attachments log
        if (!empty($this->attachments)) {
            foreach ($this->attachments as $attachment) {
                Log::info("Attaching file to email.", [
                    'file' => $attachment['file'],
                    'name' => $attachment['options']['as'] ?? 'unknown',
                    'mime' => $attachment['options']['mime'] ?? 'unknown'
                ]);
                $email->attach(
                    $attachment['file'],
                    $attachment['options']
                );
            }
        } else {
            Log::info("No attachments to include in the email.");
        }

        // Add reply headers
        if ($this->originalMessageId) {
            Log::info("Adding 'In-Reply-To' and 'References' headers.", [
                'originalMessageId' => $this->originalMessageId
            ]);

            $email->withSwiftMessage(function ($message) {
                $message->getHeaders()
                    ->addTextHeader('In-Reply-To', $this->originalMessageId);
                $message->getHeaders()
                    ->addTextHeader('References', $this->originalMessageId);
            });
        } else {
            Log::info("No originalMessageId provided. Skipping reply headers.");
        }

        Log::info("Email build completed.");
        return $email;
    }
}
