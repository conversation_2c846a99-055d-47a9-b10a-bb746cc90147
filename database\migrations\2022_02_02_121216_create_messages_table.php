<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
class CreateMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Schema::create('messages', function (Blueprint $table) {
        //     $table->id();
        //     $table->string('message');
        //     $table->foreignId('user_id')
        //         ->constrained('users')
        //         ->onUpdate('cascade')
        //         ->onDelete('cascade');
        //     $table->string('ip_address');
        //     $table->string('status');
        //     $table->index('conversation_id');
        //     $table->foreignId('conversation_id')
        //         ->constrained('conversations')
        //         ->onUpdate('cascade')
        //         ->onDelete('cascade');;
        //     $table->timestamps();
        // });
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message');
            $table->foreignId('user_id')
                ->constrained('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('ip_address')->nullable(); 
            $table->enum('status', ['sent', 'delivered', 'read']); 
            $table->foreignId('conversation_id')
                ->constrained('conversations')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->timestamps();
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('messages');
    }
}