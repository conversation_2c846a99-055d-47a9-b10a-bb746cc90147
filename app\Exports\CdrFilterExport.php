<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class CdrFilterExport implements FromArray, WithHeadings, WithMapping
{
    protected array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        return $this->data;
    }

    public function map($data): array
    {
        return [
            $data['name'] ?? '',
            $data['uniqueid'] ?? '',
            $data['recordingfile'] ?? '',
            $data['accountcode'] ?? '',
            $data['src'] ?? '',
            $data['dst'] ?? '',
            $data['channel'] ?? '',
            $data['dstchannel'] ?? '',
            $data['disposition'] ?? '',
            $data['duration'] ?? '',
            $data['transcription'] ?? '',
            $data['remark'] ?? '',
            $data['start'] ?? '',
            $data['end'] ?? '',
        ];
    }

    public function headings(): array
    {
        return [
            'Name',
            'Unique ID',
            'Recording File Name',
            'Call Type',
            'Source',
            'Destination',
            'Channel',
            'Dst Channel',
            'Call Status',
            'Duration',
            'Transcription',
            'Remark',
            'Start',
            'End',
        ];
    }
}
