<?php

namespace App\Traits;

use App\Models\PrepaidSetting;
use Illuminate\Support\Facades\DB;

trait PrepaidTrait {

// this for saving only 

    public function billSum()
    {
        $prepaid = PrepaidSetting::where('type' , 'prepaid')->first();
        if(!$prepaid){
            return false;
        }
        $calls = DB::table('cdr')->where([
            ['disposition', '=', 'ANSWERED'],
            ['answer', '>=', $prepaid->pkg_update]
        ])->pluck('billsec');

        $pulseDuration = $prepaid->pulse_duration;
        $tariff = $prepaid->tariff;
        $totalBillingSum = 0;

        foreach ($calls as $callDuration) {
            $numPulses = ceil($callDuration / $pulseDuration);
            $totalBillingSum += $numPulses * $tariff;
        }
        
        $prepaid->remaining_amount = $prepaid->amount - $totalBillingSum;
        $prepaid->save();
    }
}