<?php

namespace App\Http\Controllers;

use App\Exports\VoicemailExport;
use App\Models\Voicemail;
use App\Models\VoicemailFile;
use App\Models\VoicemailSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class VoicemailSettingController extends Controller
{
    public function getVoicemailAudio(Voicemail $voicemail): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        $fileLoc = $voicemail->fileLoc;
        return Storage::disk('voicemails')->download($fileLoc);
    }

    public function exportVoicemail(Request $request): \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
    {
        try {
            $query = Voicemail::query()->orderBy('created_at', 'desc');
            if($request->date)
                $query->whereDate('created_at', '>=', $request->date[0])
                    ->whereDate('created_at', '<=', $request->date[1]);
            if($request->number)
                $query->where('number', 'LIKE', "%$request->number%");
            return Excel::download(new VoicemailExport($query->get(['fileName', 'number', 'created_at'])), 'voicemails.xlsx');
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getVoicemails(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $query = Voicemail::query()->orderBy('created_at', 'desc');
            if($request->date)
                $query->whereDate('created_at', '>=', $request->date[0])
                    ->whereDate('created_at', '<=', $request->date[1]);
            if($request->number)
                $query->where('number', 'LIKE', "%$request->number%");
            return response()->json($query->paginate($request->perPage ?? 10));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getSettings(): \Illuminate\Http\JsonResponse
    {
        try {
            $settings = VoicemailSetting::query()->first();
            return response()->json($settings);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getVoiceMailFiles(): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(VoicemailFile::query()->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function uploadFile(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'file' => ['required', 'file', 'mimes:wav']
        ]);

        try {
            $status = $request->file('file')->storeAs('voicemail', $request->file('file')->getClientOriginalName(), 'sounds');
            if($status) {
                $model = new VoicemailFile;
                $model->name = $request->file('file')->getClientOriginalName();
                $model->path = $status;
                $model->fileloc = explode('.', $status)[0];
                $model->save();
            }
            return response()->json($status);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function updateSettings(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'weeks' => ['required'],
            'start' => ['required'],
            'end' => ['required'],
            'status' => ['required', 'boolean']
        ]);

        try {
            $settings = VoicemailSetting::query()->first();
            if ($settings) {
                $settings->update($request->all());
            } else {
                $settings = VoicemailSetting::create($request->all());
            }
            $settings->voicemail_file_id = $request->voicemailFile;
            $settings->save();
            return response()->json("Voicemail settings updated successfully.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
