<?php
namespace App\Models;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
class IvrMenuSetting extends Model
{
    use HasFactory;
    protected $fillable = ['start', 'end', 'weeks', 'queue'];
    protected $casts = ['weeks' => 'array'];
    public function setStartAttribute($value)
    {
        $this->attributes['start'] = Carbon::parse($value)->addHours(5);
    }
    public function setEndAttribute($value)
    {
        $this->attributes['end'] = Carbon::parse($value)->addHours(5);
    }
}