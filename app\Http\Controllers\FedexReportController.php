<?php

namespace App\Http\Controllers;

use App\Exports\AgentKPIReportExport;
use App\Exports\DailyReportExport;
use App\Exports\MonthlyForecastReportExport;
use App\Exports\MonthlyReportExport;
use App\Models\AgnetReport;
use Carbon\Carbon;
use DateInterval;
use DatePeriod;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Queue;
use App\Models\SystemSetting;
use App\Models\User;
use Illuminate\Support\Str;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Listener\IEventListener;
use PAMI\Message\Action\AgentsAction;
use PAMI\Message\Action\CoreShowChannelsAction;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Action\QueueSummaryAction;
use PAMI\Message\Event\CoreShowChannelEvent;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Event\QueueMemberEvent;
use PAMI\Message\Event\QueueParamsEvent;
use PAMI\Message\Event\QueueSummaryEvent;
use stdClass;

class FedexReportController extends Controller
{
    //
    public function getDailyTrendReport(Request $request)
    {

        $sum = 0;
        $counter = 0;
        $CURRENTDATE = Carbon::now()->format('Y-m-d');
        if ($CURRENTDATE == $request->date) {
            $cdr = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND Date(time) = '$request->date' "))
                ->whereRaw('CURRENT_TIME()>hour')
                ->groupBy(DB::raw('hour'))
                ->orderBy('hour')
                ->select(
                    DB::raw("
        Date(time) as Date,
        hour AS 'to', 
        AddTime(hour, '00:30:00') AS 'from' ,
        SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
        SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
        SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) as totalAnswer20Sec,
        SUM(CASE WHEN (Event = 'CONNECT' AND data1 >= 20) THEN 1 ELSE 0 END) as totalAnswerAfter20Sec,
        SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
        SUM(CASE WHEN (Event = 'ABANDON' AND data3 <=30) THEN 1 ELSE 0 END) as totalAbandon30Sec,
        SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) as totalAbandonAfter30Sec,
        SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalCallsInQueue,
        IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),100) AS GOS,
        IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),0) AS ACR,
        SUM(CASE WHEN (Event = 'ADDMEMBER') THEN 1 ELSE 0 END) -SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN 1 ELSE 0 END) AS agentLogin,
        SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END)  AS shortage")
                )
                ->get();
        } else {
            $cdr = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND Date(time) = '$request->date' "))
                ->groupBy(DB::raw('hour'))
                ->orderBy('hour')
                ->select(
                    DB::raw("
        Date(time) as Date,
        hour AS 'to', 
        AddTime(hour, '00:30:00') AS 'from' ,
        SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
        SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
        SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) as totalAnswer20Sec,
        SUM(CASE WHEN (Event = 'CONNECT' AND data1 >= 20) THEN 1 ELSE 0 END) as totalAnswerAfter20Sec,
        SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
        SUM(CASE WHEN (Event = 'ABANDON' AND data3 <=30) THEN 1 ELSE 0 END) as totalAbandon30Sec,
        SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) as totalAbandonAfter30Sec,
        SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalCallsInQueue,
        IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),100) AS GOS,
        IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),0) AS ACR,
        SUM(CASE WHEN (Event = 'ADDMEMBER') THEN 1 ELSE 0 END) -SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN 1 ELSE 0 END) AS agentLogin,
        SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END)  AS shortage")
                )
                ->get();
        }


        $CURRENTTIME = Carbon::now()->format('H');


        foreach ($cdr as  &$cd) {

            $sum = (int)$cd->agentLogin + $sum;
            if ($sum < 0) {
                $cd->agentLogin = 0;
            }

            if ((int)$CURRENTTIME < (int)substr($cd->from, 0, 2) and $CURRENTDATE == $request->date  or $sum < 0) {

                $cd->agentLogin = 0;
            } else {
                $cd->agentLogin = $sum;
            }


            if ($cd->shortage != 0) {
                $shortage =   $cd->shortage - $sum;
                $cd->shortage = $shortage;
            }

            if ($cd->Date == NULL) {
                $cd->Date = $request->date;
            }
        }
        return response()->json($cdr);
    }
    public function getMonthlyTrendReport(Request $request)
    {
        $cdr = DB::table('queue_log')->where('time', 'LIKE', "{$request->month}%")
            ->groupBy(DB::raw('Date(time)'))
            ->select(
                DB::raw("
                Date(time),
                SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) as totalAnswer20Sec,
                SUM(CASE WHEN (Event = 'CONNECT' AND data1 >= 20) THEN 1 ELSE 0 END) as totalAnswerAfter20Sec,
                SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalCallsInQueue,
                SUM(CASE WHEN (Event = 'ABANDON' AND data3 <=30) THEN 1 ELSE 0 END) as totalAbandon30Sec,
                SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) as totalAbandonAfter30Sec,
                IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),100) AS GOS,
                IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),0) AS ACR
        ")
            )->get();


        return response()->json($cdr);
    }

    public function getAgentKPIReport(Request $request)
    { 
        $user = DB::table('users')->where('name', $request->agent)->first('auth_username');
        
        $auth_username = "PJSIP/{$user->auth_username}";
    
        $username = DB::table('users')->where('auth_username', $user->auth_username)->first('name');
         $agentName =$username->name;
            $records = DB::select("Select 
            Date(time) as date,
            Agent,
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event = 'ADDMEMBER') THEN time_to_sec(time) ELSE 0 END)),1,8) As 'totalLoginTime',
                 SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event = 'ADDMEMBER') THEN time_to_sec(time) ELSE 0 END)),1,8) As 'totalSiginTime',
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event='UNPAUSE') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event='PAUSE') THEN time_to_sec(time) ELSE 0 END)),1,8) as totalBreakTime,
            SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalCalls,
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)),1,8) as totalTalkTime,
            SUBSTRING(SEC_TO_TIME(IFNULL((SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data1 ELSE 0 END) 
            + SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)) /SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END),0)),1,8) as AHT
            FROM queue_log
            WHERE (Agent = '$request->agent' OR Agent = '$auth_username')
            AND DATE(time) BETWEEN '$request->from' AND '$request->to' GROUP BY DATE(time)");
            foreach ($records as &$agent) {
            # code...
            // dd($agent->Agent);
            if($agent->Agent == $auth_username)
            {
                $agent->Agent= $agentName;
            }
            if ($agent->totalLoginTime < 0) {
                $agent->totalLoginTime = "00:00:00";
            } else {
                $loginTime = new DateTime($agent->totalLoginTime);
                $breakTime = new DateTime($agent->totalBreakTime);
                $totalSiginTime = $breakTime->diff($loginTime);
                $time = $totalSiginTime->format('%h:%i:%s');
            }
            if ($agent->totalBreakTime < 0) {
                # code...
                $agent->totalBreakTime = "00:00:00";
            }
            if ($agent->totalSiginTime < 0) {
                # code...
                $agent->totalSiginTime = "00:00:00";
            } else {
                $agent->totalSiginTime = $time;
            }
        }
          
       
        return response()->json($records);
    }
    public function getForecastReport(Request $request)
    {


        $begin = new DateTime(".$request->from.");
        $end = new DateTime(".$request->to.");
        $end->setTime(0, 0, 1);
        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod($begin, $interval, $end);
        $report = array();
        $newArray = array();
        $avgArray = array();
        $sumOfCalls = 0;
        $count = 0;

        foreach ($period as $dt) {
            $count++;
            $date = $dt->format("Y-m-d");
            $dayName = $dt->format('l');
            $reports = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND DATE(time) = '$date'  "))
                ->whereBetween(DB::raw('hour'), ['08:00:00', '23:30:00'])
                ->groupBy(DB::raw("DATE(time),hour"))
                ->orderBy(DB::raw('hour'))
                ->select(
                    DB::raw("Date(time) AS Date,DayName(Date(time)) As Day,hour AS 'Hour',SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls")
                )
                ->get();

            foreach ($reports as $key => $newReport) {

                $sumOfCalls =  $newReport->totalInbounCalls + $sumOfCalls;
                $newArray[$key][$newReport->Hour] = $newReport->totalInbounCalls;
            }
            $report[] = ['totalInbounCalls' => $sumOfCalls, 'Date' => $date, 'Day' => $dayName, 'hourly' => $newArray];
            $sumOfCalls = 0;
        }
        $averages = DB::table('queue_log')
            ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND DATE(time) Between '$request->from' AND '$request->to' "))
            ->whereBetween(DB::raw('hour'), ['08:00:00', '23:30:00'])
            ->groupBy(DB::raw("hour"))
            ->orderBy(DB::raw('hour'))
            ->select(
                DB::raw("hour AS 'Hour',SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as avg")
            )
            ->get();


        foreach ($averages as $key => $average) {
            $avgArray[$key][$average->Hour] = round($average->avg / $count);
        }


        return response()->json([$report, $avgArray]);
    }
    public function export(Request $request)
    {

        $begin = new DateTime(".$request->from.");
        $end = new DateTime(".$request->to.");
        $end->setTime(0, 0, 1);
        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod($begin, $interval, $end);
        $report = array();
        $newArray = array();
        $avgArray = array();
        $sumOfCalls = 0;
        $count = 0;

        foreach ($period as $dt) {
            $count++;
            $date = $dt->format("Y-m-d");
            $dayName = $dt->format('l');
            $reports = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND DATE(time) = '$date'  "))
                ->whereBetween(DB::raw('hour'), ['08:00:00', '23:30:00'])
                ->groupBy(DB::raw("DATE(time),hour"))
                ->orderBy(DB::raw('hour'))
                ->select(
                    DB::raw("Date(time) AS Date,DayName(Date(time)) As Day,hour AS 'Hour',SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls")
                )
                ->get();

            foreach ($reports as $key => $newReport) {

                $sumOfCalls =  $newReport->totalInbounCalls + $sumOfCalls;
                $newArray[$newReport->Hour] = $newReport->totalInbounCalls;
            }
            // dd($newArray['08:00:00']);
            $report[] = ['totalInbounCalls' => $sumOfCalls, 'Date' => $date, 'Day' => $dayName, 'hourly' => $newArray];
            $sumOfCalls = 0;
        }

        return Excel::download(new MonthlyForecastReportExport($report), 'Monthly Forecast Report.xlsx');
    }
    public function exportAgengKPIReport(Request $request)
    {   
        
        $user = DB::table('users')->where('name', $request->agent)->first('auth_username');
        
        $auth_username = "PJSIP/{$user->auth_username}";
    
        $username = DB::table('users')->where('auth_username', $user->auth_username)->first('name');
         $agentName =$username->name;
        $records = DB::select("Select 
            Date(time) as date,
            Agent,
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event = 'ADDMEMBER') THEN time_to_sec(time) ELSE 0 END)),1,8) As 'totalLoginTime',
                 SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event = 'ADDMEMBER') THEN time_to_sec(time) ELSE 0 END)),1,8) As 'totalSiginTime',
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event='UNPAUSE') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event='PAUSE') THEN time_to_sec(time) ELSE 0 END)),1,8) as totalBreakTime,
            SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalCalls,
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)),1,8) as totalTalkTime,
            SUBSTRING(SEC_TO_TIME(IFNULL((SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data1 ELSE 0 END) 
            + SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)) /SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END),0)),1,8) as AHT
            FROM queue_log
            WHERE (Agent = '$request->agent' OR Agent = '$auth_username')
            AND DATE(time) BETWEEN '$request->from' AND '$request->to'
            ");
            foreach ($records as &$agent) {
            # code...
            // dd($agent->Agent);
            if($agent->Agent == $auth_username)
            {
                $agent->Agent= $agentName;
            }
            if ($agent->totalLoginTime < 0) {
                $agent->totalLoginTime = "00:00:00";
            } else {
                $loginTime = new DateTime($agent->totalLoginTime);
                $breakTime = new DateTime($agent->totalBreakTime);
                $totalSiginTime = $breakTime->diff($loginTime);
                $time = $totalSiginTime->format('%h:%i:%s');
            }
            if ($agent->totalBreakTime < 0) {
                # code...
                $agent->totalBreakTime = "00:00:00";
            }
            if ($agent->totalSiginTime < 0) {
                # code...
                $agent->totalSiginTime = "00:00:00";
            } else {
                $agent->totalSiginTime = $time;
            }
        }
        //   dd(gettype($records));
       
        return Excel::download(new AgentKPIReportExport($records), 'Agent KPI Report.xlsx');
    }

    public function ExportMonthlyTrendReport(Request $request)
    {
        return Excel::download(new MonthlyReportExport($request->month), 'Monthly Trend Report.xlsx');
    }
    public function ExportDailyTrendReport(Request $request)
    {

        return Excel::download(new DailyReportExport($request->date), 'Daily Trend Report.xlsx');
    }
    public function ExportAgentKPIReport(Request $request)
    {

        return Excel::download(new AgentKPIReportExport($request->from, $request->to, $request->agent), 'Agent KPI Report.xlsx');
    }
    public function ExportForecastReport(Request $request)
    {
        //    dd($request);
        return Excel::download(new MonthlyForecastReportExport($request->from, $request->to,), 'Monthly Forecast Report.xlsx');
    }
    public function dashBoardData()
    {
        $CURRENTDATE = Carbon::now()->format('Y-m-d');
        $CURRENTMONTH = Carbon::now()->format('Y-m');

        $queue =  '100';
        $client = new ClientImpl($this->getOptions());
        $action = new QueueStatusAction($queue);
        $action2 = new QueueSummaryAction($queue);
        try {
            $client->open();
            $response = $client->send($action);
            $response2 = $client->send($action2);
            $client->close();
            $events = $response->getEvents();
            $events2 = $response2->getEvents();
            $members = 0;
            $paused = 0;
            $busy = 0;
            $idle = 0;
            $data = [];

            $acrs = DB::table('queue_log')->where('time', 'LIKE', "{$CURRENTDATE}%")
                ->groupBy(DB::raw('Date(time)'))
                ->select(
                    DB::raw("
                    
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) 
                    / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),0) AS ACR,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) 
                    / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),100) AS GOS")
                )->get();
            $acrs->toArray();
            if ($acrs->count() > 0) {
                foreach ($acrs as $key => $acr) {
                    $data['dailyACR']['acr'] = $acr->ACR;
                    $data['dailyACR']['gos'] = $acr->GOS;
                }
            } else {

                $data['dailyACR']['acr'] = '0.00';
                $data['dailyACR']['gos'] = '100.00';
            }


            $monthlyStats = DB::table('queue_log')->where('time', 'LIKE', "{$CURRENTMONTH}%")
                ->groupBy(DB::raw('Month(time)'))
                ->select(
                    DB::raw("
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) 
                    / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),100) AS GOS,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) 
                    / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),0) AS ACR,
                    SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls")
                )->get();
            $monthlyStats->toArray();
            if ($monthlyStats->count() > 0) {
                # code...
                foreach ($monthlyStats as $key => $stat) {
                    $data['monthStats']['gos'] = $stat->GOS;
                    $data['monthStats']['acr'] = $stat->ACR;
                    $data['monthStats']['totalCalls'] = $stat->totalInbounCalls;
                    $data['monthStats']['totalAns'] = $stat->totalAnswerCalls;
                    $data['monthStats']['totalAbandon'] = $stat->totalAbandonCalls;
                }
            } else {
                $data['monthStats']['gos'] = '100.00';
                $data['monthStats']['acr'] = '0.00';
                $data['monthStats']['totalCalls'] = '0';
                $data['monthStats']['totalAns'] = '0';
                $data['monthStats']['totalAbandon'] = '0';
            }
            foreach ($events as $key => $event) {
                if ($event instanceof QueueParamsEvent) {
                    $data['queue_params'] = $event->getKeys();
                    $data['queue_params']['totalcalls'] = (int)$event->getKey('completed') + (int)$event->getKey('abandoned');
                    $data['queue_params']['holdtime'] = gmdate("H:i:s", $event->getKey('holdtime'));
                    $data['queue_params']['talktime'] = gmdate("H:i:s", $event->getKey('talktime'));
                } elseif ($event instanceof QueueMemberEvent) {

                    $members++;

                    if ($event->getKey('paused') === "1") {
                        $paused++;
                    } elseif ($event->getKey('incall') === "1") {
                        $busy++;
                    } else {
                        $idle++;
                    }

                    $data['agents'][$key] = $event->getKeys();
                    $data['agents'][$key]['lastcall'] = $this->parse_time($event->getKey('lastcall'));
                    $data['agents'][$key]['lastpause'] = $this->parse_time($event->getKey('lastpause'));
                    $data['agents'][$key]['status'] = $this->parse_agent_state($event->getKey('status'));
                }
            }

            foreach ($events2 as $event2) {
                if ($event2 instanceof QueueSummaryEvent) {
                    $data['queue_params2'] = $event2->getKeys();
                    $data['queue_params2']['longestholdtime'] = gmdate("H:i:s", $event2->getKey('longestholdtime'));
                    $data['queue_params2']['total'] = $members;
                    $data['queue_params2']['paused'] = $paused;
                    $data['queue_params2']['idle'] = $idle;
                    $data['queue_params2']['busy'] = $busy;
                }
            }

            return response()->json($data);
        } catch (\Exception $exception) {

            return response()->json($exception->getMessage(), 500);
        }
    }
    private function getOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
        // return [
        //     'host' => '***************',
        //     'scheme' => 'tcp://',
        //     'port' => 5038,
        //     'username' => 'defaultapp',
        //     'secret' => 'randomsecretstring',
        //     'connect_timeout' => 10000,
        //     'read_timeout' => 10000
        // ];
    }
    private function parse_time($data)
    {
        return Carbon::createFromTimestamp($data)->format('d-m-Y h:i:s A');
    }
    private function parse_agent_state(int $state)
    {
        switch ($state) {
            default:
                return $state;
            case 0:
                return 'DEVICE_UNKNOWN';
            case 1:
                return 'DEVICE_NOT_INUSE';
            case 2:
                return 'DEVICE_INUSE';
            case 3:
                return 'DEVICE_BUSY';
            case 4:
                return 'DEVICE_INVALID';
            case 5:
                return 'DEVICE_UNAVAILABLE';
            case 6:
                return 'DEVICE_RINGING';
            case 7:
                return 'DEVICE_RINGINUSE';
            case 8:
                return 'DEVICE_ONHOLD';
        }
    }
}
