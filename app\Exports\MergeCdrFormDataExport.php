<?php

namespace App\Exports;

use App\Models\FormData;
use App\Models\FormField;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Support\Facades\Log;

class MergeCdrFormDataExport implements FromArray,WithMapping,WithHeadings
{
   
    protected $data;
    protected $form_id;
    
    public function __construct(array $record, $form_id)
    {   
        $this->data = $record;
        $this->form_id = $form_id;
        
    }
    public function array(): array
    {
        return $this->data;
        
    }

    public function map($data): array
    {  
        
        $keys = FormField::where('form_id', $this->form_id)->select('name')->get()->pluck('name')->toArray();
        $cdr_data = [$data['name'],$data['accountcode'] , $data['disposition'], $data['src'], $data['dst'],$data['start'],$data['end'],$data['duration'],   ];
        //$cdr_data = [$accountCode];
        $result = [];
        foreach($keys as $key)
        {   if(array_key_exists($key, $data['data'])) {
                if(is_array($data['data'][$key])){
                    
                    $result[] =  implode(" ; " , $data['data'][$key]);
                }else{
                    $result[] = $data['data'][$key];
                }
            }else {
                $result[] = '';
            }   
        }
        return [

            array_merge($cdr_data,$result )
        ];

    }

    public function headings(): array
    {
        
        $headings = FormField::where('form_id', $this->form_id)->select('label')->get()->pluck('label')->toArray();
        $fixed_headings = ['Agent Name','Call Type', 'Call Status', 'Source', 'Destination', 'Call Start', 'Call End', 'Call Duration'];
        return[
           array_merge($fixed_headings, $headings)
        ];
    }
}
