<?php

namespace Database\Seeders;

use App\Models\SystemSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SystemSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        SystemSetting::query()->upsert([
            ['key' => 'theme', 'value' => 'dark', 'type' => 'text'],
            ['key' => 'path', 'value' => '', 'type' => 'text'],
            ['key' => 'logo', 'value' => '', 'type' => 'text'],
            ['key' => 'server_address', 'value' => '127.0.0.1', 'type' => 'text'],
            ['key' => 'wss_port', 'value' => '8089', 'type' => 'text'],
            ['key' => 'manager_port', 'value' => '5038', 'type' => 'text'],
            ['key' => 'username', 'value' => 'defaultapp', 'type' => 'text'],
            ['key' => 'secret', 'value' => 'randomsecretstring', 'type' => 'password'],
            ['key' => 'connection_timeout', 'value' => '1000', 'type' => 'text'],
            ['key' => 'read_timeout', 'value' => '1000', 'type' => 'text'],
            ['key' => 'wss', 'value' => '***************', 'type' => 'text'],
            ['key' => 'outbound_string', 'value' => 'TCL-endpoint', 'type' => 'text'],
            ['key' => 'sms_url', 'value' => '', 'type' => 'text'],
            ['key' => 'userid', 'value' => '', 'type' => 'text'],
            ['key' => 'pwd', 'value' => '', 'type' => 'password'],
            ['key' => 'auto_call_answer', 'value' => 'false', 'type' => 'switch'],
            ['key' => 'enable_call_hangup', 'value' => 'false', 'type' => 'switch'],
        ],['key', 'value', 'type'], ['key', 'value','type']);
    }
}
