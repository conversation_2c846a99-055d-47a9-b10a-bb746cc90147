<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;
use App\Models\BitrixIntegration;
use App\Models\BitrixCallLog;
use Carbon\Carbon;



class IntegrationController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data = BitrixIntegration::first();
        return response()->json($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try{

            $data = BitrixIntegration::find($id);

            $data->api_key = $request->api_key;
            $data->is_enable = $request->is_enable;

            $data->save();

        }catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }


    //test function
    // public function attachRecording(Request $request) {

    //     $bitrix_call_id = $request->call_id;
    //     $recordingFileName = $request->fileName;
    //     $recordingFile = base64_encode($request->recording);
    //     $bitrix_id = $request->bitrix_id;
    //     $billsec = $request->billsec;
    //     // $response = Http::post("https://b24-yuhn1m.bitrix24.com/rest/1/eix4apa6o3jvxg2j/telephony.externalcall.finish",[
    //     //     'CALL_ID' => $bitrix_call_id,
    //     //     'USER_ID' => $bitrix_id,
    //     //     'DURATION' => $billsec,
    //     // ]);
    //     $response = Http::post("https://b24-yuhn1m.bitrix24.com/rest/1/eix4apa6o3jvxg2j/telephony.externalCall.attachRecord",[

    //         'CALL_ID' => $bitrix_call_id,
    //         'FILENAME'=> $recordingFileName,
    //         'FILE_CONTENT' => $recordingFile,
    //     ]);
    //     $response = $response->object();
    //     return response()->json($response);
    // }
}
