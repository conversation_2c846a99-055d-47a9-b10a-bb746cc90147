<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class back extends Mailable
{
    use Queueable, SerializesModels;

    public $replyBody;
    public $originalMessageId;
    public $subject;
    public $attachments;

    public function __construct($replyBody, $originalMessageId, $subject = null,$attachments = [])
    {
        $this->replyBody = $replyBody;
        $this->originalMessageId = $originalMessageId;
        $this->subject = $subject ? 'Re: ' . $subject : 'Re: Your message';
        $this->attachments = $attachments;
    }

    public function build()
    {
        $email = $this->from('<EMAIL>')
            ->subject($this->subject)
            ->html($this->replyBody);

            
            foreach ($this->attachments as $attachment) {
                $email->attach(
                    $attachment['file'],
                    $attachment['options']
                );
            }
            
        if ($this->originalMessageId) {
            $email->withSwiftMessage(function ($message) {
                $message->getHeaders()
                    ->addTextHeader('In-Reply-To', $this->originalMessageId);
                $message->getHeaders()
                    ->addTextHeader('References', $this->originalMessageId);
            });
        }

        return $email;
    }
}
