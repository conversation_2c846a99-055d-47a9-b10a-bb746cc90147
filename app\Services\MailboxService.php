<?php
namespace App\Services;

use Webklex\IMAP\Facades\Client;
use Illuminate\Support\Collection;

class MailboxService
{
    protected $client;

    public function __construct()
    {
        $this->client = Client::account('default'); // Uses config/imap.php
        $this->client->connect();
    }

    public function getEmails(int $page = 1, int $perPage = 5): array
    {
        $folder = $this->client->getFolder('INBOX');

        // Get all messages and build thread structure
        $allMessages = $folder->messages()->all()->get();

        // Build thread map
        $threads = [];
        foreach ($allMessages as $message) {
            $msgId = $message->getMessageId();
            $inReplyTo = $message->getHeader('in-reply-to')?->get('');
            $references = $message->getHeader('references')?->get('');

            // Determine thread ID
            $threadId = $inReplyTo ?: ($references ? explode(' ', $references)[0] : $msgId);

            if (!isset($threads[$threadId])) {
                $threads[$threadId] = [];
            }

            $threads[$threadId][] = [
                'message' => $message,
                'sort_key' => $message->getDate()->getTimestamp()
            ];
        }

        // Sort threads by most recent message
        $sortedThreads = collect($threads)->sortByDesc(function ($thread) {
            return max(array_column($thread, 'sort_key'));
        });

        // Paginate threads
        $total = $sortedThreads->count();
        $paginatedThreads = $sortedThreads->slice(($page - 1) * $perPage, $perPage);

        // Format response
        $emails = $paginatedThreads->map(function ($thread, $threadId) {
            // Sort messages within thread chronologically
            $sortedMessages = collect($thread)->sortBy('sort_key')->values();

            // First message is the parent
            $parent = $sortedMessages->first()['message'];

            // Format parent
            $parentData = [
                'subject' => $parent->getSubject(),
                'from' => $parent->getFrom()[0]->mail ?? '',
                'date' => $parent->getDate()->format('Y-m-d H:i:s'),
                'body' => $parent->getTextBody(),
                'attachment' => $parent->getAttachments()->map(function ($att) {
                    return [
                        'name' => $att->getName(),
                        'content' => base64_encode($att->getContent())
                    ];
                })->toArray(),
                'message_id' => $parent->getMessageId(),
                'references' => $parent->getHeader('references')?->get(''),
                'replies' => []
            ];

            // Format replies (remaining messages in thread)
            if ($sortedMessages->count() > 1) {
                $parentData['replies'] = $sortedMessages->slice(1)->map(function ($item) {
                    $msg = $item['message'];
                    return [
                        'subject' => $msg->getSubject(),
                        'from' => $msg->getFrom()[0]->mail ?? '',
                        'date' => $msg->getDate()->format('Y-m-d H:i:s'),
                        'body' => $msg->getTextBody(),
                        'attachment' => $msg->getAttachments()->map(function ($att) {
                            return [
                                'name' => $att->getName(),
                                'content' => base64_encode($att->getContent())
                            ];
                        })->toArray(),
                        'message_id' => $msg->getMessageId(),
                        'in_reply_to' => $msg->getHeader('in-reply-to')?->get(''),
                        'references' => $msg->getHeader('references')?->get('')
                    ];
                })->toArray();
            }

            return $parentData;
        })->values()->toArray();

        return [
            'emails' => $emails,
            'page' => (string)$page,
            'per_page' => (string)$perPage,
            'total' => (string)$total
        ];
    }

    public function getEmails_new_code(int $page = 1, int $perPage = 5): array
    {
        $folder = $this->client->getFolder('INBOX');

        // Fetch all messages first, then sort them manually using Laravel's Collection
        $allMessages = $folder->messages()->all()->get();
        $sortedMessages = $allMessages->sortByDesc(function ($message) {
            return $message->getDate()->get()->getTimestamp();
        })->values();

        // Manual pagination
        $total = $sortedMessages->count();
        $messages = $sortedMessages->slice(($page - 1) * $perPage, $perPage);

        $emails = $messages->map(function ($message) use ($allMessages) {
        $msgId = $message->getMessageId();

        // Replies logic (inner closure must also use $allMessages)
        $replies = $allMessages->filter(function ($m) use ($msgId) {
            $inReplyTo = $m->getHeader('in-reply-to')?->get('');
            $refs = $m->getHeader('references')?->get('');

            return ($inReplyTo && str_contains($inReplyTo, $msgId)) ||
                    ($refs && str_contains($refs, $msgId));
            })->map(function ($reply) use ($msgId) {
                return [
                    'subject'     => $reply->getSubject(),
                    'from'        => $reply->getFrom()[0]->mail ?? '',
                    'date'        => $reply->getDate()?->format('Y-m-d H:i:s'),
                    'body'        => $reply->getTextBody(),
                    'attachment'  => [],
                    'message_id'  => $reply->getMessageId(),
                    'thread_id'   => $msgId
                ];
            })->values()->all();

            return [
                'subject'    => $message->getSubject(),
                'from'       => $message->getFrom()[0]->mail ?? '',
                'date'       => $message->getDate()?->format('Y-m-d H:i:s'),
                'body'       => $message->getTextBody(),
                'attachment' => $message->getAttachments()->map(function ($att) {
                    return [
                        'name'   => $att->getName(),
                        'conent' => base64_encode($att->getContent())
                    ];
                })->values()->all(),
                'message_id' => $msgId,
                'replies'    => $replies
            ];
        })->values();


        return [
            'emails'    => $emails,
            'page'      => (string)$page,
            'per_page'  => (string)$perPage,
            'total'     => $total
        ];
    }
}


// namespace App\Services;

// class MailboxService
// {
//     private $inboxConnection;
//     private $sentConnection;

//     public function __construct()
//     {
//         // Connect to INBOX
//         $this->inboxConnection = @imap_open(
//             $this->getConnectionString('INBOX'),
//             config('mail.mailbox_username'),
//             config('mail.mailbox_password')
//         );

//         // Connect to Sent Mail
//         $this->sentConnection = @imap_open(
//             $this->getConnectionString('[Gmail]/Sent Mail'),
//             config('mail.mailbox_username'),
//             config('mail.mailbox_password')
//         );

//         if (!$this->inboxConnection) {
//             throw new \Exception('INBOX connection failed: ' . imap_last_error());
//         }

//         if (!$this->sentConnection) {
//             \Log::warning('Sent Mail connection failed: ' . imap_last_error());
//         }
//     }

//     private function getConnectionString(string $folder): string
//     {
//         return sprintf(
//             "{%s:%s/imap/%s}%s",
//             config('mail.mailbox_host'),
//             config('mail.mailbox_port'),
//             config('mail.mailbox_encryption'),
//             $folder
//         );
//     }

//     public function getEmails_bkp(int $page = 1, int $perPage = 10): array
//     {
//         // Step 1: Get paginated inbox emails only
//         $inboxUids = $this->getSortedUids($this->inboxConnection);
//         $totalInbox = count($inboxUids);

//         // Apply pagination to inbox emails only
//         $start = ($page - 1) * $perPage;
//         $paginatedInboxUids = array_slice($inboxUids, $start, $perPage);

//         // Step 2: Fetch full content for paginated inbox emails
//         $inboxEmails = [];
//         foreach ($paginatedInboxUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if ($email) {
//                 $inboxEmails[] = $email;
//             }
//         }

//         // Step 3: Get only relevant sent emails (matching references of inbox emails)
//         $inboxMessageIds = array_column($inboxEmails, 'message_id');
//         $sentEmails = [];

//         $sentUids = $this->getSortedUids($this->sentConnection);
//         foreach ($sentUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if (!$email) {
//                 continue;
//             }

//             if (!empty($email['references']) && in_array($email['references'], $inboxMessageIds)) {
//                 $sentEmails[] = $email;
//             }
//         }

//         // Step 4: Fetch all sent emails (for potential replies)
//         $sentEmails = [];
//         foreach ($sentUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if ($email) {
//                 $sentEmails[] = $email;
//             }
//         }

//         // Step 5: Group sent emails by thread for easy merging
//         $sentByReferences = [];
//         foreach ($sentEmails as $sentEmail) {
//             if (!empty($sentEmail['references'])) {
//                 $sentByReferences[$sentEmail['references']][] = $sentEmail;
//             }
//         }

//         // Step 6: Merge sent replies into their corresponding inbox threads
//         $threads = [];
//         foreach ($inboxEmails as $inboxEmail) {
//             $messageId = $inboxEmail['message_id'];
//             $thread = [$inboxEmail];

//             if (isset($sentByReferences[$messageId])) {
//                 $thread = array_merge($thread, $sentByReferences[$messageId]);
//             }

//             // Sort thread by date (optional)
//             usort($thread, fn($a, $b) => strtotime($a['date']) <=> strtotime($b['date']));

//             // Use inbox message_id as key
//             $threads[] = [
//                 'message_id' => $messageId,
//                 'date' => $inboxEmail['date'],
//                 'emails' => $thread,
//             ];
//         }

//         // Re-index array and maintain sorting
//         $threads = array_values($threads);
//         usort($threads, fn($a, $b) => strtotime($b['date']) - strtotime($a['date']));

//         $formattedEmails = [];

//         foreach ($threads as $thread) {
//             $mainEmail = $thread['emails'][0];

//             $formatted = [
//                 'subject' => $mainEmail['subject'],
//                 'from' => $mainEmail['from'],
//                 'date' => $mainEmail['date'],
//                 'body' => $mainEmail['body'],
//                 'attachment' => $mainEmail['attachment'],
//                 'message_id' => $mainEmail['message_id'],
//                 'replies' => [],
//             ];

//             // Add replies if any
//             if (count($thread['emails']) > 1) {
//                 foreach (array_slice($thread['emails'], 1) as $reply) {
//                     $formatted['replies'][] = [
//                         'subject' => $reply['subject'],
//                         'from' => $reply['from'],
//                         'date' => $reply['date'],
//                         'body' => $this->cleanEmailBody($reply['body']),
//                         'attachment' => $reply['attachment'],
//                         'message_id' => $reply['message_id'],
//                         'thread_id' => $mainEmail['message_id'],
//                     ];
//                 }
//             }

//             $formattedEmails[] = $formatted;
//         }

//         return [
//             'emails' => $formattedEmails,
//             'page' => (string)$page,
//             'per_page' => (string)$perPage,
//             'total' => $totalInbox
//         ];
//     }

//     public function getEmails_junaid(int $page = 1, int $perPage = 10): array
//     {
//         // Get all emails (both inbox and sent) first
//         $allEmails = [];

//         // Get inbox emails
//         $inboxUids = $this->getSortedUids($this->inboxConnection);
//         foreach ($inboxUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if ($email) {
//                 $allEmails[] = $email;
//             }
//         }

//         // Get sent emails if connection exists
//         if ($this->sentConnection) {
//             $sentUids = $this->getSortedUids($this->sentConnection);
//             foreach ($sentUids as $uidData) {
//                 $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//                 if ($email) {
//                     $allEmails[] = $email;
//                 }
//             }
//         }

//         // Sort all emails by date
//         usort($allEmails, function($a, $b) {
//             return strtotime($b['date']) <=> strtotime($a['date']); // Newest first
//         });

//         // Build complete thread structure
//         $threads = [];
//         foreach ($allEmails as $email) {
//             $messageId = $email['message_id'];
//             $references = $this->parseReferences($email['references'] ?? '');

//             // If this email has no references, it's a new thread
//             if (empty($references)) {
//                 if (!isset($threads[$messageId])) {
//                     $threads[$messageId] = [
//                         'root' => $email,
//                         'replies' => []
//                     ];
//                 }
//             } else {
//                 $addedToThread = false;

//                 // Check all references to find where this email belongs
//                 foreach ($references as $ref) {
//                     if (isset($threads[$ref])) {
//                         // Add to existing thread
//                         $threads[$ref]['replies'][] = $email;
//                         $addedToThread = true;
//                         break;
//                     }
//                 }

//                 // If not added to any existing thread, create new thread with first reference as root
//                 if (!$addedToThread) {
//                     $rootRef = $references[0];
//                     $threads[$rootRef] = [
//                         'root' => null, // We might not have the root message yet
//                         'replies' => [$email]
//                     ];
//                 }
//             }
//         }

//         // Now fill in any missing root messages
//         foreach ($threads as $threadId => &$thread) {
//             if (is_null($thread['root'])) {
//                 // Try to find the root message in all emails
//                 foreach ($allEmails as $email) {
//                     if ($email['message_id'] === $threadId) {
//                         $thread['root'] = $email;
//                         break;
//                     }
//                 }
//             }
//         }
//         unset($thread); // Break the reference

//         // Now process only inbox emails for pagination
//         $inboxMessageIds = array_column(
//             array_filter($allEmails, fn($e) => !$e['is_sent']),
//             'message_id'
//         );
//         $totalInbox = count($inboxMessageIds);

//         // Get only threads that have an inbox email as root
//         $inboxThreads = array_filter($threads, function($thread) use ($inboxMessageIds) {
//             return $thread['root'] && in_array($thread['root']['message_id'], $inboxMessageIds);
//         });

//         // Apply pagination
//         $paginatedThreads = array_slice($inboxThreads, ($page - 1) * $perPage, $perPage);

//         // Format the output with proper nesting
//         $formattedEmails = [];
//         foreach ($paginatedThreads as $thread) {
//             $formatted = [
//                 'subject' => $thread['root']['subject'],
//                 'from' => $thread['root']['from'],
//                 'date' => $thread['root']['date'],
//                 'body' => $thread['root']['body'],
//                 'attachment' => $thread['root']['attachment'],
//                 'message_id' => $thread['root']['message_id'],
//                 'replies' => [],
//             ];

//             // Sort replies by date
//             usort($thread['replies'], function($a, $b) {
//                 return strtotime($a['date']) <=> strtotime($b['date']);
//             });

//             // Build reply tree
//             $replyMap = [];
//             foreach ($thread['replies'] as $reply) {
//                 $replyMap[$reply['message_id']] = $reply;
//             }

//             foreach ($thread['replies'] as $reply) {
//                 $replyReferences = $this->parseReferences($reply['references'] ?? '');
//                 $parentId = end($replyReferences); // Get most recent reference

//                 if ($parentId === $thread['root']['message_id']) {
//                     // Direct reply to root
//                     $formatted['replies'][] = [
//                         'subject' => $reply['subject'],
//                         'from' => $reply['from'],
//                         'date' => $reply['date'],
//                         'body' => $this->cleanEmailBody($reply['body']),
//                         'attachment' => $reply['attachment'],
//                         'message_id' => $reply['message_id'],
//                         'thread_id' => $thread['root']['message_id'],
//                     ];
//                 } else if (isset($replyMap[$parentId])) {
//                     // Reply to another reply - handled by client-side threading
//                     $formatted['replies'][] = [
//                         'subject' => $reply['subject'],
//                         'from' => $reply['from'],
//                         'date' => $reply['date'],
//                         'body' => $this->cleanEmailBody($reply['body']),
//                         'attachment' => $reply['attachment'],
//                         'message_id' => $reply['message_id'],
//                         'thread_id' => $thread['root']['message_id'],
//                     ];
//                 }
//             }

//             $formattedEmails[] = $formatted;
//         }

//         return [
//             'emails' => $formattedEmails,
//             'page' => (string)$page,
//             'per_page' => (string)$perPage,
//             'total' => $totalInbox
//         ];
//     }


// public function getEmails(int $page = 1, int $perPage = 10): array
// {
//     // Get all emails (both inbox and sent) first
//     $allEmails = [];

//     // Get inbox emails
//     $inboxUids = $this->getSortedUids($this->inboxConnection);
//     foreach ($inboxUids as $uidData) {
//         $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//         if ($email) {
//             $allEmails[] = $email;
//         }
//     }

//     // Get sent emails if connection exists
//     if ($this->sentConnection) {
//         $sentUids = $this->getSortedUids($this->sentConnection);
//         foreach ($sentUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if ($email) {
//                 $allEmails[] = $email;
//             }
//         }
//     }

//     // Sort all emails by date
//     usort($allEmails, function($a, $b) {
//         return strtotime($b['date']) <=> strtotime($a['date']); // Newest first
//     });

//     // Build complete thread structure
//     $threads = [];
//     $allMessageIds = []; // Track all message IDs we've seen

//     foreach ($allEmails as $email) {
//         $messageId = $email['message_id'];
//         $allMessageIds[$messageId] = true;

//         // Skip if we've already processed this message as part of another thread
//         if (isset($threads[$messageId])) {
//             continue;
//         }

//         $references = $this->parseReferences($email['references'] ?? '');

//         // Find the root message ID (most recent reference or this message if no references)
//         $rootMessageId = empty($references) ? $messageId : $references[0];

//         // Initialize thread if not exists
//         if (!isset($threads[$rootMessageId])) {
//             $threads[$rootMessageId] = [
//                 'root' => null,
//                 'replies' => []
//             ];
//         }

//         // If this is the root message, set it
//         if ($messageId === $rootMessageId) {
//             $threads[$rootMessageId]['root'] = $email;
//         } else {
//             // Otherwise add as reply
//             $threads[$rootMessageId]['replies'][] = $email;
//         }
//     }

//     // Now fill in any missing root messages
//     foreach ($threads as $threadId => &$thread) {
//         if (is_null($thread['root'])) {
//             foreach ($allEmails as $email) {
//                 if ($email['message_id'] === $threadId) {
//                     $thread['root'] = $email;
//                     break;
//                 }
//             }
//         }
//     }
//     unset($thread);

//     // Now process only inbox emails for pagination
//     $inboxMessageIds = array_column(
//         array_filter($allEmails, fn($e) => !$e['is_sent']),
//         'message_id'
//     );
//     $totalInbox = count($inboxMessageIds);

//     // Get only threads that have an inbox email as root
//     $inboxThreads = array_filter($threads, function($thread) use ($inboxMessageIds) {
//         return $thread['root'] && in_array($thread['root']['message_id'], $inboxMessageIds);
//     });

//     // Apply pagination
//     $paginatedThreads = array_slice($inboxThreads, ($page - 1) * $perPage, $perPage);

//     // Format the output with proper nesting
//     $formattedEmails = [];
//     foreach ($paginatedThreads as $thread) {
//         $formatted = [
//             'subject' => $thread['root']['subject'],
//             'from' => $thread['root']['from'],
//             'date' => $thread['root']['date'],
//             'body' => $thread['root']['body'],
//             'attachment' => $thread['root']['attachment'],
//             'message_id' => $thread['root']['message_id'],
//             'replies' => [],
//         ];

//         // Sort replies by date
//         usort($thread['replies'], function($a, $b) {
//             return strtotime($a['date']) <=> strtotime($b['date']);
//         });

//         // Track added message IDs to prevent duplicates
//         $addedMessageIds = [];

//         foreach ($thread['replies'] as $reply) {
//             $replyMessageId = $reply['message_id'];

//             // Skip if we've already added this message
//             if (isset($addedMessageIds[$replyMessageId])) {
//                 continue;
//             }

//             $addedMessageIds[$replyMessageId] = true;

//             $formatted['replies'][] = [
//                 'subject' => $reply['subject'],
//                 'from' => $reply['from'],
//                 'date' => $reply['date'],
//                 'body' => $this->cleanEmailBody($reply['body']),
//                 'attachment' => $reply['attachment'],
//                 'message_id' => $replyMessageId,
//                 'thread_id' => $thread['root']['message_id'],
//             ];
//         }

//         $formattedEmails[] = $formatted;
//     }

//     return [
//         'emails' => $formattedEmails,
//         'page' => (string)$page,
//         'per_page' => (string)$perPage,
//         'total' => $totalInbox
//     ];
// }

//     private function parseReferences(string $references): array
//     {
//         if (empty($references)) {
//             return [];
//         }

//         // References can be a space-separated list of message IDs
//         return array_filter(array_map('trim', explode(' ', $references)));
//     }

//     private function getFullEmail($connection, int $uid): ?array
//     {
//         $msgno = imap_msgno($connection, $uid);
//         if (!$msgno) {
//             return null;
//         }

//         $header = imap_headerinfo($connection, $msgno);
//         if (!$header) {
//             return null;
//         }

//         $structure = imap_fetchstructure($connection, $msgno);
//         $email = $this->parseEmail($connection, $header, $structure, $msgno);
//         $email['is_sent'] = ($connection === $this->sentConnection);

//         return $email;
//     }

//     private function getSortedUids($connection): array
//     {
//         if (!$connection) {
//             return [];
//         }

//         // Fixed: Correct parameter order for imap_sort()
//         $uids = imap_sort($connection, SORTDATE, SORT_DESC, SE_UID);

//         if (!$uids) {
//             return [];
//         }

//         return array_map(fn($uid) => [
//             'connection' => $connection,
//             'uid' => $uid,
//         ], $uids);
//     }

//     private function parseEmail_junaid($connection, $header, $structure, $msgno): array
//     {
//         $fromEmail = $header->from[0]->mailbox . '@' . $header->from[0]->host;
//         $toEmail = isset($header->to[0]) ?
//         $header->to[0]->mailbox . '@' . $header->to[0]->host : null;

//         return [
//             'subject' => $header->subject ?? '(No Subject)',
//             'from' => $fromEmail,
//             'to' => $toEmail,
//             'date' => date('Y-m-d H:i:s', $header->udate),
//             'body' => $this->cleanEmailBody($this->getBody($connection, $msgno, $structure)),
//             'attachment' => $this->getAttachments($connection, $msgno, $structure),
//             'message_id' => $header->message_id,
//             'references' => $header->references ?? null,
//         ];
//     }


// private function parseEmail($connection, $header, $structure, $msgno): array
// {
//     // Convert subject to UTF-8
//     $subject = '';
//     if (isset($header->subject)) {
//         $subject = $this->decodeMimeString($header->subject);
//     }

//     // Convert from address to UTF-8
//     $fromEmail = $this->decodeMimeString(
//         $header->from[0]->mailbox . '@' . $header->from[0]->host
//     );

//     // Convert to address to UTF-8 if exists
//     $toEmail = null;
//     if (isset($header->to[0])) {
//         $toEmail = $this->decodeMimeString(
//             $header->to[0]->mailbox . '@' . $header->to[0]->host
//         );
//     }

//     // Get body with proper encoding handling
//     $body = $this->cleanEmailBody($this->getBody($connection, $msgno, $structure));

//     return [
//         'subject' => $subject ?: '(No Subject)',
//         'from' => $fromEmail,
//         'to' => $toEmail,
//         'date' => date('Y-m-d H:i:s', $header->udate),
//         'body' => $body,
//         'attachment' => $this->getAttachments($connection, $msgno, $structure),
//         'message_id' => $header->message_id,
//         'references' => isset($header->references) ? $this->decodeMimeString($header->references) : null,
//     ];
// }

// private function decodeMimeString($string): string
// {
//     if (empty($string)) {
//         return '';
//     }

//     // If the string is already UTF-8, return as is
//     if (mb_check_encoding($string, 'UTF-8')) {
//         return $string;
//     }

//     // Decode MIME header
//     $decoded = imap_utf8($string);

//     // Fallback to mb_convert_encoding if imap_utf8 fails
//     if ($decoded === false || $decoded === $string) {
//         $decoded = mb_convert_encoding($string, 'UTF-8', 'ISO-8859-1');
//     }

//     return $decoded;
// }

//     private function cleanEmailBody_bkp(string $body): string
//     {
//         // Decode quoted-printable (for = signs, etc.)
//         $body = quoted_printable_decode($body);

//         // Strip any MIME headers accidentally present in the body
//         $body = preg_replace('/(Return-Path:|Received:|Content-Type:|Content-Transfer-Encoding:|MIME-Version:).*?\n(?:\s.*\n)*/i', '', $body);

//         // Convert common HTML line breaks to real newlines
//         $body = str_ireplace(["<br>", "<br/>", "<br />"], "\n", $body);

//         // Remove remaining HTML tags
//         $body = strip_tags($body);

//         // Decode HTML entities
//         $body = html_entity_decode($body, ENT_QUOTES | ENT_HTML5, 'UTF-8');

//         // Remove common reply lines (like Gmail, Outlook)
//         $body = preg_replace('/\n?On\s.+?wrote:\n.*$/s', '', $body); // Removes "On <date>, <name> wrote:"
//         $body = preg_replace('/>+.*/', '', $body); // Removes lines starting with '>' (quoted reply)

//         // Remove signatures (e.g., starting with "--")
//         $body = preg_replace('/--\s*\n.*$/s', '', $body);

//         // Normalize and clean line breaks
//         $body = preg_replace("/\r\n|\r/", "\n", $body);
//         $body = preg_replace("/\n{2,}/", "\n\n", $body); // Collapse multiple line breaks

//         return trim($body);
//     }

//     private function cleanEmailBody_working(string $body): string
//     {
//         // Decode quoted-printable (for = signs, etc.)
//         $body = quoted_printable_decode($body);

//         // Convert common HTML line breaks to real newlines
//         $body = str_ireplace(["<br>", "<br/>", "<br />", "</p>", "</div>"], "\n", $body);

//         // Remove remaining HTML tags
//         $body = strip_tags($body);

//         // Decode HTML entities
//         $body = html_entity_decode($body, ENT_QUOTES | ENT_HTML5, 'UTF-8');

//         // Remove email headers that might appear in the body
//         $body = preg_replace('/^From:.*$/mi', '', $body);
//         $body = preg_replace('/^To:.*$/mi', '', $body);
//         $body = preg_replace('/^Subject:.*$/mi', '', $body);
//         $body = preg_replace('/^Date:.*$/mi', '', $body);
//         $body = preg_replace('/^Message-ID:.*$/mi', '', $body);
//         $body = preg_replace('/^References:.*$/mi', '', $body);
//         $body = preg_replace('/^In-Reply-To:.*$/mi', '', $body);
//         $body = preg_replace('/^X-.*?:.*$/mi', '', $body); // All X-headers
//         $body = preg_replace('/^Content-.*?:.*$/mi', '', $body); // Content headers
//         $body = preg_replace('/^MIME-.*?:.*$/mi', '', $body); // MIME headers

//         // Remove common reply patterns (Gmail, Outlook, Apple Mail, etc.)
//         $body = preg_replace('/\n?On\s.+\s<\d{4}-\d{2}-\d{2}>.*$/s', '', $body);
//         $body = preg_replace('/\n?On\s.+\s\w+,\s\w+\s\d{1,2},\s\d{4}.*$/s', '', $body);
//         $body = preg_replace('/\n?On\s.+\swrote:.*$/s', '', $body);
//         $body = preg_replace('/\n?Le\s.+\sa\sécrit\s:.*$/s', '', $body); // French
//         $body = preg_replace('/\n?El\s.+\sescribió:.*$/s', '', $body); // Spanish
//         $body = preg_replace('/\n?Am\s.+\sschrieb\s.*$/s', '', $body); // German
//         $body = preg_replace('/\n?-\s?original\s?message\s?-.*$/is', '', $body);
//         $body = preg_replace('/\n?From:.*$/mi', '', $body);
//         $body = preg_replace('/\n?Sent:.*$/mi', '', $body);
//         $body = preg_replace('/\n?To:.*$/mi', '', $body);
//         $body = preg_replace('/\n?Subject:.*$/mi', '', $body);

//         // Remove quoted text (lines starting with >)
//         $body = preg_replace('/^>+.*$/m', '', $body);

//         // Remove signatures (common patterns)
//         $body = preg_replace('/\n-- \n.*$/s', '', $body);
//         $body = preg_replace('/\n___+\n.*$/s', '', $body);
//         $body = preg_replace('/\n-\w+\n.*$/s', '', $body);
//         $body = preg_replace('/\nBest regards,.*$/is', '', $body);
//         $body = preg_replace('/\nRegards,.*$/is', '', $body);
//         $body = preg_replace('/\nCheers,.*$/is', '', $body);
//         $body = preg_replace('/\nThanks,.*$/is', '', $body);
//         $body = preg_replace('/\nSincerely,.*$/is', '', $body);
//         $body = preg_replace('/\nSent from my .*$/i', '', $body);

//         // Remove any remaining email headers in the body
//         $body = preg_replace('/^.*@.*>\n?/m', '', $body);

//         // Normalize and clean line breaks
//         $body = preg_replace("/\r\n|\r/", "\n", $body);
//         $body = preg_replace("/\n{3,}/", "\n\n", $body); // Collapse multiple line breaks
//         $body = trim($body);

//         // Try to extract only the first part before common reply separators
//         $parts = preg_split('/\n-----Original Message-----|\n---|\n__|\nFrom:|\n> On|\nOn\s\w{3},|\nLe\s\w{3},/i', $body);
//         if (count($parts) > 1) {
//             $body = trim($parts[0]);
//         }

//         return $body;
//     }

//     private function cleanEmailBody(string $body): string
//     {
//         // Decode quoted-printable first
//         $body = quoted_printable_decode($body);

//         // Remove all email headers that might appear in the body
//         $body = preg_replace('/^Return-Path:.*$/mi', '', $body);
//         $body = preg_replace('/^Received:.*$/mi', '', $body);
//         $body = preg_replace('/^From:.*$/mi', '', $body);
//         $body = preg_replace('/^To:.*$/mi', '', $body);
//         $body = preg_replace('/^Subject:.*$/mi', '', $body);
//         $body = preg_replace('/^Date:.*$/mi', '', $body);
//         $body = preg_replace('/^Message-ID:.*$/mi', '', $body);
//         $body = preg_replace('/^References:.*$/mi', '', $body);
//         $body = preg_replace('/^In-Reply-To:.*$/mi', '', $body);
//         $body = preg_replace('/^X-.*?:.*$/mi', '', $body); // All X-headers
//         $body = preg_replace('/^Content-.*?:.*$/mi', '', $body); // Content headers
//         $body = preg_replace('/^MIME-.*?:.*$/mi', '', $body); // MIME headers
//         $body = preg_replace('/^DKIM-.*?:.*$/mi', '', $body); // DKIM headers
//         $body = preg_replace('/^ARC-.*?:.*$/mi', '', $body); // ARC headers

//         // Remove SMTP status messages
//         $body = preg_replace('/^.*?ESMTPSA id [a-f0-9-]+.*$/mi', '', $body);
//         $body = preg_replace('/^.*?version=TLS.*$/mi', '', $body);
//         $body = preg_replace('/^.*?cipher=.*$/mi', '', $body);
//         $body = preg_replace('/^.*?bits.*$/mi', '', $body);

//         // Convert HTML line breaks to newlines
//         $body = str_ireplace(["<br>", "<br/>", "<br />", "</p>", "</div>"], "\n", $body);

//         // Remove remaining HTML tags
//         $body = strip_tags($body);

//         // Decode HTML entities
//         $body = html_entity_decode($body, ENT_QUOTES | ENT_HTML5, 'UTF-8');

//         // Remove common reply patterns (Gmail, Outlook, etc.)
//         $body = preg_replace('/\n?On\s.+\s<\d{4}-\d{2}-\d{2}>.*$/s', '', $body);
//         $body = preg_replace('/\n?On\s.+\s\w+,\s\w+\s\d{1,2},\s\d{4}.*$/s', '', $body);
//         $body = preg_replace('/\n?On\s.+\swrote:.*$/s', '', $body);
//         $body = preg_replace('/\n?Le\s.+\sa\sécrit\s:.*$/s', '', $body); // French
//         $body = preg_replace('/\n?El\s.+\sescribió:.*$/s', '', $body); // Spanish
//         $body = preg_replace('/\n?Am\s.+\sschrieb\s.*$/s', '', $body); // German
//         $body = preg_replace('/\n?-\s?original\s?message\s?-.*$/is', '', $body);

//         // Remove quoted text (lines starting with >)
//         $body = preg_replace('/^>+.*$/m', '', $body);

//         // Remove signatures
//         $body = preg_replace('/\n-- \n.*$/s', '', $body);
//         $body = preg_replace('/\n___+\n.*$/s', '', $body);
//         $body = preg_replace('/\n-\w+\n.*$/s', '', $body);
//         $body = preg_replace('/\nBest regards,.*$/is', '', $body);
//         $body = preg_replace('/\nRegards,.*$/is', '', $body);
//         $body = preg_replace('/\nCheers,.*$/is', '', $body);
//         $body = preg_replace('/\nThanks,.*$/is', '', $body);
//         $body = preg_replace('/\nSincerely,.*$/is', '', $body);
//         $body = preg_replace('/\nSent from my .*$/i', '', $body);

//         // Remove any remaining technical details
//         $body = preg_replace('/\n?.*?smtp\..*?$/mi', '', $body);
//         $body = preg_replace('/\n?.*?for .*?;.*?$/mi', '', $body);
//         $body = preg_replace('/\n?.*?\(PDT\).*?$/mi', '', $body);

//         // Normalize line breaks
//         $body = preg_replace("/\r\n|\r/", "\n", $body);

//         // Collapse multiple line breaks
//         $body = preg_replace("/\n{3,}/", "\n\n", $body);

//         // Trim whitespace
//         $body = trim($body);

//         // Extract only the first part before common separators
//         $parts = preg_split(
//             '/\n-----Original Message-----|\n---|\n__|\nFrom:|\n> On|\nOn\s\w{3},|\nLe\s\w{3},/i',
//             $body
//         );

//         if (count($parts) > 1) {
//             $body = trim($parts[0]);
//         }

//         return $body;
//     }

//     private function getBody_jun($connection, $msgno, $structure): string
//     {
//         // First try to get plain text part
//         $body = $this->getPart($connection, $msgno, $structure, 'text/plain');

//         // If no plain text, try HTML
//         if (empty($body)) {
//             $body = $this->getPart($connection, $msgno, $structure, 'text/html');
//         }

//         // Fallback to raw body
//         if (empty($body)) {
//             $body = imap_body($connection, $msgno);
//         }

//         return $body;
//     }

// private function getBody($connection, $msgno, $structure): string
// {
//     // First try to get plain text part
//     $body = $this->getPart($connection, $msgno, $structure, 'text/plain');

//     // If no plain text, try HTML
//     if (empty($body)) {
//         $body = $this->getPart($connection, $msgno, $structure, 'text/html');
//     }

//     // Fallback to raw body
//     if (empty($body)) {
//         $body = imap_body($connection, $msgno, FT_UID);
//     }

//     // Convert to UTF-8 if needed
//     if (!mb_check_encoding($body, 'UTF-8')) {
//         $body = mb_convert_encoding($body, 'UTF-8', 'ISO-8859-1');
//     }

//     return $body;
// }

//     private function getPart_jun($connection, $msgno, $structure, $mimeType, $partNumber = '')
//     {
//         if ($structure->type == 1) { // MULTIPART
//             foreach ($structure->parts as $index => $part) {
//                 $partId = $partNumber ? $partNumber . '.' . ($index + 1) : ($index + 1);
//                 $data = $this->getPart($connection, $msgno, $part, $mimeType, $partId);
//                 if ($data) {
//                     return $data;
//                 }
//             }
//         } elseif ($structure->type == 0 && isset($structure->subtype)) { // SINGLE PART
//             $subtype = strtolower($structure->subtype);
//             if (
//                 ($mimeType == 'text/plain' && $subtype == 'plain') ||
//                 ($mimeType == 'text/html' && $subtype == 'html')
//             ) {
//                 return imap_fetchbody($connection, $msgno, $partNumber);
//             }
//         }
//         return '';
//     }


// private function getPart($connection, $msgno, $structure, $mimeType, $partNumber = '')
// {
//     if ($structure->type == 1) { // MULTIPART
//         foreach ($structure->parts as $index => $part) {
//             $partId = $partNumber ? $partNumber . '.' . ($index + 1) : ($index + 1);
//             $data = $this->getPart($connection, $msgno, $part, $mimeType, $partId);
//             if ($data) {
//                 return $data;
//             }
//         }
//     } elseif ($structure->type == 0 && isset($structure->subtype)) { // SINGLE PART
//         $subtype = strtolower($structure->subtype);
//         if (
//             ($mimeType == 'text/plain' && $subtype == 'plain') ||
//             ($mimeType == 'text/html' && $subtype == 'html')
//         ) {
//             $body = imap_fetchbody($connection, $msgno, $partNumber, FT_UID);

//             // Handle encoding
//             if (isset($structure->parameters)) {
//                 foreach ($structure->parameters as $param) {
//                     if (strtolower($param->attribute) == 'charset') {
//                         $charset = strtoupper($param->value);
//                         if ($charset != 'UTF-8' && $charset != 'US-ASCII') {
//                             $body = mb_convert_encoding($body, 'UTF-8', $charset);
//                         }
//                         break;
//                     }
//                 }
//             }

//             return $body;
//         }
//     }
//     return '';
// }


//     private function getAttachments_jun($connection, $msgno, $structure): array
//     {
//         $attachments = [];
//         if (!isset($structure->parts))
//             return $attachments;

//         foreach ($structure->parts as $index => $part) {
//             if ($part->ifdisposition && $part->disposition == 'ATTACHMENT') {
//                 $attachments[] = [
//                     'name' => $part->dparameters[0]->value ?? 'attachment',
//                     'content' => imap_fetchbody($connection, $msgno, $index + 1)
//                 ];
//             }
//         }
//         return $attachments;
//     }

// private function getAttachments($connection, $msgno, $structure): array
// {
//     $attachments = [];
//     if (!isset($structure->parts)) {
//         return $attachments;
//     }

//     foreach ($structure->parts as $index => $part) {
//         if ($part->ifdisposition && $part->disposition == 'ATTACHMENT') {
//             $name = $part->dparameters[0]->value ?? 'attachment';
//             $name = $this->decodeMimeString($name);

//             $content = imap_fetchbody($connection, $msgno, $index + 1, FT_UID);
//             $attachments[] = [
//                 'name' => $name,
//                 'content' => $content
//             ];
//         }
//     }
//     return $attachments;
// }

//     public function getMostRecentReplyByMessageId(string $messageId): ?array
//     {
//         if (!$this->sentConnection) {
//             return null;
//         }

//         // Get UIDs sorted descending (latest first)
//         $sentUids = $this->getSortedUids($this->sentConnection);
//         usort($sentUids, fn($a, $b) => $b['uid'] <=> $a['uid']); // descending UID

//         foreach ($sentUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if (!$email) {
//                 continue;
//             }

//             // Normalize all headers
//             $messageId = strtolower(trim($messageId));
//             $inReplyTo = strtolower(trim($email['in_reply_to'] ?? ''));
//             $references = array_map('strtolower', array_map('trim', $this->parseReferences($email['references'] ?? '')));

//             // Match message ID
//             if ($inReplyTo === $messageId || in_array($messageId, $references)) {
//                 // Debugging
//                 // error_log("Matched UID: " . $uidData['uid'] . " Date: " . $email['date']);
//                 return $email;
//             }
//         }

//         return null;
//     }


//     public function __destruct()
//     {
//         if ($this->inboxConnection)
//             imap_close($this->inboxConnection);
//         if ($this->sentConnection)
//             imap_close($this->sentConnection);
//     }
// }










































// namespace App\Services;

// class MailboxService
// {
//     private $inboxConnection;
//     private $sentConnection;

//     public function __construct()
//     {
//         // Connect to INBOX
//         $this->inboxConnection = @imap_open(
//             $this->getConnectionString('INBOX'),
//             config('mail.mailbox_username'),
//             config('mail.mailbox_password')
//         );

//         // Connect to Sent Mail
//         $this->sentConnection = @imap_open(
//             $this->getConnectionString('[Gmail]/Sent Mail'),
//             config('mail.mailbox_username'),
//             config('mail.mailbox_password')
//         );

//         if (!$this->inboxConnection) {
//             throw new \Exception('INBOX connection failed: ' . imap_last_error());
//         }

//         if (!$this->sentConnection) {
//             \Log::warning('Sent Mail connection failed: ' . imap_last_error());
//         }
//     }

//     private function getConnectionString(string $folder): string
//     {
//         return sprintf(
//             "{%s:%s/imap/%s}%s",
//             config('mail.mailbox_host'),
//             config('mail.mailbox_port'),
//             config('mail.mailbox_encryption'),
//             $folder
//         );
//     }

//     public function getEmails_bkp(int $page = 1, int $perPage = 10): array
//     {
//         // Step 1: Get paginated inbox emails only
//         $inboxUids = $this->getSortedUids($this->inboxConnection);
//         $totalInbox = count($inboxUids);

//         // Apply pagination to inbox emails only
//         $start = ($page - 1) * $perPage;
//         $paginatedInboxUids = array_slice($inboxUids, $start, $perPage);

//         // Step 2: Fetch full content for paginated inbox emails
//         $inboxEmails = [];
//         foreach ($paginatedInboxUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if ($email) {
//                 $inboxEmails[] = $email;
//             }
//         }

//         // Step 3: Get only relevant sent emails (matching references of inbox emails)
//         $inboxMessageIds = array_column($inboxEmails, 'message_id');
//         $sentEmails = [];

//         $sentUids = $this->getSortedUids($this->sentConnection);
//         foreach ($sentUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if (!$email) {
//                 continue;
//             }

//             if (!empty($email['references']) && in_array($email['references'], $inboxMessageIds)) {
//                 $sentEmails[] = $email;
//             }
//         }

//         // Step 4: Fetch all sent emails (for potential replies)
//         $sentEmails = [];
//         foreach ($sentUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if ($email) {
//                 $sentEmails[] = $email;
//             }
//         }

//         // Step 5: Group sent emails by thread for easy merging
//         $sentByReferences = [];
//         foreach ($sentEmails as $sentEmail) {
//             if (!empty($sentEmail['references'])) {
//                 $sentByReferences[$sentEmail['references']][] = $sentEmail;
//             }
//         }

//         // Step 6: Merge sent replies into their corresponding inbox threads
//         $threads = [];
//         foreach ($inboxEmails as $inboxEmail) {
//             $messageId = $inboxEmail['message_id'];
//             $thread = [$inboxEmail];

//             if (isset($sentByReferences[$messageId])) {
//                 $thread = array_merge($thread, $sentByReferences[$messageId]);
//             }

//             // Sort thread by date (optional)
//             usort($thread, fn($a, $b) => strtotime($a['date']) <=> strtotime($b['date']));

//             // Use inbox message_id as key
//             $threads[] = [
//                 'message_id' => $messageId,
//                 'date' => $inboxEmail['date'],
//                 'emails' => $thread,
//             ];
//         }

//         // Re-index array and maintain sorting
//         $threads = array_values($threads);
//         usort($threads, fn($a, $b) => strtotime($b['date']) - strtotime($a['date']));

//         $formattedEmails = [];

//         foreach ($threads as $thread) {
//             $mainEmail = $thread['emails'][0];

//             $formatted = [
//                 'subject' => $mainEmail['subject'],
//                 'from' => $mainEmail['from'],
//                 'date' => $mainEmail['date'],
//                 'body' => $mainEmail['body'],
//                 'attachment' => $mainEmail['attachment'],
//                 'message_id' => $mainEmail['message_id'],
//                 'replies' => [],
//             ];

//             // Add replies if any
//             if (count($thread['emails']) > 1) {
//                 foreach (array_slice($thread['emails'], 1) as $reply) {
//                     $formatted['replies'][] = [
//                         'subject' => $reply['subject'],
//                         'from' => $reply['from'],
//                         'date' => $reply['date'],
//                         'body' => $this->cleanEmailBody($reply['body']),
//                         'attachment' => $reply['attachment'],
//                         'message_id' => $reply['message_id'],
//                         'thread_id' => $mainEmail['message_id'],
//                     ];
//                 }
//             }

//             $formattedEmails[] = $formatted;
//         }

//         return [
//             'emails' => $formattedEmails,
//             'page' => (string)$page,
//             'per_page' => (string)$perPage,
//             'total' => $totalInbox
//         ];
//     }

//     public function getEmails(int $page = 1, int $perPage = 10): array
//     {
//         // Get all emails (both inbox and sent) first
//         $allEmails = [];

//         // Get inbox emails
//         $inboxUids = $this->getSortedUids($this->inboxConnection);
//         foreach ($inboxUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if ($email) {
//                 $allEmails[] = $email;
//             }
//         }

//         // Get sent emails if connection exists
//         if ($this->sentConnection) {
//             $sentUids = $this->getSortedUids($this->sentConnection);
//             foreach ($sentUids as $uidData) {
//                 $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//                 if ($email) {
//                     $allEmails[] = $email;
//                 }
//             }
//         }

//         // Sort all emails by date
//         usort($allEmails, function($a, $b) {
//             return strtotime($a['date']) <=> strtotime($b['date']);
//         });

//         // Build complete thread structure
//         $threads = [];
//         foreach ($allEmails as $email) {
//             $messageId = $email['message_id'];
//             $references = $this->parseReferences($email['references'] ?? '');

//             // If this email has no references, it's a new thread
//             if (empty($references)) {
//                 if (!isset($threads[$messageId])) {
//                     $threads[$messageId] = [
//                         'root' => $email,
//                         'replies' => []
//                     ];
//                 }
//             } else {
//                 $addedToThread = false;

//                 // Check all references to find where this email belongs
//                 foreach ($references as $ref) {
//                     if (isset($threads[$ref])) {
//                         // Add to existing thread
//                         $threads[$ref]['replies'][] = $email;
//                         $addedToThread = true;
//                         break;
//                     }
//                 }

//                 // If not added to any existing thread, create new thread with first reference as root
//                 if (!$addedToThread) {
//                     $rootRef = $references[0];
//                     $threads[$rootRef] = [
//                         'root' => null, // We might not have the root message yet
//                         'replies' => [$email]
//                     ];
//                 }
//             }
//         }

//         // Now fill in any missing root messages
//         foreach ($threads as $threadId => &$thread) {
//             if (is_null($thread['root'])) {
//                 // Try to find the root message in all emails
//                 foreach ($allEmails as $email) {
//                     if ($email['message_id'] === $threadId) {
//                         $thread['root'] = $email;
//                         break;
//                     }
//                 }
//             }
//         }
//         unset($thread); // Break the reference

//         // Now process only inbox emails for pagination
//         $inboxMessageIds = array_column(
//             array_filter($allEmails, fn($e) => !$e['is_sent']),
//             'message_id'
//         );
//         $totalInbox = count($inboxMessageIds);

//         // Get only threads that have an inbox email as root
//         $inboxThreads = array_filter($threads, function($thread) use ($inboxMessageIds) {
//             return $thread['root'] && in_array($thread['root']['message_id'], $inboxMessageIds);
//         });

//         // Apply pagination
//         $paginatedThreads = array_slice($inboxThreads, ($page - 1) * $perPage, $perPage);

//         // Format the output with proper nesting
//         $formattedEmails = [];
//         foreach ($paginatedThreads as $thread) {
//             $formatted = [
//                 'subject' => $thread['root']['subject'],
//                 'from' => $thread['root']['from'],
//                 'date' => $thread['root']['date'],
//                 'body' => $thread['root']['body'],
//                 'attachment' => $thread['root']['attachment'],
//                 'message_id' => $thread['root']['message_id'],
//                 'replies' => [],
//             ];

//             // Sort replies by date
//             usort($thread['replies'], function($a, $b) {
//                 return strtotime($a['date']) <=> strtotime($b['date']);
//             });

//             // Build reply tree
//             $replyMap = [];
//             foreach ($thread['replies'] as $reply) {
//                 $replyMap[$reply['message_id']] = $reply;
//             }

//             foreach ($thread['replies'] as $reply) {
//                 $replyReferences = $this->parseReferences($reply['references'] ?? '');
//                 $parentId = end($replyReferences); // Get most recent reference

//                 if ($parentId === $thread['root']['message_id']) {
//                     // Direct reply to root
//                     $formatted['replies'][] = [
//                         'subject' => $reply['subject'],
//                         'from' => $reply['from'],
//                         'date' => $reply['date'],
//                         'body' => $this->cleanEmailBody($reply['body']),
//                         'attachment' => $reply['attachment'],
//                         'message_id' => $reply['message_id'],
//                         'thread_id' => $thread['root']['message_id'],
//                     ];
//                 } else if (isset($replyMap[$parentId])) {
//                     // Reply to another reply - handled by client-side threading
//                     $formatted['replies'][] = [
//                         'subject' => $reply['subject'],
//                         'from' => $reply['from'],
//                         'date' => $reply['date'],
//                         'body' => $this->cleanEmailBody($reply['body']),
//                         'attachment' => $reply['attachment'],
//                         'message_id' => $reply['message_id'],
//                         'thread_id' => $thread['root']['message_id'],
//                     ];
//                 }
//             }

//             $formattedEmails[] = $formatted;
//         }

//         return [
//             'emails' => $formattedEmails,
//             'page' => (string)$page,
//             'per_page' => (string)$perPage,
//             'total' => $totalInbox
//         ];
//     }

//     private function parseReferences(string $references): array
//     {
//         if (empty($references)) {
//             return [];
//         }

//         // References can be a space-separated list of message IDs
//         return array_filter(array_map('trim', explode(' ', $references)));
//     }

//     private function getFullEmail($connection, int $uid): ?array
//     {
//         $msgno = imap_msgno($connection, $uid);
//         if (!$msgno) {
//             return null;
//         }

//         $header = imap_headerinfo($connection, $msgno);
//         if (!$header) {
//             return null;
//         }

//         $structure = imap_fetchstructure($connection, $msgno);
//         $email = $this->parseEmail($connection, $header, $structure, $msgno);
//         $email['is_sent'] = ($connection === $this->sentConnection);

//         return $email;
//     }

//     private function getSortedUids($connection): array
//     {
//         if (!$connection) {
//             return [];
//         }

//         // Fixed: Correct parameter order for imap_sort()
//         $uids = imap_sort($connection, SORTDATE, SORT_DESC, SE_UID);

//         if (!$uids) {
//             return [];
//         }

//         return array_map(fn($uid) => [
//             'connection' => $connection,
//             'uid' => $uid,
//         ], $uids);
//     }

//     private function parseEmail($connection, $header, $structure, $msgno): array
//     {
//         $fromEmail = $header->from[0]->mailbox . '@' . $header->from[0]->host;
//         $toEmail = isset($header->to[0]) ?
//         $header->to[0]->mailbox . '@' . $header->to[0]->host : null;

//         return [
//             'subject' => $header->subject ?? '(No Subject)',
//             'from' => $fromEmail,
//             'to' => $toEmail,
//             'date' => date('Y-m-d H:i:s', $header->udate),
//             'body' => $this->cleanEmailBody($this->getBody($connection, $msgno, $structure)),
//             'attachment' => $this->getAttachments($connection, $msgno, $structure),
//             'message_id' => $header->message_id,
//             'references' => $header->references ?? null,
//         ];
//     }

//     private function cleanEmailBody_bkp(string $body): string
//     {
//         // Decode quoted-printable (for = signs, etc.)
//         $body = quoted_printable_decode($body);

//         // Strip any MIME headers accidentally present in the body
//         $body = preg_replace('/(Return-Path:|Received:|Content-Type:|Content-Transfer-Encoding:|MIME-Version:).*?\n(?:\s.*\n)*/i', '', $body);

//         // Convert common HTML line breaks to real newlines
//         $body = str_ireplace(["<br>", "<br/>", "<br />"], "\n", $body);

//         // Remove remaining HTML tags
//         $body = strip_tags($body);

//         // Decode HTML entities
//         $body = html_entity_decode($body, ENT_QUOTES | ENT_HTML5, 'UTF-8');

//         // Remove common reply lines (like Gmail, Outlook)
//         $body = preg_replace('/\n?On\s.+?wrote:\n.*$/s', '', $body); // Removes "On <date>, <name> wrote:"
//         $body = preg_replace('/>+.*/', '', $body); // Removes lines starting with '>' (quoted reply)

//         // Remove signatures (e.g., starting with "--")
//         $body = preg_replace('/--\s*\n.*$/s', '', $body);

//         // Normalize and clean line breaks
//         $body = preg_replace("/\r\n|\r/", "\n", $body);
//         $body = preg_replace("/\n{2,}/", "\n\n", $body); // Collapse multiple line breaks

//         return trim($body);
//     }

//     private function cleanEmailBody(string $body): string
//     {
//         // Decode quoted-printable (for = signs, etc.)
//         $body = quoted_printable_decode($body);

//         // Convert common HTML line breaks to real newlines
//         $body = str_ireplace(["<br>", "<br/>", "<br />", "</p>", "</div>"], "\n", $body);

//         // Remove remaining HTML tags
//         $body = strip_tags($body);

//         // Decode HTML entities
//         $body = html_entity_decode($body, ENT_QUOTES | ENT_HTML5, 'UTF-8');

//         // Remove email headers that might appear in the body
//         $body = preg_replace('/^From:.*$/mi', '', $body);
//         $body = preg_replace('/^To:.*$/mi', '', $body);
//         $body = preg_replace('/^Subject:.*$/mi', '', $body);
//         $body = preg_replace('/^Date:.*$/mi', '', $body);
//         $body = preg_replace('/^Message-ID:.*$/mi', '', $body);
//         $body = preg_replace('/^References:.*$/mi', '', $body);
//         $body = preg_replace('/^In-Reply-To:.*$/mi', '', $body);
//         $body = preg_replace('/^X-.*?:.*$/mi', '', $body); // All X-headers
//         $body = preg_replace('/^Content-.*?:.*$/mi', '', $body); // Content headers
//         $body = preg_replace('/^MIME-.*?:.*$/mi', '', $body); // MIME headers

//         // Remove common reply patterns (Gmail, Outlook, Apple Mail, etc.)
//         $body = preg_replace('/\n?On\s.+\s<\d{4}-\d{2}-\d{2}>.*$/s', '', $body);
//         $body = preg_replace('/\n?On\s.+\s\w+,\s\w+\s\d{1,2},\s\d{4}.*$/s', '', $body);
//         $body = preg_replace('/\n?On\s.+\swrote:.*$/s', '', $body);
//         $body = preg_replace('/\n?Le\s.+\sa\sécrit\s:.*$/s', '', $body); // French
//         $body = preg_replace('/\n?El\s.+\sescribió:.*$/s', '', $body); // Spanish
//         $body = preg_replace('/\n?Am\s.+\sschrieb\s.*$/s', '', $body); // German
//         $body = preg_replace('/\n?-\s?original\s?message\s?-.*$/is', '', $body);
//         $body = preg_replace('/\n?From:.*$/mi', '', $body);
//         $body = preg_replace('/\n?Sent:.*$/mi', '', $body);
//         $body = preg_replace('/\n?To:.*$/mi', '', $body);
//         $body = preg_replace('/\n?Subject:.*$/mi', '', $body);

//         // Remove quoted text (lines starting with >)
//         $body = preg_replace('/^>+.*$/m', '', $body);

//         // Remove signatures (common patterns)
//         $body = preg_replace('/\n-- \n.*$/s', '', $body);
//         $body = preg_replace('/\n___+\n.*$/s', '', $body);
//         $body = preg_replace('/\n-\w+\n.*$/s', '', $body);
//         $body = preg_replace('/\nBest regards,.*$/is', '', $body);
//         $body = preg_replace('/\nRegards,.*$/is', '', $body);
//         $body = preg_replace('/\nCheers,.*$/is', '', $body);
//         $body = preg_replace('/\nThanks,.*$/is', '', $body);
//         $body = preg_replace('/\nSincerely,.*$/is', '', $body);
//         $body = preg_replace('/\nSent from my .*$/i', '', $body);

//         // Remove any remaining email headers in the body
//         $body = preg_replace('/^.*@.*>\n?/m', '', $body);

//         // Normalize and clean line breaks
//         $body = preg_replace("/\r\n|\r/", "\n", $body);
//         $body = preg_replace("/\n{3,}/", "\n\n", $body); // Collapse multiple line breaks
//         $body = trim($body);

//         // Try to extract only the first part before common reply separators
//         $parts = preg_split('/\n-----Original Message-----|\n---|\n__|\nFrom:|\n> On|\nOn\s\w{3},|\nLe\s\w{3},/i', $body);
//         if (count($parts) > 1) {
//             $body = trim($parts[0]);
//         }

//         return $body;
//     }

//     private function getBody($connection, $msgno, $structure): string
//     {
//         // First try to get plain text part
//         $body = $this->getPart($connection, $msgno, $structure, 'text/plain');

//         // If no plain text, try HTML
//         if (empty($body)) {
//             $body = $this->getPart($connection, $msgno, $structure, 'text/html');
//         }

//         // Fallback to raw body
//         if (empty($body)) {
//             $body = imap_body($connection, $msgno);
//         }

//         return $body;
//     }

    // private function getPart($connection, $msgno, $structure, $mimeType, $partNumber = '')
    // {
    //     if ($structure->type == 1) { // MULTIPART
    //         foreach ($structure->parts as $index => $part) {
    //             $partId = $partNumber ? $partNumber . '.' . ($index + 1) : ($index + 1);
    //             $data = $this->getPart($connection, $msgno, $part, $mimeType, $partId);
    //             if ($data) {
    //                 return $data;
    //             }
    //         }
    //     } elseif ($structure->type == 0 && isset($structure->subtype)) { // SINGLE PART
    //         $subtype = strtolower($structure->subtype);
    //         if (
    //             ($mimeType == 'text/plain' && $subtype == 'plain') ||
    //             ($mimeType == 'text/html' && $subtype == 'html')
    //         ) {
    //             return imap_fetchbody($connection, $msgno, $partNumber);
    //         }
    //     }
    //     return '';
    // }

    // private function getAttachments($connection, $msgno, $structure): array
    // {
    //     $attachments = [];
    //     if (!isset($structure->parts))
    //         return $attachments;

    //     foreach ($structure->parts as $index => $part) {
    //         if ($part->ifdisposition && $part->disposition == 'ATTACHMENT') {
    //             $attachments[] = [
    //                 'name' => $part->dparameters[0]->value ?? 'attachment',
    //                 'content' => imap_fetchbody($connection, $msgno, $index + 1)
    //             ];
    //         }
    //     }
    //     return $attachments;
    // }

//     public function getMostRecentReplyByMessageId(string $messageId): ?array
//     {
//         if (!$this->sentConnection) {
//             return null;
//         }

//         // Get UIDs sorted descending (latest first)
//         $sentUids = $this->getSortedUids($this->sentConnection);
//         usort($sentUids, fn($a, $b) => $b['uid'] <=> $a['uid']); // descending UID

//         foreach ($sentUids as $uidData) {
//             $email = $this->getFullEmail($uidData['connection'], $uidData['uid']);
//             if (!$email) {
//                 continue;
//             }

//             // Normalize all headers
//             $messageId = strtolower(trim($messageId));
//             $inReplyTo = strtolower(trim($email['in_reply_to'] ?? ''));
//             $references = array_map('strtolower', array_map('trim', $this->parseReferences($email['references'] ?? '')));

//             // Match message ID
//             if ($inReplyTo === $messageId || in_array($messageId, $references)) {
//                 // Debugging
//                 // error_log("Matched UID: " . $uidData['uid'] . " Date: " . $email['date']);
//                 return $email;
//             }
//         }

//         return null;
//     }


//     public function __destruct()
//     {
//         if ($this->inboxConnection)
//             imap_close($this->inboxConnection);
//         if ($this->sentConnection)
//             imap_close($this->sentConnection);
//     }
// }
