<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Models\ScheduleCallBack;
use App\Models\Cdr;
use Carbon\Carbon;

class ScheduleCallBackController extends Controller
{
    public function index(Request $request) {
        $request->validate([
            'agent' => 'required|integer|exists:users,id'
        ]);

        $todayDate = Carbon::today()->toDateString();
        $yesterday = Carbon::yesterday()->toDateString();
        
        $base = ScheduleCallBack::query()->where('status', 'pending')->where('created_by', $request->agent);
        $today = (clone $base)->whereDate('date', $todayDate)->orderBy('id', 'desc')->get();
        $yesterday = (clone $base)->whereDate('date', $yesterday)->orderBy('id', 'desc')->get();
        $overdue = (clone $base)->whereDate('date', '<', $todayDate)->orderBy('date', 'asc')->orderBy('id', 'desc')->get();

        return response()->json([
            'today' => [
                'count' => $today->count(),
                'records' => $today
            ],
            'yesterday' => [
                'count' => $yesterday->count(),
                'records' => $yesterday
            ],
            'overdue' => [
                'count' => $overdue->count(),
                'records' => $overdue
            ],
        ]);
    }

    public function store(Request $request) 
    {
        $validated = $request->validate([
            "number" => "required",
            "date" => "required|date_format:Y-m-d H:i:s",
            "created_by" => "required|integer|exists:users,id"
        ]);

        $validated["status"] = "pending";

	//$duplicate = ScheduleCallBack::where('number', $validated['number'])->where('date', $validated['date'])->first();
        $duplicate = ScheduleCallBack::where('number', $validated['number'])->where('date', $validated['date'])->where('created_by', $validated['created_by'])->first();

        if($duplicate && $duplicate->id) {
	        return response()->json(["error" => "Duplicate record found for the same datetime"], 409);
        }

        $callback = ScheduleCallBack::create($validated);

        if($callback && $callback->id) {
            return response()->json([
                'message' => 'Schedule call back created successfully',
                'data' => $callback
            ], 201);
        }

        return response()->json(["error" => "Failed to create new record"], 422);
    }

    public function changeStatus(Request $request)
    {
        $validated = $request->validate([
            'id'     => 'required|integer|exists:schedule_call_backs,id',
            'agent' => 'required|integer|exists:users,id',
            'status' => ['required', Rule::in(['pending', 'completed', 'cancelled', 'answer', 'noanswer', 'redial'])],
            'uniqueid' => 'nullable|string'
        ]);

        $record = ScheduleCallBack::find($validated['id']);

        if (!$record) {
            return response()->json(['error' => 'Record not found'], 404);
        }

        if(isset($validated['uniqueid']) && $validated['uniqueid'] != "") {
            $cdr = Cdr::where('uniqueid', $validated['uniqueid'])->first();
            
            if ($cdr && $cdr->disposition === 'ANSWERED') {
                $validated['status'] = 'completed';
            } else {
                $validated['status'] = 'noanswer';
            }

            $record->update(['called_by'=>$validated['agent'], 'status'=>$validated['status'], 'uniqueid'=>$validated['uniqueid']]);
        }
        else {
            $record->update(['called_by'=>$validated['agent'], 'status'=>$validated['status']]);
        }

        // if ($record->status === $validated['status']) {
        //     return response()->json([
        //         'message' => 'Status already set',
        //         'data'    => $record
        //     ], 200);
        // }

        return response()->json([
            'message' => 'Status updated successfully',
            'data'    => $record->fresh()
        ], 200);
    }

    public function delete(Request $request)
    {
        $validated = $request->validate([
            'id'     => 'required|integer|exists:schedule_call_backs,id',
        ]);

        $record = ScheduleCallBack::find($validated['id']);
        
        if (!$record) {
            return response()->json(['error' => 'Record not found'], 404);
        }

        if($record->delete()) {
            return response()->json([
                'message' => 'record deleted successfully'
            ], 200);
        }
        
        return response()->json([
            'message' => 'Failed to delete record'
        ], 200);
    }
}
