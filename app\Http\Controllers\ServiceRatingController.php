<?php

namespace App\Http\Controllers;

use App\Exports\ServiceRatingExport;
use App\Models\ServiceRating;
use App\Models\ServiceRatingFile;
use App\Models\ServiceRatingSetting;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use DB;

class ServiceRatingController extends Controller
{

    public function __construct() {

        $this->middleware('permission:update_settings', ['only' => ['update']]);
        $this->middleware('permission:read_settings', ['only' => ['index']]);
    }

    public function getRatings(Request $request)
    { 
        try {
            // $ratings = ServiceRating::query()->leftJoin('users', 'agentId', '=', 'auth_username')->select(['service_ratings.*', 'users.name'])->orderBy('service_ratings.created_at', 'desc');
            $ratings = ServiceRating::query()
                                    ->leftJoin('users', 'agentId', '=', 'auth_username')
                                    ->leftJoin('cdr', 'cdr.uniqueid', '=', 'service_ratings.uniqueid')
                                    ->select(['service_ratings.*', 'users.name', 'cdr.recordingfile'])
                                    ->orderBy('service_ratings.created_at', 'desc')
                                    ->groupBy('service_ratings.id');

            if($request->has('startDate'))
                $ratings->whereDate('service_ratings.created_at', '>=', $request->startDate);
            if($request->has('endDate'))
                $ratings->whereDate('service_ratings.created_at', '<=', $request->endDate);
            if($request->has('agent'))
                $ratings->whereIn('agentId', $request->agent);
            if($request->has('rating'))
                $ratings->whereIn('rating', $request->rating);
            if($request->has('uniqueid'))
                $ratings->where('uniqueid', 'LIKE', "%$request->uniqueid%");
            if($request->has('export'))
                return Excel::download(new ServiceRatingExport($ratings->get()), 'service_ratings.xlsx');
                return response()->json($ratings->paginate($request->perPage ?? 10 ));
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getFiles(): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(ServiceRatingFile::query()->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getSettings(): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(ServiceRatingSetting::query()->first());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function uploadFile(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'file' => ['required', 'file', 'mimes:wav']
        ]);

        try {
            $status = $request->file('file')->storeAs('service_rating', $request->file('file')->getClientOriginalName(), 'sounds');
            if($status) {
                $model = new ServiceRatingFile;
                $model->name = $request->file('file')->getClientOriginalName();
                $model->path = $status;
                $model->fileloc = explode('.', $status)[0];
                $model->save();
            }
            return response()->json($status);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function updateSettings(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'file' => ['required'],
            'status' => ['required', 'boolean'],
            'best' => ['required'],
            'worst' => ['required']
        ]);

        try {
            $settings = ServiceRatingSetting::query()->first();
            $settings->update($request->all());
            $settings->service_rating_file_id = $request->file;
            $settings->save();
            return response()->json("Service rating settings updated successfully.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
