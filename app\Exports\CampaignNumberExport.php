<?php

namespace App\Exports;

use App\Models\CampaignNumber;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;


class CampaignNumberExport implements FromArray, WithHeadings, WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */
    // public function collection()
    // {
    //     //return CampaignNumber::all();
    // }

    public function __construct(array $data) 
    {
        $this->data = $data;
    }

    public function array(): array
    {
        return $this->data;
    }

    public function map($data): array
    {
        return[
            $data['number'],
            $data['name'] ?? null,
            $data['city'] ?? null,
            $data['dial_time'] ?? null,
            $data['duration'] ?? null,
            $data['billsec'] ?? null,
            $data['disposition'] ?? null,
            $data['created_at'],

        ];
    }

    public function headings(): array 
    {
        return[
            'Number',
            'Name',
            'City',
            'Dial Time',
            'Duration',
            'Bill Seconds',
            'Disposition',
            'Created At'
        ];
    }
}
