<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Hash;
use DB;
use Illuminate\Support\Facades\Hash as FacadesHash;

class AuthController extends Controller
{
    public function supervisorLogin(Request $request) :\Illuminate\Http\JsonResponse
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required',
        ]);
        $credentials = $request->only('username', 'password');
        $credentials = array_merge($credentials, ['type' => 'supervisor']);
        if (Auth::attempt($credentials)) {
            $token = $request->user()->createToken("token")->plainTextToken;
            return response()->json([
                'token' => $token,
                'token_type' => 'Bearer',
                'user' => $request->user()
            ]);
        }

        return response()->json(["message" => "credentials doesn't match"], 500);
    }

    public function Login(Request $request)
    {
       
        $attr = $request->validate([
            'username' => 'required',
            'password' => 'required'
        ]);

        if(Auth::attempt($attr)){
            $type = auth()->user()->type;
            if($type === 'Normal' || $type === 'supervisor')
            {

                $users = Cache::get('logged_in_users', []);
                if (!isset($users[$request->user()->id])) {
                    $users[$request->user()->id] = [
                        'user_id' => $request->user()->id,
                        'currentPage' => 1,
                        'pageSize' => 10,
                        'timestamp' => now(),
                    ];

                    Cache::put('logged_in_users', $users);
                }

                return response()->json([
                    "id" => $request->user()->id,
                    "username" => $request->user()->username,
                    "token" => $request->user()->createToken('token')->plainTextToken
                ]);
            }
        }
        return response()->json(['message' =>'invalid crediential'],401);
    }

    public function LoginBKP(Request $request)
    {
        
        $attr = $request->validate([
            'username' => 'required',
            'password' => 'required'
        ]);

        if(Auth::attempt($attr)){
            $type = auth()->user()->type;
            if($type === 'Normal')
            {
                return response()->json([
                    "id" => $request->user()->id,
                    "username" => $request->user()->username,
                    "token" => $request->user()->createToken('token')->plainTextToken
                ]);
            }
        }
        return response()->json(['message' =>'invalid crediential'],401);
    }

    // fdsf
    public function LoginAgent(Request $request)
    {
        $attr = $request->validate([
            'username' => 'required',
            'password' => 'required'
        ]);

        if(Auth::attempt($attr)){
            $type = auth()->user()->type;
            if($type !== 'Normal' && $type !== 'supervisor')
            {
                $users = Cache::get('logged_in_users', []);
                if (!isset($users[$request->user()->id])) {
                    $users[$request->user()->id] = [
                        'user_id' => $request->user()->id,
                        'currentPage' => 1,
                        'pageSize' => 10,
                        'timestamp' => now(),
                    ];

                    Cache::put('logged_in_users', $users);
                }

                return response()->json([
                    "id" => $request->user()->id,
                    "username" => $request->user()->username,
                    "token" => $request->user()->createToken('token')->plainTextToken
                ]);
            }
        }

        return response()->json(['message' => 'Invalid credentials entered.'],401);
    }

    public function LogoutUser(Request $request)
    {
      
    	$userId = auth()->user()->id;
        auth()->user()->tokens()->delete();

        $users = Cache::get('logged_in_users', []);
        if (isset($users[$userId])) {
            unset($users[$userId]);
            Cache::put('logged_in_users', $users);
        }

        return response()->json(['message' => 'Tokens Revoked']);
    }

    public function passwordReset(Request $request ,User $user){
        //dd($user);
        $request->validate([
            'password' => 'required|confirmed',
        ]);
        try{    

            $user->password = Hash::make($request->password);
            $user->save();   
            DB::table('personal_access_tokens')->where('tokenable_id', $user->id)->delete();
            return response()->json(['message' => 'Password has been changed'],200);
      
        }catch(\Exception $e){   

            return response()->json($e->getMessage());
        }

    }

    public function isAuthenticated(Request $request)
    {
        $user = Auth::user();
    
        if ($user) {
            return response()->json([
                'status' => true,
                'message' => 'User is authenticated',
                'user' => $user
            ], 200);
        } else {
            return response()->json([
                'status' => false,
                'message' => 'User is not authenticated'
            ], 401);
        }
    }
}
