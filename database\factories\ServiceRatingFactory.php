<?php

namespace Database\Factories;

use App\Models\ServiceRating;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

class ServiceRatingFactory extends Factory
{
    protected $model = ServiceRating::class;
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'uniqueid' => Carbon::now()->timestamp,
            'rating' => random_int(1, 2),
            'number' => $this->faker->phoneNumber,
            'agentId' => '9995',
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
