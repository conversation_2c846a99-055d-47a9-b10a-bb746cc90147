<?php

namespace App\Http\Controllers;

use App\Models\PrepaidSetting;
use App\Traits\PrepaidTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PrepaidController extends Controller
{
    use PrepaidTrait;
    public function __construct()
    {
        $this->middleware('role:superadministrator', ['only' => ['getPrepaid', 'update']]);
    }

    public function getPrepaid(Request $request)
    {

        try {
            $prepaid = PrepaidSetting::select('id', 'start_date', 'pulse_duration', 'tariff', 'amount', 'type')->first();
            if (!$prepaid) {
                return response()->json(['message', 'Prepaid not found'], 404);
            }
            return response()->json(['prepaid' => $prepaid], 200);
        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    public function update(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'pulse_duration' => 'required|integer|gt:0',
            'tariff' => 'required|integer|gt:0',
            'amount' => 'required|integer|gt:0',
            'type' => 'required|in:prepaid,postpaid',
        ]);

        try {
            $setting = PrepaidSetting::first();
            if (!$setting) {
                return response()->json([
                    'message' => 'No prepaid setting'
                ], 404);
            }
           
            if ($request->type == 'prepaid' && $request->start_date != $setting->start_date) {
                $setting->update([
                    'start_date' => $request->start_date,
                    'pulse_duration' => $request->pulse_duration,
                    'tariff' => $request->tariff,
                    'amount' => $request->amount,
                    'pkg_update' => Carbon::now(),
                    'remaining_amount' => $request->amount
                ]);

                return response()->json([
                    'message' => 'Prepaid settings updated successfully',
                    'data' => $setting
                ], 200);
            }

            return response()->json([
                'message' => 'No changes made (start date must differ for prepaid updates)',
                'data' => $setting
            ], 200);
        } catch (Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function switchPrepaid(Request $request)
    {
        try {
            $setting = PrepaidSetting::first();
            if (!$setting) {
                return response()->json([
                    'message' => 'No prepaid setting'
                ], 404);
            }
            $setting->type = $request->type;
            $setting->save();

            return response()->json([
                'message' => "Switched to {$request->type} mode successfully",
                'data' => $setting
            ], 200);
        } catch (Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    


    public function bilSum()
    {
        try {

            $this->billSum(); //it's a trait this being use in serverhandlers to

            return response()->json(['prepaid' => PrepaidSetting::first()]);
        } catch (Exception $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }
    // befor agentlesscampaign 
    // public function bilSumOld()
    // {
    //     $prepaid = PrepaidSetting::first();

    //     if (!$prepaid) {
    //         return 0;
    //     }

    //     $calls = DB::table('cdr')->where([
    //         ['disposition', '=', 'ANSWERED'],
    //         ['answer', '>=', $prepaid->pkg_update]
    //     ])->pluck('billsec');

    //     $pulseDuration = $prepaid->pulse_duration;
    //     $tariff = $prepaid->tariff;
    //     $totalBillingSum = 0;
    //     foreach ($calls as $callDuration) {
    //         $numPulses = ceil($callDuration / $pulseDuration);
    //         $totalBillingSum += $numPulses * $tariff;
    //     }
    //     $prepaid->remaining_amount = $prepaid->amount - $totalBillingSum;
    //     $prepaid->save();
    //     return response()->json(['prepaid' => $prepaid],200);
    // }


    public function checkRemainingMinutesAndNotifyOLD()
    {
        $prepaid = PrepaidSetting::first();
        if (!$prepaid) {
            return response()->json(['error' => 'No prepaid settings found'], 404);
        }
        $totalAmount = $prepaid->amount;
        $pulseDuration = $prepaid->pulse_duration;
        $tariff = $prepaid->tariff;
        $calls = DB::table('cdr')->where([
            ['disposition', '=', 'ANSWERED'],
            ['answer', '>', $prepaid->pkg_update]
        ])->pluck('duration');
        $totalBillingSum = 0;
        foreach ($calls as $callDuration) {
            $numPulses = ceil($callDuration / $pulseDuration);
            $totalBillingSum += $numPulses * $tariff;
        }
        $totalMinutesAvailable = ($totalAmount / $tariff) * $pulseDuration / 60;
        $totalMinutesAvailable = floor($totalMinutesAvailable);
        $percentageUsed = ($totalBillingSum / $totalAmount) * 100;
        if ($percentageUsed >= 80) {
            return response()->json([
                'remaining_minutes' => $totalMinutesAvailable,
                'message' => "You have {$totalMinutesAvailable} minutes left.",
                'status' => 'warning'
            ], 200);
        }
        return response()->json([
            'remaining_minutes' => $totalMinutesAvailable,
            'message' => 'Your balance is sufficient.',
            'status' => 'success'
        ], 200);
    }

    public function checkRemainingMinutesAndNotifyBKP()
    {
        $prepaid = PrepaidSetting::first();
        if (!$prepaid) {
            return response()->json(['error' => 'No prepaid settings found'], 404);
        }

        $pulseDuration = $prepaid->pulse_duration;
        $tariff = $prepaid->tariff;
        $remainingAmount = $prepaid->remaining_amount;

        $costPerSecond = $tariff / $pulseDuration;

        $callDuration = $pulseDuration;
        $callCost = $tariff;

        $remainingAmount -= $callCost;

        if ($remainingAmount <= 0) {
            return response()->json([
                'remaining_minutes' => 0,
                'message' => 'Insufficient balance.',
                'status' => 'error'
            ]);
        }

        $data = $this->calculateTotalMinutes($remainingAmount, $costPerSecond);
        $totalSecondsAvailable = $data['seconds'];
        $totalMinutesAvailable = $data['minutes'];

        $calls = DB::table('cdr')
            ->where([
                ['disposition', '=', 'ANSWERED'],
                ['answer', '>', $prepaid->pkg_update]
            ])
            ->pluck('billsec');

        $totalUsedSeconds = $calls->sum();
        $usagePercentage = min(($totalUsedSeconds / $totalSecondsAvailable) * 100, 100);

        if ($usagePercentage >= 80) {
            return response()->json([
                'remaining_minutes' => round($totalMinutesAvailable, 2),
                'message' => "You have " . round($totalMinutesAvailable, 2) . " minutes left. You have used " . round($usagePercentage, 2) . "% of your balance.",
                'status' => 'warning'
            ], 200);
        }

        return response()->json([
            'remaining_seconds' => $data['seconds'],
            'remaining_minutes' => round($data['minutes'], 2),
            'usage_percentage' => round($usagePercentage, 2),
            'message' => 'Your balance is sufficient.',
            'status' => 'success'
        ]);
    }

    function calculateTotalMinutesBKP($remainingAmount, $costPerSecond)
    {
        $totalSeconds = $remainingAmount / $costPerSecond;
        $totalMinutes = $totalSeconds / 60;
        return [
            'seconds' => $totalSeconds,
            'minutes' => $totalMinutes
        ];
    }
    // this runing 
    public function checkRemainingMinutesAndNotify()
    {
        $prepaid = PrepaidSetting::first();
        if (!$prepaid) {
            return response()->json(['error' => 'No prepaid settings found'], 404);
        }

        $pulseDuration = $prepaid->pulse_duration;
        $tariff = $prepaid->tariff;
        $remainingAmount = $prepaid->remaining_amount;

        $costPerSecond = $tariff / $pulseDuration;

        $callCost = $tariff;
        $remainingAmount -= $callCost;

        if ($remainingAmount <= 0) {
            return response()->json([
                'remaining_minutes' => 0,
                'message' => 'Insufficient balance.',
                'status' => 'error'
            ]);
        }

        $data = $this->calculateTotalMinutes($remainingAmount, $costPerSecond);
        $totalSecondsAvailable = $data['seconds'];
        $totalMinutesAvailable = $data['minutes'];

        $calls = DB::table('cdr')
            ->where([
                ['disposition', '=', 'ANSWERED'],
                ['answer', '>', $prepaid->pkg_update]
            ])
            ->pluck('billsec');

        $totalUsedSeconds = $calls->sum();

        $usagePercentage = min(($totalUsedSeconds / $totalSecondsAvailable) * 100, 100);

        if ($usagePercentage >= 80) {
            return response()->json([
                'remaining_minutes' => round($totalMinutesAvailable, 2),
                'message' => "You have " . round($totalMinutesAvailable, 2) . " minutes left. You have used " . round($usagePercentage, 2) . "% of your balance.",
                'status' => 'warning'
            ], 200);
        }

        return response()->json([
            'remaining_seconds' => round($data['seconds'], 2),
            'remaining_minutes' => round($data['minutes'], 2),
            'usage_percentage' => round($usagePercentage, 2),
            'message' => 'Your balance is sufficient.',
            'status' => 'success'
        ]);
    }

    function calculateTotalMinutes($remainingAmount, $costPerSecond)
    {
        $totalSeconds = $remainingAmount / $costPerSecond;
        $totalMinutes = $totalSeconds / 60;
        return [
            'seconds' => $totalSeconds,
            'minutes' => $totalMinutes
        ];
    }
}
