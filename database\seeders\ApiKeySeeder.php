<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Support\Facades\DB;

class ApiKeySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('bitrix_integrations')->insert([
            'api_key' => 'https://b24-yuhn1m.bitrix24.com/rest/1/eix4apa6o3jvxg2j',
            'is_enable' => 0,
            
        ]);
    }
}
