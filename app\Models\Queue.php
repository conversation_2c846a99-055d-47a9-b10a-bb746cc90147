<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Queue extends Model
{
    use HasFactory;
    public $timestamps = false;
    protected $guarded = [];
    protected $primaryKey = 'name';

    public $incrementing = false;

    // In Laravel 6.0+ make sure to also set $keyType
    protected $keyType = 'string';

    public function users()
    {
        return $this->belongsToMany(User::class);
    }
    


    // public function users(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    // {
    //     return $this->belongsToMany(User::class);
    // }

    public function forms(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Form::class);
    }

    public function inbound()
    {
        return $this->morphOne(Inbound::class, 'module', 'module_type', 'module_id');
    }

    public static function getPossibleEnumValuesOld ($column) {
    // Create an instance of the model to be able to get the table name
        $instance = new static;

        $arr = DB::select(DB::raw('SHOW COLUMNS FROM '.$instance->getTable().' WHERE Field = "'.$column.'"'));
        if (count($arr) == 0){
            return array();
        }
        // Pulls column string from DB
        $enumStr = $arr[0]->Type;

        // Parse string
        preg_match_all("/'([^']+)'/", $enumStr, $matches);

        // Return matches
        return isset($matches[1]) ? $matches[1] : [];
    }

    public static function getPossibleEnumValues($column)
        {
            // Create an instance of the model
            $instance = new static;
            $table = $instance->getTable();

            // Use parameterized query to prevent SQL injection
            $query = "SHOW COLUMNS FROM `{$table}` WHERE Field = ?";
            $arr = DB::select($query, [$column]);

            if (count($arr) === 0) {
                return []; // No matching column found
            }

            // Extract enum type definition
            $enumStr = $arr[0]->Type;

            // Parse enum values
            preg_match_all("/'([^']+)'/", $enumStr, $matches);

            // Return parsed values or an empty array if none found
            return $matches[1] ?? [];
        }



}
