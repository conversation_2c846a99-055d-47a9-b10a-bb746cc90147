<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Navbar extends Model
{
    use HasFactory;
    protected $table = 'navbar';
    protected $primaryKey = 'navbar_id';

    protected $casts = [
        'module_accessibility' => 'boolean',
    ];

    public function parent()
    {
        return $this->belongsTo(Navbar::class, 'parent_id');
    }

    /**
     * Get the child menu items.
     */
    public function children()
    {
        return $this->hasMany(Navbar::class, 'parent_id');
    }
}
