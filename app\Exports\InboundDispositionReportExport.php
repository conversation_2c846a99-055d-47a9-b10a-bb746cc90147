<?php

namespace App\Exports;

use DateTime;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class InboundDispositionReportExport implements  WithHeadings, WithMapping,FromArray
{
    /**
     * @return \Illuminate\Support\Collection
     */
 

    function __construct(array $data)
    {
        $this->data= $data;
    }
    public function array(): array
    {
        return $this->data;
    }
 

    public function map($data): array
    {   
        return [
            $data['uniqueid'],
            $data['src'],
            $data['dstchannel'],
            $data['date_time'],
            $data['call_status'],
            $data['workcode'],
            $data['agent']
        ];
    }

    public function headings(): array
    {
        return [
            'uniqueid',
            'src',
            'dstchannel',
            'date_time',
            'call_status',
            'workcode',
            'agent'
        ];
    }
}