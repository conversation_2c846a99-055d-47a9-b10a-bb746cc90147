<?php

namespace App\Jobs;

use App\Events\AgentlessCampaignUpdate;
use App\Models\AgentlessCampaign;
use App\Models\AgentlessCampaignLog;
use App\Models\AgentlessCampaignNumber;
use App\Models\CallingServer;
use App\Models\PrepaidSetting;
use App\Models\Signal;
use App\Traits\PrepaidTrait;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use PAMI\Client\Exception\ClientException;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\CommandAction;

class AgentlessServerHandlerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, PrepaidTrait;

    /**
     * Create a new job instance.
     */

    public int $server;
    public int $campaign;
    private int $limit = 200;
    public $timeout = 27000;

    public function __construct($server, $campaign)
    {
        $this->server = $server;
        $this->campaign = $campaign;
    }


    public function tags()
    {
        return ['server_handler_job:' . $this->server, 'campaign:' . $this->campaign];
    }

    private function checkTerminateSignal()
    {
        $signal = Signal::query()->where('campaign_id', $this->campaign)->where('signal', 'terminate')->orderBy('created_at', 'desc')->first();
        if ($signal) {
            return true;
        }
        return false;
    }


    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // ServerLog::query()->create(['calling_server_id' => $this->server, 'log' => 'started']);
            $server = CallingServer::find($this->server)->toArray();
            $numbers = AgentlessCampaignNumber::where('campaign_id', $this->campaign)->pluck('number')->toArray();
            $numberCount = count($numbers);
            $jobs = 0;
            $sum = 0;
            $limit = $server['allowed_limit'];
            AgentlessCampaign::where('id', $this->campaign)->update(['stage' => 'in progress']);
            broadcast(new AgentlessCampaignUpdate(AgentlessCampaign::orderBy('created_at', 'desc')->get()));
           
            $prepaid = PrepaidSetting::first();
            while ($jobs < $numberCount) {

                if ($this->checkTerminateSignal()) {
                    // Terminate campaign
                    break;
                }
                // if remaining amount is 0 then break
                $this->billSum(); //this is tarit it's being use in prapid bilsum api too 
               
                $stopThreshold = $prepaid->amount * 0.02;
                if ($prepaid->remaining_amount <= $stopThreshold) {
                    break;
                }

                $callCount = $this->callCount($server);
              
                $numberOfJobToDispatch = $numberCount < $limit ? $numberCount : $limit - $callCount;
                for ($i = $jobs; $i < $jobs + $numberOfJobToDispatch; $i++) {
                    if ($i < $numberCount) {
                        DispatchNumberJob::dispatch($numbers[$i], $server, $this->campaign)->onQueue("default");
                        usleep(250000);
                        $sum++;
                    }
                }
                $jobs = $sum;
            }

            usleep(60 * 1000000);

            // ServerLog::query()->create(['calling_server_id' => $this->server, 'log' => 'ended']);
            AgentlessCampaignLog::query()->create(['campaign_id' => $this->campaign, 'log' => 'ended', 'created_at' => Carbon::now()->addSecond()]);
            AgentlessCampaign::where('id', $this->campaign)->update(['stage' => $prepaid->remaining_amount <= ($prepaid->amount * 0.02) ? 'no amount' :   'ended', 'status' => false]);
            // AgentlessCampaign::where('id' , $this->campaign)->update(['stage' => "ended" , 'status' => false]);
            broadcast(new AgentlessCampaignUpdate(AgentlessCampaign::orderBy('created_at', 'desc')->get()));

            //    billSum for last call 

            $this->billSum();

        } catch (\Exception $e) {
            Log::info('AgentlessServerHandlerJob:error' . $e->getMessage());
        }
    }

    private function callCount($server): int
    {
        $client = new ClientImpl($server);
        $action = new CommandAction('core show calls');
        $client->open();
        $res = $client->send($action);
        $client->close();
        return (int) explode(' ', $res->getKey('output')[0])[0];
    }
}
