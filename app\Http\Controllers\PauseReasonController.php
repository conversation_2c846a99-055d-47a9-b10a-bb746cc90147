<?php

namespace App\Http\Controllers;

use App\Models\PauseReason;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class PauseReasonController extends Controller
{

    public function __construct() {

        $this->middleware('permission:create_pausedreason', ['only' => ['store']]);
        $this->middleware('permission:update_pausedreason', ['only' => ['update']]);
        $this->middleware('permission:delete_pausedreason', ['only' => ['destroy']]);
        $this->middleware('permission:read_pausedreason', ['only' => ['index']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        return response()->json(PauseReason::query()->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'unique:pause_reasons,name']
        ]);
        try {
            $reason = PauseReason::query()->create($request->all());
            return response()->json("Reason {$reason->name} has been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param PauseReason $pauseReason
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, PauseReason $pauseReason): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'name' => ['required', 'string', Rule::unique('pause_reasons', 'name')->ignoreModel($pauseReason)]
        ]);

        try {
            $reason = $pauseReason->update($request->all());
            return response()->json("Reason {$pauseReason->name} has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param PauseReason $pauseReason
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(PauseReason $pauseReason): \Illuminate\Http\JsonResponse
    {
        try {
            $pauseReason->delete();
            return response()->json("Reason {$pauseReason->name} has been deleted.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }
}
