<?php

namespace Database\Seeders;

use App\Models\EmailSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EmailSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        EmailSetting::query()->upsert([
            ['key' => 'host', 'value' => 'imap.gmail.com', 'type' => 'text'],
            ['key' => 'port', 'value' => '993', 'type' => 'text'],
            ['key' => 'encryption', 'value' => 'ssl', 'type' => 'text'],
            ['key' => 'email', 'value' => '<EMAIL>', 'type' => 'email'],
            ['key' => 'password', 'value' => 'gsstqpimydlupqvd', 'type' => 'password'],
            ['key' => 'sending_host', 'value' => 'smtp.gmail.com', 'type' => 'text'],
            ['key' => 'sending_port', 'value' => '993', 'type' => 'text'],
            ['key' => 'sending_encryption', 'value' => 'tls', 'type' => 'text'],
            ['key' => 'sending_email', 'value' => '<EMAIL>', 'type' => 'email'],
            ['key' => 'sending_password', 'value' => 'gsstqpimydlupqvd', 'type' => 'password'],
        ],['key', 'value', 'type'], ['key', 'value','type']);
    }
}
