<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCdrChangesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('cdr_changes'))
        {
            Schema::create('cdr_changes', function (Blueprint $table) {
                $table->id();
                $table->string('cdr_id')->index(); // The unique identifier for the CDR (e.g., uniqueid)
                $table->timestamp('changed_at')->default(DB::raw('CURRENT_TIMESTAMP')); // The time the change occurred
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cdr_changes');
    }
}
