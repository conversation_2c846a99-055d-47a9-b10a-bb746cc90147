<?php

namespace App\Http\Controllers;

use App\Models\CampaignLog;
use Illuminate\Http\Request;

class CampaignLogController extends Controller
{
    private $events = 'START,STOP,AGENT_START,AGENT_STOP,AGENT_PAUSE,AGENT_RESUME,AGENT_NUMBER';
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        return response()->json(CampaignLog::query()->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'event' => ['required', "in:{$this->events}"],
            'user_id' => ['required', 'exists:users,id'],
            'campaign_id' => ['required', 'exists:campaigns,id']
        ]);

        $campaignLog = new CampaignLog();
        $campaignLog->fill($request->all());
        $campaignLog->user()->associate($request->user_id);
        $campaignLog->campaign()->associate($request->campaign_id);
        $campaignLog->save();
        return response()->json("Event {$campaignLog->event} started");
    }
}
