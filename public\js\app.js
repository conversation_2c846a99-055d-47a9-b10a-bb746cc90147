/******/ (() => { // webpackBootstrap
/*!*****************************!*\
  !*** ./resources/js/app.js ***!
  \*****************************/
// import { createApp } from 'vue';

// import { InertiaApp } from '@inertiajs/inertia-vue';
// import { InertiaForm } from 'laravel-jetstream';
// // import AppLayout from '@/Layouts/AppLayout';
// // import SectionBorder from '@/Jetstream/SectionBorder';

// import PortalVue from 'portal-vue';

// const app = document.getElementById('app');

// createApp({
//     render: () =>
//         h(InertiaApp, {
//             initialPage: JSON.parse(app.dataset.page),
//             resolveComponent: (name) => require(`./Pages/${name}`).default,
//         }),
// })
//     .use(InertiaApp)
//     .use(InertiaForm)
//     .use(PortalVue)
//     .mount(app);

// import { createApp, h } from 'vue';
// import { app } from '@inertiajs/inertia-vue3'; 

createApp({
  render: function render() {
    return h(App, props);
  }
}).use(plugin).mount(el);
/******/ })()
;