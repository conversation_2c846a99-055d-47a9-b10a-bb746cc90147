<?php

namespace App\Http\Controllers;

use App\Models\Supervisor;
use App\Models\SystemSetting;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class SupervisorController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = User::query()->where('type', '=', 'supervisor')->get();
        $data = $collection->transform(function ($item, $key) {
            $item->queue = $item->queues()->first()->name ?? "";
            return $item;
        });
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'min:3', 'unique:users'],
            'email' => ['required', 'email', 'unique:users,email', 'regex:/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'],
            'password' => ['string', 'min:4', 'confirmed', "required"],
            'auth_username' => ['required', 'min:3', 'string', 'not_in:2','unique:users,auth_username'],
            'auth_password' => ['required', 'min:3', 'string', 'not_in:2','unique:users,auth_password'],
        ]);
        try {
            $data = array_merge($request->except('password'), ['password' => Hash::make($request->password)]);
            $data = array_merge($data, ["type" => "supervisor"]);
            $user = User::query()->create($data);

            if ($user) {
                $user->syncRoles(['supervisor']);
            }

            DB::table('ps_aors')->insert([
                'id' => $request->auth_username,
                'max_contacts' => 1,
                'remove_existing' => 'yes'
            ]);
            DB::table('ps_auths')->insert([
                'id' => $request->auth_username,
                'auth_type' => 'userpass',
                'password' => $request->auth_password,
                'username' => $request->auth_username
            ]);
            DB::table('ps_endpoints')->insert([
                'id' => $request->auth_username,
                'transport' => 'transport-wss',
                'aors' => $request->auth_username,
                'context' => 'default',
                'disallow' => 'all',
                'allow' => 'alaw,ulaw,opus',
                'direct_media' => 'no',
                'webrtc' => 'yes'
            ]);
            return response()->json("Supervisor {$user->name} has been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Supervisor  $supervisor
     * @return \Illuminate\Http\Response
     */
    public function show(Supervisor $supervisor)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        return response()->json(User::query()->where('id', $id)->first());
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $supervisor
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, User $supervisor)
    {
        $supervisor = User::query()->where('id', $request->id)->first();
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'min:3', Rule::unique('users')->ignore($supervisor)],
            'email' => ['required', 'email', Rule::unique('users')->ignore($supervisor), 'regex:/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'],
            'auth_username' => ['required', 'min:3', 'numeric', Rule::unique('users')->ignore($supervisor)],
            'auth_password' => ['required', 'min:3', 'numeric', Rule::unique('users')->ignore($supervisor)],
            'queue' => ['sometimes', 'exists:queues,name']
        ]);
        try {
            if ($request->exists('auth_username')) {
                DB::table('ps_endpoints')->where('id', $supervisor->auth_username)->update(['aors' => $request->auth_username, 'id' => $request->auth_username]);
                DB::table('ps_auths')->where('id', $supervisor->auth_username)->update(['username' => $request->auth_username, 'id' => $request->auth_username]);
                DB::table('ps_aors')->where('id', $supervisor->auth_username)->update(['id' => $request->auth_username]);
            }
            if ($request->exists('auth_password')) {
                DB::table('ps_auths')->where('id', $supervisor->auth_username)->update(['password' => $request->auth_password, 'id' => $request->auth_username]);
            }
            $data = $request->all();
            $data = array_merge($data, ["type" => "supervisor"]);
            $supervisor->update($data);
            return response()->json("Supervisor {$supervisor->name} has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(User $user)
    {
        try {
            DB::table('ps_endpoints')->delete(['id' => $user->auth_username]);
            DB::table('ps_aors')->delete(['id' => $user->auth_username]);
            DB::table('ps_auths')->delete(['id' => $user->auth_username]);
            $user->queues()->detach();
            $user->delete();
            return response()->json("Supervisor {$user->name} has been deleted");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
