<?php

namespace App\Exports;

use DateTime;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OutboundDispositionReportExport implements  WithHeadings, WithMapping,FromArray
{
    /**
     * @return \Illuminate\Support\Collection
     */
 

    function __construct(array $data)
    {
        $this->data= $data;
    }
    public function array(): array
    {
        return $this->data;
    }
 

    public function map($data): array
    {   
        return [
            $data['uniqueid'],
            $data['channel'],
            $data['dst'],
            $data['start'],
            $data['disposition'],
            $data['workcode']
        ];
    }

    public function headings(): array
    {
        return [
            'Unique Id',
            'Source',
            'Destination',
            'Date Time',
            'Call Status',
            'Workcode'
        ];
    }
}