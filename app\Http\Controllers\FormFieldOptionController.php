<?php

namespace App\Http\Controllers;

use App\Models\FormFieldOption;
use Illuminate\Http\Request;

class FormFieldOptionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\FormFieldOption  $formFieldOption
     * @return \Illuminate\Http\Response
     */
    public function show(FormFieldOption $formFieldOption)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\FormFieldOption  $formFieldOption
     * @return \Illuminate\Http\Response
     */
    public function edit(FormFieldOption $formFieldOption)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\FormFieldOption  $formFieldOption
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, FormFieldOption $formFieldOption)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\FormFieldOption  $formFieldOption
     * @return \Illuminate\Http\Response
     */
    public function destroy(FormFieldOption $formFieldOption)
    {
        //
    }
}
