<?php

namespace App\Console\Commands;

use App\Models\SystemSetting;
use Illuminate\Console\Command;
use PAMI\Client\Exception\ClientException;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\CommandAction;

class CoreReload extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'core:reload';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Core reload asterisk server.';

    /**
     * Get PAMI client
     * @return ClientImpl
     */
    public function getClient($options): ClientImpl
    {
        return new ClientImpl($options);
    }

    /**
     * Get manager options
     * @return array
     */
    public function getManagerOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     * @throws ClientException
     */
    public function handle()
    {
        $client = $this->getClient($this->getManagerOptions());
        $client->open();
        $action = new CommandAction('pjsip reload');
        $client->send($action);
        $client->close();
        return 0;
    }
}
