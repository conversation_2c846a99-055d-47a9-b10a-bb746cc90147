<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cdr extends Model
{
    use HasFactory;

    protected $table = 'cdr';

    public function getChannelAttribute($value)
    {
        return explode('-', explode('/', $value)[1])[0];
    }

    public function getDstchannelAttribute($value)
    {
        try {
            return explode('-', explode('/', $value)[1])[0];
        } catch (\Exception $exception) {
            return $value;
        }
    }
}
