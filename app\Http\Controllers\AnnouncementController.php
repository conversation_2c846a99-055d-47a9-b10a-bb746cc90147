<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\Announcement;


class AnnouncementController extends Controller
{
    public function announcementList()
    {
        $data = Announcement::all();
        return response()->json([
            'data' => $data
        ], 200);
    }

    public function storeBKP(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'queue' => 'required|string',
            'media' => 'required|mimes:wav,mp3',
            'time_interval' => 'nullable|integer|min:0',
        ]);

        if ($request->hasFile('media')) {
            $file = $request->file('media');
            $fileName = $file->getClientOriginalName();
            $destinationPath = '/var/lib/asterisk/sounds/en/announcements/';

            $file->move($destinationPath, $fileName);
            $fileNameWithoutExt = pathinfo($fileName, PATHINFO_FILENAME);

            $announcement = Announcement::updateOrCreate(
                ['name' => $request->name],
                [
                    'queue' => $request->queue,
                    'file_location' => 'announcements/' . $fileNameWithoutExt,
                    'file_path' => 'announcements/' . $fileName,
                    'time_interval' => $request->time_interval,
                ]
            );

            return response()->json([
                'message' => 'Announcement uploaded successfully!',
                'data' => $announcement
            ], 201);
        }

        return response()->json(['message' => 'File upload failed'], 500);
    }


    public function store(Request $request)
    {
        if (!$request->id && Announcement::where('queue', $request->queue)->exists()) {
            return response()->json([
                'message' => 'Queue already exists.',
                'errors' => [
                    'queue' => ['This queue already exists.']
                ]
            ], 422);
        }

        $request->validate([
            'name' => 'required|string',
            'queue' => 'required|string|unique:announcements,queue,' . ($request->id ?? ''),
            'media' => $request->id ? 'nullable|mimes:wav' : 'required|mimes:wav',
            'time_interval' => 'nullable|integer|min:0',
        ]);


        // Find announcement by ID if provided, otherwise by name
        $announcement = $request->id
            ? Announcement::find($request->id)
            : Announcement::where('name', $request->name)->first();

        $filePath = $announcement->file_path ?? null;
        $fileLocation = $announcement->file_location ?? null;

        if ($request->hasFile('media')) {
            $file = $request->file('media');
            $fileName = $file->getClientOriginalName();
            $destinationPath = '/var/lib/asterisk/sounds/en/announcements/';

            $file->move($destinationPath, $fileName);
            $fileNameWithoutExt = pathinfo($fileName, PATHINFO_FILENAME);

            $filePath = 'announcements/' . $fileName;
            $fileLocation = 'announcements/' . $fileNameWithoutExt;
        }

        $isUpdate = $announcement !== null;

        $data = [
            'name' => $request->name,
            'queue' => $request->queue,
            'file_location' => $fileLocation,
            'file_path' => $filePath,
            'time_interval' => $request->time_interval,
        ];

        if ($isUpdate) {
            $announcement->update($data);
        } else {
            $announcement = Announcement::create($data);
        }

        return response()->json([
            'message' => $request->id ? 'Announcement updated successfully!' : 'Announcement created successfully!',
            'data' => $announcement
        ], $isUpdate ? 200 : 201);
    }


    public function announcementDelete($id)
    {
        try {
            $announcement = Announcement::find($id);

            if (!$announcement) {
                return response()->json(['message' => 'Announcement not found'], 404);
            }

            $announcement->delete();
            return response()->json(['message' => 'Announcement has been deleted']);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 500);
        }
    }
}

