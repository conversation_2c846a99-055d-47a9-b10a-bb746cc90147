import imaplib
import email
import json
from email.utils import parsedate_to_datetime
from datetime import datetime
import re
from typing import Dict, List, Any
import sys
import base64

class GmailFetcher:
    def __init__(self, username: str, password: str):
        self.username = username
        self.password = password
        self.imap_server = "imap.gmail.com"
        self.connection = None

    def connect(self) -> bool:
        try:
            self.connection = imaplib.IMAP4_SSL(self.imap_server)
            self.connection.login(self.username, self.password)
            return True
        except Exception as e:
            print(f"Connection error: {str(e)}")
            return False

    def disconnect(self):
        if self.connection:
            try:
                self.connection.logout()
            except Exception:
                pass

    def fetch_emails(self, page: int = 1, per_page: int = 5) -> Dict[str, Any]:
        if not self.connect():
            return {"error": "Failed to connect to Gmail"}

        try:
            self.connection.select("INBOX")

            # First fetch ALL messages to build complete thread structure
            _, message_ids = self.connection.search(None, "ALL")
            total_messages = len(message_ids[0].split()) if message_ids[0] else 0
            _, all_data = self.connection.fetch("1:*", "(RFC822)")

            # Process all emails to build complete thread map
            all_emails = []
            email_map = {}
            thread_roots = {}

            for i in range(len(all_data) - 1):
                if isinstance(all_data[i], tuple):
                    raw_email = all_data[i][1]
                    email_message = email.message_from_bytes(raw_email)
                    email_data = self._parse_email(email_message)
                    all_emails.append(email_data)
                    email_map[email_data["message_id"]] = email_data

            # Track known roots
            email_map['_roots'] = {}

            # Build complete thread structure
            for email_data in all_emails:
                thread_id = self._get_thread_id(email_data, email_map)

                if thread_id not in thread_roots:
                    email_map['_roots'][thread_id] = True

                    if email_data["message_id"] == thread_id:
                        thread_roots[thread_id] = email_data
                    elif thread_id in email_map:
                        thread_roots[thread_id] = email_map[thread_id]
                    else:
                        thread_roots[thread_id] = email_data

                    if "replies" not in thread_roots[thread_id]:
                        thread_roots[thread_id]["replies"] = []

                if email_data["message_id"] != thread_id:
                    self._add_reply_to_thread(thread_roots[thread_id], email_data, email_map)

            # Sort all root emails by date (newest first)
            sorted_roots = sorted(
                thread_roots.values(),
                key=lambda x: datetime.strptime(x["date"], "%Y-%m-%d %H:%M:%S"),
                reverse=True
            )

            # Apply pagination to ROOT emails only
            total_parents = len(sorted_roots)
            start = (page - 1) * per_page
            end = start + per_page
            paginated_roots = sorted_roots[start:end]

            return {
                "emails": paginated_roots,
                "page": str(page),
                "per_page": str(per_page),
                "total": total_parents
            }

        except Exception as e:
            return {"error": str(e)}
        finally:
            self.disconnect()

    def _get_thread_id(self, email_data: Dict, email_map: Dict) -> str:
        """Determine the root message ID for a thread"""
        # Check if this is already a known root
        if email_data["message_id"] in email_map.get('_roots', {}):
            return email_data["message_id"]

        # First try to follow in_reply_to chain
        if email_data["in_reply_to"]:
            parent_id = email_data["in_reply_to"]
            if parent_id in email_map:
                return self._get_thread_id(email_map[parent_id], email_map)
            else:
                # If we don't have the parent, use in_reply_to as thread ID
                return parent_id

        # Then try references chain
        if email_data["references"]:
            refs = email_data["references"].split()
            if refs:
                # Return the first reference (oldest message) as thread root
                return refs[0]

        # Default to using own message_id as thread_id
        return email_data["message_id"]

    def _add_reply_to_thread(self, root_email: Dict, reply_email: Dict, email_map: Dict):
        """Recursively add a reply to the correct position in the thread"""
        # Don't add if it's already in the thread
        if self._is_email_in_thread(root_email, reply_email["message_id"]):
            return

        # Check direct parent via in_reply_to first
        parent_id = reply_email["in_reply_to"]
        if parent_id:
            # Check if parent is the root
            if parent_id == root_email["message_id"]:
                root_email["replies"].append(reply_email)
                return
            
            # Check if parent exists in our map
            if parent_id in email_map:
                parent = email_map[parent_id]
                # Verify parent is in this thread
                if self._is_email_in_thread(root_email, parent_id):
                    self._add_reply_to_thread(root_email, parent, email_map)
                    parent["replies"].append(reply_email)
                    return

        # If in_reply_to didn't work, try references
        if reply_email["references"]:
            refs = reply_email["references"].split()
            # Search references in reverse order (most recent first)
            for ref in reversed(refs):
                if ref == root_email["message_id"]:
                    # Direct reply to root
                    root_email["replies"].append(reply_email)
                    return
                if ref in email_map:
                    parent = email_map[ref]
                    if self._is_email_in_thread(root_email, ref):
                        self._add_reply_to_thread(root_email, parent, email_map)
                        parent["replies"].append(reply_email)
                        return

        # If we still haven't found a parent, add to root's replies
        root_email["replies"].append(reply_email)

    def _is_email_in_thread(self, root_email: Dict, message_id: str) -> bool:
        """Check if an email is already in the thread"""
        if root_email["message_id"] == message_id:
            return True
        for reply in root_email.get("replies", []):
            if self._is_email_in_thread(reply, message_id):
                return True
        return False

    def _parse_email(self, email_message) -> Dict[str, Any]:
        email_data = {
            "subject": self._decode_header(email_message.get("Subject", "No Subject")),
            "from": self._parse_sender(email_message.get("From", "")),
            "date": self._parse_date(email_message.get("Date", "")),
            "body": "",
            "attachment": [],
            "message_id": email_message.get("Message-ID", "").strip(),
            "in_reply_to": email_message.get("In-Reply-To", "").strip(),
            "references": email_message.get("References", "").strip(),
            "replies": []
        }

        email_data["body"], email_data["attachment"] = self._parse_email_content(email_message)
        return email_data

    def _parse_email_content(self, email_message) -> tuple:
        body = ""
        attachments = []

        for part in email_message.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get("Content-Disposition"))

            if part.is_multipart():
                continue

            if "attachment" in content_disposition:
                attachment = {
                    "name": self._decode_header(part.get_filename() or "unnamed"),
                    "content": self._get_part_content(part)
                }
                attachments.append(attachment)
                continue

            # Try to get the body from text/plain first
            if content_type == "text/plain" and not body:
                body_charset = part.get_content_charset() or "utf-8"
                try:
                    part_body = part.get_payload(decode=True).decode(body_charset, errors="replace")
                    # Clean up the body text
                    body = self._clean_email_body(part_body.strip())
                except Exception:
                    pass

            # Fall back to HTML if plain text not available
            elif content_type == "text/html" and not body:
                body_charset = part.get_content_charset() or "utf-8"
                try:
                    html_body = part.get_payload(decode=True).decode(body_charset, errors="replace")
                    # Convert HTML to clean text
                    clean_text = re.sub('<[^<]+?>', '', html_body)
                    body = self._clean_email_body(clean_text.strip())
                except Exception:
                    pass

        # If we still have no body, try the simple payload approach
        if not body and not email_message.is_multipart():
            try:
                body_charset = email_message.get_content_charset() or "utf-8"
                body = self._clean_email_body(
                    email_message.get_payload(decode=True).decode(body_charset, errors="replace"))
            except Exception:
                pass

        return body, attachments

    def _clean_email_body(self, body: str) -> str:
        """Clean up email body text"""
        if not body:
            return body

        # Remove quoted text patterns
        patterns = [
            r'On\s+.+?\s+wrote:',  # "On Wed, Jun 4, 2025 at 3:51 PM X wrote:"
            r'-----Original Message-----',
            r'From:\s+.+?[\r\n]+Sent:\s+.+?[\r\n]+To:\s+.+?[\r\n]+Subject:',
            r'_{10,}',  # Long underscores
            r'-{10,}',  # Long dashes
        ]

        for pattern in patterns:
            body = re.sub(pattern, '', body, flags=re.IGNORECASE)

        # Remove excessive whitespace
        body = re.sub(r'\n\s*\n', '\n\n', body)
        body = body.strip()

        return body

    def _get_part_content(self, part) -> str:
        """Get base64 encoded content of a part"""
        try:
            binary_content = part.get_payload(decode=True)
            if binary_content is None:
                return ""
            return base64.b64encode(binary_content).decode('utf-8')
        except Exception as e:
            print(f"Error encoding attachment: {str(e)}")
            return ""

    def _parse_sender(self, sender: str) -> str:
        match = re.search(r'<(.+?)>', sender)
        return match.group(1) if match else sender

    def _parse_date(self, date_str: str) -> str:
        try:
            dt = parsedate_to_datetime(date_str)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (TypeError, ValueError):
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _decode_header(self, header) -> str:
        if not header:
            return ""

        try:
            decoded = email.header.decode_header(header)
            return " ".join(
                str(t[0], t[1] or "utf-8") if isinstance(t[0], bytes) else t[0]
                for t in decoded
            )
        except Exception:
            return str(header)

if __name__ == "__main__":
    fetcher = GmailFetcher(sys.argv[3], sys.argv[4])
    page = int(sys.argv[1]) if len(sys.argv) > 1 else 1
    per_page = int(sys.argv[2]) if len(sys.argv) > 2 else 5
    result = fetcher.fetch_emails(page=page, per_page=per_page)
    print(json.dumps(result, ensure_ascii=True))



# import imaplib
# import email
# import json
# from email.utils import parsedate_to_datetime
# from datetime import datetime
# import re
# from typing import Dict, List, Any
# import sys
# import base64

# class GmailFetcher:
#     def __init__(self, username: str, password: str):
#         self.username = username
#         self.password = password
#         self.imap_server = "imap.gmail.com"
#         self.connection = None

#     def connect(self) -> bool:
#         try:
#             self.connection = imaplib.IMAP4_SSL(self.imap_server)
#             self.connection.login(self.username, self.password)
#             return True
#         except Exception as e:
#             print(f"Connection error: {str(e)}")
#             return False

#     def disconnect(self):
#         if self.connection:
#             try:
#                 self.connection.logout()
#             except Exception:
#                 pass

#     # def fetch_emails(self, page: int = 1, per_page: int = 5) -> Dict[str, Any]:
#     #     if not self.connect():
#     #         return {"error": "Failed to connect to Gmail"}

#     #     try:
#     #         self.connection.select("INBOX")

#     #         # First fetch ALL messages to build complete thread structure
#     #         _, message_ids = self.connection.search(None, "ALL")
#     #         total_messages = len(message_ids[0].split()) if message_ids[0] else 0
#     #         _, all_data = self.connection.fetch("1:*", "(RFC822)")

#     #         # Process all emails to build complete thread map
#     #         all_emails = []
#     #         email_map = {}
#     #         thread_roots = {}

#     #         for i in range(len(all_data) - 1):
#     #             if isinstance(all_data[i], tuple):
#     #                 raw_email = all_data[i][1]
#     #                 email_message = email.message_from_bytes(raw_email)
#     #                 email_data = self._parse_email(email_message)
#     #                 all_emails.append(email_data)
#     #                 email_map[email_data["message_id"]] = email_data

#     #         # Track known roots
#     #         email_map['_roots'] = {}

#     #         # Build complete thread structure
#     #         for email_data in all_emails:
#     #             thread_id = self._get_thread_id(email_data, email_map)

#     #             if thread_id not in thread_roots:
#     #                 email_map['_roots'][thread_id] = True

#     #                 if email_data["message_id"] == thread_id:
#     #                     thread_roots[thread_id] = email_data
#     #                 elif thread_id in email_map:
#     #                     thread_roots[thread_id] = email_map[thread_id]
#     #                 else:
#     #                     thread_roots[thread_id] = email_data

#     #                 if "replies" not in thread_roots[thread_id]:
#     #                     thread_roots[thread_id]["replies"] = []

#     #             if email_data["message_id"] != thread_id:
#     #                 self._add_reply_to_thread(thread_roots[thread_id], email_data, email_map)

#         #     # Sort all root emails by date (newest first)
#         #     sorted_roots = sorted(
#         #         thread_roots.values(),
#         #         key=lambda x: datetime.strptime(x["date"], "%Y-%m-%d %H:%M:%S"),
#         #         reverse=True
#         #     )

#         #     # Apply pagination to ROOT emails only
#         #     total_parents = len(sorted_roots)
#         #     start = (page - 1) * per_page
#         #     end = start + per_page
#         #     paginated_roots = sorted_roots[start:end]

#         #     return {
#         #         "emails": paginated_roots,
#         #         "page": str(page),
#         #         "per_page": str(per_page),
#         #         "total": total_parents
#         #     }

#         # except Exception as e:
#         #     return {"error": str(e)}
#         # finally:
#         #     self.disconnect()

#     def fetch_emails(self, page: int = 1, per_page: int = 5) -> Dict[str, Any]:
#         if not self.connect():
#             return {"error": "Failed to connect to Gmail"}

#         try:
#             self.connection.select("INBOX")
            
#             # First fetch ALL messages to build complete thread structure
#             _, message_ids = self.connection.search(None, "ALL")
#             total_messages = len(message_ids[0].split()) if message_ids[0] else 0
#             _, all_data = self.connection.fetch("1:*", "(RFC822)")
            
#             # Process all emails to build complete thread map
#             all_emails = []
#             email_map = {}
#             thread_roots = {}
            
#             for i in range(len(all_data) - 1):
#                 if isinstance(all_data[i], tuple):
#                     raw_email = all_data[i][1]
#                     email_message = email.message_from_bytes(raw_email)
#                     email_data = self._parse_email(email_message)
#                     all_emails.append(email_data)
#                     email_map[email_data["message_id"]] = email_data
            
#             # Track known roots
#             email_map['_roots'] = {}
            
#             # Build complete thread structure - FIRST PASS: identify all roots
#             for email_data in all_emails:
#                 thread_id = self._get_thread_id(email_data, email_map)
#                 if thread_id not in thread_roots:
#                     if thread_id in email_map:
#                         thread_roots[thread_id] = email_map[thread_id]
#                     else:
#                         # Create a dummy root if we don't have it
#                         thread_roots[thread_id] = {
#                             "message_id": thread_id,
#                             "subject": "Thread Root",
#                             "from": "",
#                             "date": email_data["date"],
#                             "body": "",
#                             "attachment": [],
#                             "in_reply_to": "",
#                             "references": "",
#                             "replies": []
#                         }
#                     email_map['_roots'][thread_id] = True
            
#             # SECOND PASS: build complete thread hierarchy
#             for email_data in all_emails:
#                 thread_id = self._get_thread_id(email_data, email_map)
#                 root_email = thread_roots[thread_id]
                
#                 # Skip if this is the root email itself
#                 if email_data["message_id"] == thread_id:
#                     # Update root email with actual data if we had a dummy
#                     if root_email.get("subject") == "Thread Root":
#                         root_email.update(email_data)
#                     continue
                    
#                 # Add to thread if not already present
#                 if not self._is_email_in_thread(root_email, email_data["message_id"]):
#                     self._add_reply_to_thread(root_email, email_data, email_map)
                    
#             # Sort all root emails by date (newest first)
#             sorted_roots = sorted(
#                 thread_roots.values(),
#                 key=lambda x: datetime.strptime(x["date"], "%Y-%m-%d %H:%M:%S"),
#                 reverse=True
#             )

#             # Apply pagination to ROOT emails only
#             total_parents = len(sorted_roots)
#             start = (page - 1) * per_page
#             end = start + per_page
#             paginated_roots = sorted_roots[start:end]

#             return {
#                 "emails": paginated_roots,
#                 "page": str(page),
#                 "per_page": str(per_page),
#                 "total": total_parents
#             }

#         except Exception as e:
#             return {"error": str(e)}
#         finally:
#             self.disconnect()

#     # def _get_thread_id(self, email_data: Dict, email_map: Dict) -> str:
#     #     """Determine the root message ID for a thread"""
#     #     # Check if this is already a known root
#     #     if email_data["message_id"] in email_map.get('_roots', {}):
#     #         return email_data["message_id"]

#     #     # Try to get from References header
#     #     if email_data["references"]:
#     #         refs = email_data["references"].split()
#     #         if refs:
#     #             # Use first reference as thread ID even if we don't have the message
#     #             return refs[0]

#     #     # Then try In-Reply-To
#     #     if email_data["in_reply_to"]:
#     #         # Use in_reply_to as thread ID even if we don't have the message
#     #         return email_data["in_reply_to"]

#     #     # Default to using own message_id as thread_id
#     #     return email_data["message_id"]

#     def _get_thread_id(self, email_data: Dict, email_map: Dict) -> str:
#         """Determine the root message ID for a thread"""
#         # First check if this is already a known root
#         if email_data["message_id"] in email_map.get('_roots', {}):
#             return email_data["message_id"]
        
#         # Try to get from References header (look for the earliest reference)
#         if email_data["references"]:
#             refs = email_data["references"].split()
#             if refs:
#                 # The first reference is typically the root message
#                 return refs[0]
        
#         # Then try In-Reply-To
#         if email_data["in_reply_to"]:
#             # If we have the parent, get its thread ID
#             if email_data["in_reply_to"] in email_map:
#                 parent = email_map[email_data["in_reply_to"]]
#                 return self._get_thread_id(parent, email_map)
#             # Otherwise use in_reply_to as thread ID
#             return email_data["in_reply_to"]
        
#         # Default to using own message_id as thread_id
#         return email_data["message_id"]


#     # def _add_reply_to_thread(self, root_email: Dict, reply_email: Dict, email_map: Dict):
#     #     """Recursively add a reply to the correct position in the thread"""
#     #     # Don't add if it's already in the thread
#     #     if self._is_email_in_thread(root_email, reply_email["message_id"]):
#     #         return

#     #     # Base case: reply directly to the root
#     #     if reply_email["in_reply_to"] == root_email["message_id"]:
#     #         root_email["replies"].append(reply_email)
#     #         return

#     #     # Recursive case: find the parent in existing replies
#     #     for existing_reply in root_email["replies"]:
#     #         if reply_email["in_reply_to"] == existing_reply["message_id"]:
#     #             existing_reply["replies"].append(reply_email)
#     #             return

#     #     # If parent not found in direct replies, check if we have the parent
#     #     if reply_email["in_reply_to"] in email_map:
#     #         parent = email_map[reply_email["in_reply_to"]]
#     #         self._add_reply_to_thread(root_email, parent, email_map)
#     #         parent["replies"].append(reply_email)
#     #     else:
#     #         # If we can't find parent, add to root's replies
#     #         root_email["replies"].append(reply_email)

#     def _add_reply_to_thread(self, root_email: Dict, reply_email: Dict, email_map: Dict):
#         """Recursively add a reply to the correct position in the thread"""
#         # Don't add if it's already in the thread
#         if self._is_email_in_thread(root_email, reply_email["message_id"]):
#             return
        
#         # Base case: reply directly to the root
#         if reply_email["in_reply_to"] == root_email["message_id"]:
#             root_email["replies"].append(reply_email)
#             return
        
#         # Recursive case: find the parent in existing replies
#         for existing_reply in root_email["replies"]:
#             if reply_email["in_reply_to"] == existing_reply["message_id"]:
#                 existing_reply["replies"].append(reply_email)
#                 return
        
#         # If parent not found in direct replies, check if we have the parent
#         if reply_email["in_reply_to"] in email_map:
#             parent = email_map[reply_email["in_reply_to"]]
#             # First add the parent to the thread if it's not already there
#             if not self._is_email_in_thread(root_email, parent["message_id"]):
#                 self._add_reply_to_thread(root_email, parent, email_map)
#             # Then add the reply under its parent
#             parent["replies"].append(reply_email)
#         else:
#             # If we can't find parent, add to root's replies
#             root_email["replies"].append(reply_email)

#     def _is_email_in_thread(self, root_email: Dict, message_id: str) -> bool:
#         """Check if an email is already in the thread"""
#         if root_email["message_id"] == message_id:
#             return True
#         for reply in root_email.get("replies", []):
#             if self._is_email_in_thread(reply, message_id):
#                 return True
#         return False

#     def _parse_email(self, email_message) -> Dict[str, Any]:
#         email_data = {
#             "subject": self._decode_header(email_message.get("Subject", "No Subject")),
#             "from": self._parse_sender(email_message.get("From", "")),
#             "date": self._parse_date(email_message.get("Date", "")),
#             "body": "",
#             "attachment": [],
#             "message_id": email_message.get("Message-ID", "").strip(),
#             "in_reply_to": email_message.get("In-Reply-To", "").strip(),
#             "references": email_message.get("References", "").strip(),
#             "replies": []
#         }

#         email_data["body"], email_data["attachment"] = self._parse_email_content(email_message)
#         return email_data

#     def _parse_email_content(self, email_message) -> tuple:
#         body = ""
#         attachments = []

#         for part in email_message.walk():
#             content_type = part.get_content_type()
#             content_disposition = str(part.get("Content-Disposition"))

#             if part.is_multipart():
#                 continue

#             if "attachment" in content_disposition:
#                 attachment = {
#                     "name": self._decode_header(part.get_filename() or "unnamed"),
#                     "content": self._get_part_content(part)
#                 }
#                 attachments.append(attachment)
#                 continue

#             # Try to get the body from text/plain first
#             if content_type == "text/plain" and not body:
#                 body_charset = part.get_content_charset() or "utf-8"
#                 try:
#                     part_body = part.get_payload(decode=True).decode(body_charset, errors="replace")
#                     # Clean up the body text
#                     body = self._clean_email_body(part_body.strip())
#                 except Exception:
#                     pass

#             # Fall back to HTML if plain text not available
#             elif content_type == "text/html" and not body:
#                 body_charset = part.get_content_charset() or "utf-8"
#                 try:
#                     html_body = part.get_payload(decode=True).decode(body_charset, errors="replace")
#                     # Convert HTML to clean text
#                     clean_text = re.sub('<[^<]+?>', '', html_body)
#                     body = self._clean_email_body(clean_text.strip())
#                 except Exception:
#                     pass

#         # If we still have no body, try the simple payload approach
#         if not body and not email_message.is_multipart():
#             try:
#                 body_charset = email_message.get_content_charset() or "utf-8"
#                 body = self._clean_email_body(
#                     email_message.get_payload(decode=True).decode(body_charset, errors="replace"))
#             except Exception:
#                 pass

#         return body, attachments

#     def _clean_email_body(self, body: str) -> str:
#         """Clean up email body text"""
#         if not body:
#             return body

#         # Remove quoted text patterns
#         patterns = [
#             r'On\s+.+?\s+wrote:',  # "On Wed, Jun 4, 2025 at 3:51 PM X wrote:"
#             r'-----Original Message-----',
#             r'From:\s+.+?[\r\n]+Sent:\s+.+?[\r\n]+To:\s+.+?[\r\n]+Subject:',
#             r'_{10,}',  # Long underscores
#             r'-{10,}',  # Long dashes
#         ]

#         for pattern in patterns:
#             body = re.sub(pattern, '', body, flags=re.IGNORECASE)

#         # Remove excessive whitespace
#         body = re.sub(r'\n\s*\n', '\n\n', body)
#         body = body.strip()

#         return body

#     def _get_part_content(self, part) -> str:
#         """Get base64 encoded content of a part"""
#         try:
#             binary_content = part.get_payload(decode=True)
#             if binary_content is None:
#                 return ""
#             return base64.b64encode(binary_content).decode('utf-8')
#         except Exception as e:
#             print(f"Error encoding attachment: {str(e)}")
#             return ""

#     def _parse_sender(self, sender: str) -> str:
#         match = re.search(r'<(.+?)>', sender)
#         return match.group(1) if match else sender

#     def _parse_date(self, date_str: str) -> str:
#         try:
#             dt = parsedate_to_datetime(date_str)
#             return dt.strftime("%Y-%m-%d %H:%M:%S")
#         except (TypeError, ValueError):
#             return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

#     def _decode_header(self, header) -> str:
#         if not header:
#             return ""

#         try:
#             decoded = email.header.decode_header(header)
#             return " ".join(
#                 str(t[0], t[1] or "utf-8") if isinstance(t[0], bytes) else t[0]
#                 for t in decoded
#             )
#         except Exception:
#             return str(header)


# if __name__ == "__main__":
#     fetcher = GmailFetcher("<EMAIL>", "gsstqpimydlupqvd")
#     page = int(sys.argv[1]) if len(sys.argv) > 1 else 1
#     per_page = int(sys.argv[2]) if len(sys.argv) > 2 else 5
#     result = fetcher.fetch_emails(page=page, per_page=per_page)
#     print(json.dumps(result, ensure_ascii=True))