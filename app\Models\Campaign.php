<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Campaign extends Model
{
    use HasFactory;
    protected $fillable = ['name', 'start_time', 'end_time', 'status', 'attempts'];

    public function setStartTimeAttribute($value)
    {
        $this->attributes['start_time'] = Carbon::parse($value)->setTimezone('Asia/Karachi')->toDateTimeString();
    }

    public function setEndTimeAttribute($value)
    {
        $this->attributes['end_time'] = Carbon::parse($value)->setTimezone('Asia/Karachi')->toDateTimeString();
    }

    public function campaign_numbers(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(CampaignNumber::class);
    }

    public function custom_numbers(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(CustomNumber::class);
    }

    public function getCustomNumber(User $user)
    {
        return $this->custom_numbers()->select(['id', 'customer_phone', 'retailer_name', 'shop_name', 'inactive_days', 'target', 'gmv', 'number_of_unique_sku_sold', 'agent_id'])->where('status', 0)->where('attempts', '>', 0)->where('agent_id', $user->auth_username)->inRandomOrder()->first();
    }

    public function outboundlists(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Outbound::class);
    }

    public function users(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function getNumber()
    {
        return $this->campaign_numbers()->where('status', 0)->where('attempts', '>', 0)->inRandomOrder()->first();
    }
}
