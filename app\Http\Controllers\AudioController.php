<?php

namespace App\Http\Controllers;

use App\Models\Audio;
use App\Models\Permission;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class AudioController extends Controller
{

    public function __construct() {

        $this->middleware('permission:create_audio', ['only' => ['create']]);
        $this->middleware('permission:update_audio', ['only' => ['update']]);
        $this->middleware('permission:delete_audio', ['only' => ['destroy']]);
        $this->middleware('permission:read_audio', ['only' => ['index']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            return response()->json(Audio::query()->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    /**
     * Get an individual Audio
     * @param Request $request
     * @param Audio $audio
     * @return
     */
    public function show(Request $request, Audio $audio)
    {
        try {
            $file = Storage::disk('public')->get($audio->path);
        } catch (FileNotFoundException $e) {
            return response()->json($e->getMessage());
        }
        return response()->json($file);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     * @throws FileNotFoundException
     */
    public function store(Request $request)
    {
        if($request->hasFile('fileName')) {

            $request->validate([
                'fileName' => ['required', 'mimes:wav,gsm'],
            ]);

            $allowedExtensions = ['wav', 'gsm'];
            $file = $request->file('fileName');
            $extension = $file->getClientOriginalExtension();

            if(Storage::disk('sftp')->exists($file->getClientOriginalName())) {
                throw \Illuminate\Validation\ValidationException::withMessages([
                    'fileName' => 'File already exists'
                ]);
            }

            if(in_array($extension, $allowedExtensions)) {
                $path = $file->storeAs('audio', $file->getClientOriginalName());

                // Save to server
                $serverPath = Storage::disk('sftp')->put($file->getClientOriginalName(), fopen($file, 'r+'));

                if($serverPath) {
                    $name = $file->getClientOriginalName();
                    $save = new Audio;
                    $save->name = $name;
                    $save->path = $path;
                    $save->save();

                } else {
                    return response()->json(['file_could_not_be_uploaded'], 422);
                }
                return response()->json(['path' => $path], 200);
            } else {
                return response()->json(['invalid_file_format'], 422);
            }
        } else {
            return response()->json(['upload_file_not_found'], 400);
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Audio  $audio
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Audio $audio)
    {
        try {
            $status = $audio->delete();
            Storage::disk('sftp')->delete($audio->name);
            return response()->json($status);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }
}
