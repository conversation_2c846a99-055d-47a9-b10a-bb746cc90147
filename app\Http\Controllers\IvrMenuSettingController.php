<?php

namespace App\Http\Controllers;

use App\Models\IvrMenuSetting;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class IvrMenuSettingController extends Controller
{
    public function getMenuSettings()
    {
        return IvrMenuSetting::all();
    }
    public function updateSettings(Request $request): \Illuminate\Http\JsonResponse
    {
        $validatedData = $request->validate([
            'start' => 'required',
            'end' => 'required',
            'weeks' => 'required',
            'queue' => 'nullable|string',
        ]);
        try {
            $settings = IvrMenuSetting::first();
            $data = [
                'start' => $validatedData['start'],
                'end' => $validatedData['end'],
                'weeks' => $validatedData['weeks'],
                'queue' => $validatedData['queue'],
            ];
            if ($settings) {
                $settings->update($data);
                return response()->json(
                    [
                        "status" => 200,
                        "message" => "IVR menu settings updated successfully."
                    ]
                );
            } else {
                $created = IvrMenuSetting::create($data);
                if ($created) {
                    return response()->json(
                        [
                            "status" => 200,
                            "message" => "IVR menu settings created successfully."
                        ]
                    );
                } else {
                    return response()->json(
                        [
                            "status" => 400,
                            "message" => "Something went wrong."
                        ]
                    );
                }
            }
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
