<?php

namespace App\Http\Controllers;

use App\Models\SMSCategory;
use App\Models\SMSTemplate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class SMSCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response()->json(SMSCategory::query()->get());
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
//        $request->validate([
//            'name', ['required']
//        ]);
        try {
            $smsCateggory = new SMSCategory();
            $smsCateggory->fill($request->all());
            $smsCateggory->save();
            return response()->json("Category has been Inserted");
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\SMSCategory  $sMSCategory
     * @return \Illuminate\Http\Response
     */
    public function show($category)
    {
        // try {
        //     // return response()->json($sMSCategory->load('sMSTemplate'));

        //     $templates = SMSTemplate::where('sms_category_id', $sMSCategory->id)->get();

        //     return response()->json([
        //         'category' => $sMSCategory,
        //         's_m_s_template' => $templates,
        //     ]);
        // } catch (\Exception $exception){
        //     return response()->json($exception->getMessage(), 500);
        // }

        try {
            
            if ($category == 'custom_sms') {
                $response = [
                    'id' => null,
                    'name' => 'custom_sms',
                    'sMSTemplate' => [] 
                ];
                return response()->json($response);
            }

            $sMSCategory = SMSCategory::find($category);

            if (!$sMSCategory) {
                return response()->json(['message' => 'Category not found'], 404);
            }

            $templates = SMSTemplate::where('sms_category_id', $sMSCategory->id)->get();

            return response()->json([
                'category' => $sMSCategory,
                's_m_s_template' => $templates,
            ]);

            // return response()->json($sMSCategory->load('sMSTemplate'));

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\SMSCategory  $sMSCategory
     * @return \Illuminate\Http\Response
     */
    public function edit(SMSCategory $sMSCategory)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\SMSCategory  $sMSCategory
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, SMSCategory $sMSCategory)
    {
//        $request->validate([
//            'name', ['required']
//        ]);
        try {
            $sMSCategory->fill($request->all());
            $sMSCategory->save();
            return response()->json("Category has been updated");
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\SMSCategory  $sMSCategory
     * @return \Illuminate\Http\Response
     */
    public function destroy(SMSCategory $sMSCategory)
    {
        try {
            $sMSCategory->sMSTemplate()->delete();
            $sMSCategory->delete();
            return response()->json("SMS category {$sMSCategory->name} has been deleted");
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }
}
