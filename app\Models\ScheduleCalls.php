<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ScheduleCalls extends Model
{
    use HasFactory;

    protected $fillable = [
        'number',
        'datetime',
        'user_id',
        'status'
    ];

    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getCreatedAtAttribute($value) 
    {
        return Carbon::parse($value)->setTimezone('Asia/Karachi')->toDateTimeString();    
    }
}
