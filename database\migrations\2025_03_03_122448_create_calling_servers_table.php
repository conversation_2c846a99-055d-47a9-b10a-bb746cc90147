<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('calling_servers', function (Blueprint $table) {
            $table->id();
            $table->string('host');
            $table->unsignedInteger('port');
            $table->string('scheme');
            $table->string('username');
            $table->string('secret');
            $table->bigInteger('connect_timeout');
            $table->bigInteger('read_timeout');
            $table->bigInteger('allowed_limit');
            $table->string('trunk');
            $table->string('caller_id');
            $table->string('context')->nullable();
            $table->bigInteger('timeout');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('calling_servers');
    }
};
