<?php

namespace Database\Seeders;

use App\Models\FormFieldType;
use Illuminate\Database\Seeder;

class FormFieldTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $values = [
            ['name' => 'TextBox', 'html' => 'input'],
            ['name' => 'SelectBox', 'html' => 'select'],
            ['name' => 'Checkbox', 'html' => 'checkbox'],
            ['name' => 'Radio', 'html' => 'radio'],
            ['name' => 'TextArea', 'html' => 'textarea'],
            ['name' => 'DatePicker' , 'html' => 'date']
        ];

        foreach ($values as $value) {
            if(!FormFieldType::query()->where('name', $value['name'])->exists()) {
                FormFieldType::query()->insert([
                    'name' => $value['name'],
                    'html' => $value['html']
                ]);
            }
        }
    }
}
