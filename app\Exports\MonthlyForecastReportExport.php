<?php

namespace App\Exports;

use DateInterval;
use DatePeriod;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Events\BeforeExport;

class MonthlyForecastReportExport implements  WithMapping, WithHeadings, FromArray, WithEvents
{
    /**
     * @return \Illuminate\Support\Collection
     */
   
    function __construct(array $data)
    {
        $this->data= $data;
    }
    public function array(): array
    {
        return $this->data;
    }
   
    public function map($data): array
    {   
            if($data['totalInbounCalls']==0)
            {
                $data['totalInbounCalls'] ='0';
            }
            return [
               $data['Date'],
               $data['Day'],
               $data['totalInbounCalls'],
               $data['hourly']['08:00:00'],
               $data['hourly']['08:30:00'],
               $data['hourly']['09:00:00'],
               $data['hourly']['09:30:00'],
               $data['hourly']['10:00:00'],
               $data['hourly']['10:30:00'],
               $data['hourly']['11:00:00'],
               $data['hourly']['11:30:00'],
               $data['hourly']['12:00:00'],
               $data['hourly']['12:30:00'],
               $data['hourly']['13:00:00'],
               $data['hourly']['13:30:00'],
               $data['hourly']['14:00:00'],
               $data['hourly']['14:30:00'],
               $data['hourly']['15:00:00'],
               $data['hourly']['15:30:00'],
               $data['hourly']['16:00:00'],
               $data['hourly']['16:30:00'],
               $data['hourly']['17:00:00'],
               $data['hourly']['17:30:00'],
               $data['hourly']['18:00:00'],
               $data['hourly']['18:30:00'],
               $data['hourly']['19:00:00'],
               $data['hourly']['19:30:00'],
               $data['hourly']['20:00:00'],
               $data['hourly']['20:30:00'],
               $data['hourly']['21:00:00'],
               $data['hourly']['21:30:00'],
               $data['hourly']['22:00:00'],
               $data['hourly']['22:30:00'],
               $data['hourly']['23:00:00'],
               $data['hourly']['23:30:00'],
            
               

    
            ];
      

    }
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->setCellValue('C'. ($event->sheet->getHighestRow()+1), '=ROUND(AVERAGE(C2:C'.$event->sheet->getHighestRow().'),0)');
                $event->sheet->setCellValue('D'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(D2:D'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('E'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(E2:E'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('F'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(F2:F'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('G'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(G2:G'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('H'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(H2:H'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('I'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(I2:I'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('J'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(J2:J'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('K'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(K2:K'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('L'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(L2:L'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('M'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(M2:M'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('N'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(N2:N'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('O'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(O2:O'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('P'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(P2:P'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('Q'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(Q2:Q'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('R'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(R2:R'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('S'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(S2:S'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('T'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(T2:T'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('U'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(U2:U'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('V'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(V2:V'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('W'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(W2:W'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('X'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(X2:X'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('Y'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(Y2:Y'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('Z'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(Z2:Z'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('AA'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(AA2:AA'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('AB'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(AB2:AB'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('AC'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(AC2:AC'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('AD'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(AD2:AD'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('AE'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(AE2:AE'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('AF'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(AF2:AF'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('AG'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(AG2:AG'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('AH'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(AH2:AH'.$event->sheet->getHighestRow()-1 .'),0)');
                $event->sheet->setCellValue('AI'. ($event->sheet->getHighestRow()), '=ROUND(AVERAGE(AI2:AI'.$event->sheet->getHighestRow()-1 .'),0)');
            
            }
        ];
    }
    public function headings(): array
    { 
        return [
            'Date',
            'Day',
            'Total Inbound Calls',
            '08:00:00',
            '08:30:00',
            '09:00:00',
            '09:30:00',
            '10:00:00',
            '10:30:00',
            '11:00:00',
            '11:30:00',
            '12:00:00',
            '12:30:00',
            '13:00:00',
            '13:30:00',
            '14:00:00',
            '14:30:00',
            '15:00:00',
            '15:30:00',
            '16:00:00',
            '16:30:00',
            '17:00:00',
            '17:30:00',
            '18:00:00',
            '18:30:00',
            '19:00:00',
            '19:30:00',
            '20:00:00',
            '20:30:00',
            '21:00:00',
            '21:30:00',
            '22:00:00',
            '22:30:00',
            '23:00:00',
            '23:30:00'

         
        ];
    }
}
