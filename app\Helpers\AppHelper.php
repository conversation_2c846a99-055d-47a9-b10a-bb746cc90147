<?php
namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Schema;
use App\Models\EmailSetting;

class AppHelper
{
    // public static function get($key, $default = null)
    // {
    //     return EmailSetting::where('key', $key)->value('value') ?? $default;
    // }

    public static function get($key, $default = null)
    {
        if (!Schema::hasTable('email_settings')) {
            return $default;
        }

        return EmailSetting::where('key', $key)->value('value') ?? $default;
    }
}
