<?php

namespace App\Console\Commands;

use App\Jobs\AgentlessCampaignHandlerJob;
use App\Models\AgentlessCampaign;
use App\Models\AgentlessCampaignLog;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class AgentlessCampaignCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:agentless-campaign-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try{
            $campaign = AgentlessCampaign::where('status', true)->orderBy('created_at', 'desc')->first();
            
            if($campaign) {
                $campaignLogs = AgentlessCampaignLog::query()->where('campaign_id', $campaign->id)->where('log', 'started')->orderBy('created_at', 'desc')->first();
                $now = Carbon::now();
                if($now->toDateString() >= $campaign->date_from && $now->toDateString() <= $campaign->date_to && $now->between(Carbon::createFromTimeString($campaign->time_from), Carbon::createFromTimeString($campaign->time_to)) && in_array(strtoupper($now->dayName), explode(',', strtoupper($campaign->days)))) {
                    Artisan::call('horizon:status');
                    $output = Artisan::output();
                    if ($output === 'Horizon is paused.') {
                        Artisan::call('horizon:continue');
                    }
                    if(!$campaignLogs) {
                        AgentlessCampaignHandlerJob::dispatch($campaign->id)->onQueue('default');
                    }

                } else {
                    if($campaignLogs && $campaignLogs->log === 'started') {
                        // Then stop it
                        // Check horizon status
                        Artisan::call('horizon:status');
                        $output = Artisan::output();
                        if($output === 'Horizon is running.') {
                            Artisan::call('horizon:pause');
                        }
                    }
                }
            }
        }catch(Exception $e){
            Log::info("AgentlessCampaignCommand:error {$e->getMessage()}");
        }
       
    }


    }

