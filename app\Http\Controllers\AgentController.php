<?php

namespace App\Http\Controllers;

use App\Models\FormData;
use App\Models\Queue;
use App\Models\QueueLog;
use App\Models\SystemSetting;
use App\Models\Cdr;
use App\Models\DTMFSetting;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PAGI\CDR\Impl\CDRFacade;
use PAMI\Client\Exception\ClientException;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\CommandAction;
use PAMI\Message\Action\CoreShowChannelsAction;
use PAMI\Message\Action\GetVarAction;
use PAMI\Message\Action\QueueAddAction;
use PAMI\Message\Action\QueueLogAction;
use PAMI\Message\Action\QueuePauseAction;
use PAMI\Message\Action\QueueRemoveAction;
use PAMI\Message\Action\QueueStatusAction;
use PAMI\Message\Action\QueueUnpauseAction;
use PAMI\Message\Event\CoreShowChannelEvent;
use PAMI\Message\Event\QueueMemberEvent;
use Illuminate\Pagination\Paginator;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Events\{ IsReady, AgentLogin, AgentStatus };

class AgentController extends Controller
{
    protected function getOptions()
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    public function getOutgoingChannelId(Request $request)
    {
        try {
            $user = $request->user();
            $action = new CoreShowChannelsAction();
            $client = new ClientImpl($this->getOptions());
            $client->open();
            $response = $client->send($action);
            $client->close();
            $events = $response->getEvents();
            foreach ($events as $key => $event) {
                if (str_contains($event->getKey('channel'), $user->auth_username)) {
                    return response()->json($event->getKey('uniqueid'));
                }
            }
            return response()->json("Unable to locate channel.", 500);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function saveData(Request $request)
    {
        $request->validate([
            'call_id' => ['required', 'unique:form_data,call_id']
        ]);
        try {
            $data = new FormData;
            $data->call_id = $request->call_id;
            $data->data = json_encode($request->except('call_id'));
            $data->save();
            return response()->json("Data has been saved.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getForms(Queue $queue, Request $request)
    {
        try {
            return response()->json($queue->forms()->with(['form_fields', 'form_fields.form_field_type', 'form_fields.form_field_options'])->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function get_queue(Request $request)
    {
        $user = $request->user();
        $queue = $user->queues->pluck('name');
        return response()->json($queue);
    }

    public function login_queue(Request $request)
    {
        $user = $request->user();
        $CURRENTDATE = Carbon::now()->format('Y-m-d H:i:s');
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());

        try {
            if ($user->type === 'Outbound') {
                return response()->json("Outbound agents cannot perform this queue operation", 403);
            }
            $client->open();
            $response = "";
            foreach ($queues as $queue) {
                $action = new QueueAddAction($queue->name, "PJSIP/{$user->auth_username}");
                $action->setPaused(false);
                $action->setMemberName($user->name);
                $response = $client->send($action);
            }
            $client->close();

            if ($response) {
                broadcast(new IsReady(['user_id' => $user->id, 'status' => true]))->toOthers();
                broadcast(new AgentLogin(['user_id' => $user->id, 'status' => true]))->toOthers();
                broadcast(new AgentStatus(['user_id' => $user->id, 'status' => true]))->toOthers();

                return response()->json($response->getKeys());
            } else {
                return response()->json("Failed to add user in queue", 500);
            }
        } catch (ClientException $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    public function logout_queue(Request $request)
    {
        $user = $request->user();
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = "Failed to add user in queue";
            foreach ($queues as $queue) {
                $this->check_agent_pause($client, $queue->name, "PJSIP/{$user->auth_username}");
                $action = new QueueRemoveAction($queue->name, "PJSIP/{$user->auth_username}");
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {

                broadcast(new IsReady(['user_id' => $user->id, 'status' => false]))->toOthers();
                broadcast(new AgentLogin(['user_id' => $user->id, 'status' => false]))->toOthers();
                broadcast(new AgentStatus(['user_id' => $user->id, 'status' => false]))->toOthers();

                return response()->json($response->getKeys());
            } else {
                return response()->json("Failed to logout user in queue", 500);
            }
        } catch (ClientException $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    private function check_agent_pause($client, $queue, $interface)
    {
        $action = new QueueStatusAction($queue, $interface);
        $response = $client->send($action);
        $status = $response->getEvents()[1]->getKey('paused') ?? 0;
        if ($status == 1) {
            $action = new QueueUnpauseAction($interface, $queue, $response->getEvents()[1]->getKey('pausedreason'));
            $res = $client->send($action);
        }
    }

    public function pause_queue(Request $request)
    {
        $user = $request->user();
        $queues = $user->queues;
        $reason = $request->reason;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = "Failed to add user in queue";
            foreach ($queues as $queue) {
                $action = new QueuePauseAction("PJSIP/{$user->auth_username}", $queue->name, $reason);
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {

                broadcast(new IsReady(['user_id' => $user->id, 'status' => false]))->toOthers();

                return response()->json($response->getKeys());
            } else {
                return response()->json("Failed to pause user in queue", 500);
            }
        } catch (ClientException $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    public function unpause_queue(Request $request)
    {
        $user = $request->user();
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = "Failed to add user in queue";
            foreach ($queues as $queue) {
                $action = new QueueUnpauseAction("PJSIP/{$user->auth_username}", $queue->name, $request->reason ?? '');
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {

                broadcast(new IsReady(['user_id' => $user->id, 'status' => true]))->toOthers();

                return response()->json($response->getKeys());
            } else {
                return response()->json("Failed to unpause user in queue", 500);
            }
        } catch (ClientException $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    public function agent_status(Request $request)
    {
        $user = $request->user();
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = "Failed to fetch data.";
            foreach ($queues as $queue) {
                $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {
                $data = [];
                foreach ($response->getEvents() as $event) {
                    $data[] = $event->getKeys();
                }

                $parsedData = $this->queueParser($data[0]);
                $parsedData2 = $this->agentParser($data[1]);

                return response()->json(['data' => [$parsedData, $parsedData2]]);
            } else {
                return response()->json(['error' => "Failed to unpause user in queue"], 500);
            }
        } catch (\Exception $e) {
            Log::error("{$e->getMessage()} {$e->getLine()} {$e->getFile()}");
            return response()->json(['error' => $e->getMessage()]);
        }
    }

    public function get_channel(Request $request)
    {
        $user = $request->user();
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $action = new CoreShowChannelsAction();
            $response = $client->send($action);
            $events = $response->getEvents();
            $data = "";
            foreach ($events as $event) {
                if ($event instanceof CoreShowChannelEvent && str_contains($event->getKey('channel'), "PJSIP/{$user->auth_username}"))
                    /* $data = $event->getKey('uniqueid');*/
                    $data = $event->getKey('linkedid'); // Changed due to variable not persisting in CDRs
            }
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function get_channel_id(Request $request)
    {
        $user = $request->user();
        $query = DB::table('queue_log')->where('EVENT', 'CONNECT')->where('AGENT', $user->name)->latest('time')->first();
        return response()->json($query->callid);
    }

    public function get_dtmf_input(Request $request)
    {
        $request->validate([
            'phone_number' => 'required',
        ]);

        $dtmfRecord = DB::table('dtmf_input')
            ->where('phone_number', 'like', "%{$request->phone_number}")
            ->orderByDesc('created_at')
            ->first();

        if (!$dtmfRecord || empty($dtmfRecord->dtmf)) {
            return response()->json('', 200);
        }

        if (!DTMFSetting::exists()) {
            return response()->json('', 200);
        }

        $dtmfSetting = DTMFSetting::where('dtmf', $dtmfRecord->dtmf)->first();

        if (!$dtmfSetting) {
            return response()->json('Unknown DTMF Input', 200);
        }

        return response()->json($dtmfSetting->value);
    }


    public function is_ready(Request $request)
    {
        $user = $request->user();
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = null;
            foreach ($queues as $queue) {
                $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {
                $status = $response->getEvents()[1]->getKey('paused');
                return response()->json($status == 1 ? false : true);
            } else {
                return response()->json("Failed to fetch agent status", 500);
            }
        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }
    public function is_login(Request $request)
    {
        $user = $request->user();
        if ($request->user()) {
            cache(['user' => $user]); // Store the queue without expiration
        }
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = null;
            foreach ($queues as $queue) {
                $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {
                $value = $response->getEvents()[1]->getKey('event');
                return response()->json($value === 'QueueMember' ? true : false);
            } else {
                return response()->json("Failed to fetch agent status", 500);
            }
        } catch (\Exception $e) {
            //Log::info($e->getFile(), $e->getLine());
            return response()->json($e->getMessage(), 500);
        }
    }

    public function submit_workcode(Request $request)
    {
        try {
            $request->validate([
                'channel' => ['required', 'unique:queue_log,callId,NULL,time,Event,WORKCODE'],
                'queue' => ['required', 'exists:queues,name']
            ]);
            $user = $request->user();
            $queue = $request->queue;
            $channel = $request->channel;
            $code = $request->code;
            $client = new ClientImpl($this->getOptions());
            $client->open();
            $action = new QueueLogAction($queue, "WORKCODE");
            $action->setMemberName($user->name);
            $action->setUniqueId($channel);
            $action->setMessage($code);
            $response = $client->send($action);
            $data = $response->getKey('message');
            $client->close();
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function hold(Request $request)
    {
        // dd('test');
        try {
            $request->validate([
                'callId' => ['required'],
                'queue' => ['required', 'exists:queues,name']
            ]);

            $user = $request->user();
            $queue = $request->queue;
            $callId = $request->callId;

            $client = new ClientImpl($this->getOptions());
            //dd($client);
            $client->open();
            $action = new QueueLogAction($queue, "HOLD");
            //dd($action);
            $action->setMemberName($user->name);
            $action->setUniqueId($callId);
            $response = $client->send($action);
            $data = $response->getKey('message');
            $client->close();
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function un_hold(Request $request)
    {

        try {

            $request->validate([
                'callId' => ['required'],
                'queue' => ['required', 'exists:queues,name']
            ]);


            $user = $request->user();
            $queue = $request->queue;
            $callId = $request->callId;

            $client = new ClientImpl($this->getOptions());
            $client->open();
            $action = new QueueLogAction($queue, "UNHOLD");
            $action->setMemberName($user->name);
            $action->setUniqueId($callId);
            $response = $client->send($action);
            $data = $response->getKey('message');
            $client->close();
            return response()->json($data);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    private function queueParser(array $data)
    {
        // Only allowed keys should be returned

        $allowed = ['queue', 'max', 'strategy', 'calls', 'holdtime', 'talktime', 'completed', 'abandoned', 'servicelevelperf2', 'weight'];
        $nData = [];

        foreach ($allowed as $item) {
            $nData[$item === 'servicelevelperf2' ? 'Service Level' : ucfirst($item)] = $data[$item];
        }

        return $nData;
    }

    private function agentParser(array $data)
    {
        $allowed = ['name', 'stateinterface', 'callstaken', 'lastcall', 'lastpause', 'incall', 'status', 'paused', 'pausedreason', 'wrapuptime'];
        $nData = [];

        foreach ($allowed as $item) {
            if ($item === 'stateinterface')
                $nData['Interface'] = explode("/", $data[$item])[1];
            elseif ($item === 'lastcall' || $item === 'lastpause')

                $nData[ucfirst($item)] = $data[$item] !== '0' ? Carbon::createFromTimestamp($data[$item])->diffForHumans() : $data[$item];

            elseif ($item === 'paused' || $item === 'incall')
                $nData[ucfirst($item)] = $data[$item] === '1' ? 'Yes' : 'No';
            elseif ($item === 'status')
                $nData[ucfirst($item)] = $this->mapState($data[$item]);
            else
                $nData[ucfirst($item)] = $data[$item];
        }

        return $nData;
    }

    /**
        Status - The numeric device state status of the queue member.
        0 - AST_DEVICE_UNKNOWN
        1 - AST_DEVICE_NOT_INUSE
        2 - AST_DEVICE_INUSE
        3 - AST_DEVICE_BUSY
        4 - AST_DEVICE_INVALID
        5 - AST_DEVICE_UNAVAILABLE
        6 - AST_DEVICE_RINGING
        7 - AST_DEVICE_RINGINUSE
        8 - AST_DEVICE_ONHOLD
     */
    private function mapState(int $state)
    {
        $state = (int)$state;
        switch ($state) {
            default:
                return $state;
            case 0:
                return 'DEVICE_UNKNOWN';
            case 1:
                return 'DEVICE_NOT_INUSE';
            case 2:
                return 'DEVICE_INUSE';
            case 3:
                return 'DEVICE_BUSY';
            case 4:
                return 'DEVICE_INVALID';
            case 5:
                return 'DEVICE_UNAVAILABLE';
            case 6:
                return 'DEVICE_RINGING';
            case 7:
                return 'DEVICE_RINGINUSE';
            case 8:
                return 'DEVICE_ONHOLD';
        }
    }

    /**
     * Get Queue stats
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function get_queue_stats(Request $request)
    {
        $user = $request->user();
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = "Failed to fetch data.";
            foreach ($queues as $queue) {
                $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {
                $data = [];
                foreach ($response->getEvents() as $event) {
                    $data[] = $event->getKeys();
                }

                $parsedData = $this->queueParser($data[0]);
                //$parsedData2 = $this->agentParser($data[1]);

                return response()->json($parsedData);
            } else {
                return response()->json("Failed to fetch queue stats.", 500);
            }
        } catch (\Exception $e) {
            Log::error("{$e->getMessage()} {$e->getLine()} {$e->getFile()}");
            return response()->json($e->getMessage(), 500);
        }
    }

    /**
     * Get Agent stats
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function get_agent_stats(Request $request)
    {

        $user = $request->user();
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = "Failed to fetch data.";
            foreach ($queues as $queue) {
                $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {
                $data = [];
                foreach ($response->getEvents() as $event) {
                    $data[] = $event->getKeys();
                }
                //$parsedData = $this->queueParser($data[0]);
                $parsedData2 = $this->agentParser($data[1]);

                return response()->json($parsedData2);
            } else {
                return response()->json("Failed to fetch agent stats.", 500);
            }
        } catch (\Exception $e) {
            Log::error("{$e->getMessage()} {$e->getLine()} {$e->getFile()}");
            return response()->json($e->getMessage(), 500);
        }
    }

    /**
     * Get agent's call detail records
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function get_agent_cdr(Request $request)
    {
        $user = $request->user();

        // $cdr = Cdr::where('channel', 'like', "%PJSIP/{$user->auth_username}%")->orWhere('dstchannel', 'like', "%PJSIP/{$user->auth_username}%")
        $cdr = Cdr::where(function ($query) use ($user) {
            $query->where('channel', 'like', "%PJSIP/{$user->auth_username}%")
                ->orWhere('dstchannel', 'like', "%PJSIP/{$user->auth_username}%");
        });

        if ($request->number) {
            $cdr = $cdr->where(function ($query) use ($request) {
                $query->where('src', $request->number)
                    ->orWhere('dst', $request->number);
            });
        }
        $cdr = $cdr->orderBy('start', 'desc')->paginate(5)->through(function ($record) {
            if ($record->accountcode === 'Queue') {
                // It will be dstchannel
                $user = User::query()->where('auth_username', $record->dstchannel)->first();
                if ($record->dstchannel && $user)
                    $record->agent = $user->name;
                else
                    $record->agent = $record->dstchannel;
            } else if ($record->accountcode === 'Outbound') {
                // It will be channel
                $user = User::query()->where('auth_username', $record->channel)->first();
                if ($record->channel && $user)
                    $record->agent = $user->name;
                else
                    $record->agent = $record->channel;
            }
            return $record;
        });
        return response()->json($cdr);
    }

    public function get_agent_status(Request $request)
    {
        $user = $request->user();
        $queues = $user->queues;
        $client = new ClientImpl($this->getOptions());
        try {
            $client->open();
            $response = "Failed to fetch data.";
            foreach ($queues as $queue) {
                $action = new QueueStatusAction($queue->name, "PJSIP/{$user->auth_username}");
                $response = $client->send($action);
            }
            $client->close();
            if ($response) {
                return response()->json($response->getEvents()[1] instanceof QueueMemberEvent);
            } else {
                return response()->json("Failed to fetch agent status", 500);
            }
        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    public function getTime(Request $request)
    {

        $user = $request->user();
        $CURRENTDATE = Carbon::now()->format('Y-m-d');
        $breakTime = 0;
        $loginTime = 0;
        $breakData = DB::select("SELECT * FROM queue_log WHERE EVENT IN ('PAUSE', 'UNPAUSE') AND Agent = '$user->name' AND DATE(time) = '$CURRENTDATE' ORDER BY TIME ASC");

        for ($x = 0; $x < count($breakData); $x += 2) {
            if ($breakData[$x]->Event === 'PAUSE') {
                // Search for UNPAUSE

                if ($x !== count($breakData) - 1 && $breakData[$x + 1]->Event === 'UNPAUSE') {
                    // Take difference
                    $breakTime += Carbon::parse($breakData[$x + 1]->time)->diffInSeconds($breakData[$x]->time);
                } else if ($CURRENTDATE === Carbon::now()->format('Y-m-d')) {
                    $unpause = 0;
                }
            }
        } //end of break time
        $breakSeconds = round($breakTime);
        $breakOutput = sprintf('%02d:%02d:%02d', ($breakSeconds / 3600), ($breakSeconds / 60 % 60), $breakSeconds % 60);


        $data = DB::select("SELECT * FROM queue_log WHERE EVENT IN ('ADDMEMBER', 'REMOVEMEMBER') AND Agent = '$user->name' AND DATE(time) = '$CURRENTDATE' ORDER BY TIME ASC");
        for ($i = 0; $i < count($data); $i += 2) {
            if ($data[$i]->Event === 'ADDMEMBER') {
                // Search for remove member
                if ($i !== count($data) - 1 && $data[$i + 1]->Event === 'REMOVEMEMBER') {
                    // Take difference
                    $loginTime += Carbon::parse($data[$i + 1]->time)->diffInSeconds($data[$i]->time);
                } else if ($CURRENTDATE === Carbon::now()->format('Y-m-d')) {
                    $removeMember = 0;
                }
            }
        } //end of login time
        $loginSeconds = round($loginTime);
        $loginOutput = sprintf('%02d:%02d:%02d', ($loginSeconds / 3600), ($loginSeconds / 60 % 60), $loginSeconds % 60);

        return response()->json(['breakTime' => $breakSeconds, 'loginTime' => $loginSeconds]);
    }
}
