<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Menu;
use App\Models\MenuOption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
class MenuOptionController extends Controller
{
    public function index()
    {
        return MenuOption::with('menu')->get();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'menu_id' => 'required|exists:menus,id',
            'option_number' => 'nullable|integer',
            'option_text' => 'required|string',
            'target_menu_id' => 'nullable|exists:menus,id',
            'queue' => 'nullable|string',
        ]);


        // Custom validation rule: Either target_menu_id or queue must be provided, but not both.
        if (!$request->filled('target_menu_id') && !$request->filled('queue')) {
            return response()->json(['error' => 'Either target_menu_id or queue must be provided.'], 422);
        }

        if ($request->filled('target_menu_id') && $request->filled('queue')) {
            return response()->json(['error' => 'You cannot provide both target_menu_id and queue.'], 422);
        }

        // Check for duplicates
        $duplicate = DB::table('menu_options')->where('menu_id', $request->input('menu_id'));

        if ($request->filled('option_number')) {
            $duplicate = $duplicate->where('option_number', $request->input('option_number'));
        }

        if ($request->filled('target_menu_id')) {
            $duplicate = $duplicate->where('target_menu_id', $request->input('target_menu_id'))->whereNull('queue');
        }
        elseif ($request->filled('queue')) {
            $duplicate = $duplicate->where('queue', $request->input('queue'))->whereNull('target_menu_id');
        }

        $duplicate = $duplicate->exists();

        if ($duplicate) {
            return response()->json(['error' => 'Duplicate menu option detected.'], 400);
        }

        return MenuOption::create($validated);
    }

    public function show(MenuOption $menuOption)
    {
        return $menuOption->load('menu');
    }

    public function updateMenuOption(Request $request, $id)
    {
        $menu = Menu::findOrFail($id);

        // Update menu fields
        if(!empty($request->input('name'))) {
            $menu->name = $request->input('name');
        }

        $menu->prompt_text = $request->input('prompt_text');

        if (!$menu->save()) {
            return response()->json(['error' => 'Failed to update menu.'], 500);
        }

        if($request->input('options')) {
            foreach ($request->input('options', []) as $optionId => $optionData) {

                // Check if both target_menu_id and queue are provided
                if (!empty($optionData['target_menu_id']) && !empty($optionData['queue'])) {
                    return response()->json(['error' => 'Either target_menu_id or queue must be provided.'], 422);
                }

                if ($optionId === 'new') {
                    foreach ($optionData as $newOption) {

                        // Check for duplicates
                        $duplicate = DB::table('menu_options')->where('menu_id', $newOption['menu_id']);

                        if (isset($newOption['option_number']) && !empty($newOption['option_number'])) {
                            $duplicate = $duplicate->where('option_number', $newOption['option_number']);
                        }

                        if (isset($newOption['target_menu_id']) && !empty($newOption['target_menu_id'])) {
                            $duplicate = $duplicate->where('target_menu_id', $newOption['target_menu_id'])->whereNull('queue');
                        } elseif (isset($newOption['queue']) && !empty($newOption['queue'])) {
                            $duplicate = $duplicate->where('queue', $newOption['queue'])->whereNull('target_menu_id');
                        }

                        $duplicate = $duplicate->where('id', '!=', $optionId)->exists();

                        if ($duplicate) {
                            return response()->json(['error' => 'Duplicate menu option detected.'], 422);
                        }

                        $newMenuOption = MenuOption::create([
                            'menu_id' => $newOption['menu_id'],
                            'option_number' => $newOption['option_number'] ?? null,
                            'option_text' => $newOption['option_text'] ?? null,
                            'target_menu_id' => $newOption['target_menu_id'] ?? null,
                            'queue' => $newOption['queue'] ?? null,
                        ]);

                        if (!$newMenuOption) {
                            return response()->json(['error' => 'Failed to create new menu option.'], 500);
                        }
                    }
                    continue; // Skip processing for "new"
                }

                // Update existing menu options
                $option = MenuOption::find($optionId);
                if ($option) {
                    // Check for duplicates
                    $duplicate = DB::table('menu_options')->where('menu_id', $optionData['menu_id']);

                    if (isset($optionData['option_number']) && !empty($optionData['option_number'])) {
                        $duplicate = $duplicate->where('option_number', $optionData['option_number']);
                    }

                    if (isset($optionData['target_menu_id']) && !empty($optionData['target_menu_id'])) {
                        $duplicate = $duplicate->where('target_menu_id', $optionData['target_menu_id'])->whereNull('queue');
                    } elseif (isset($optionData['queue']) && !empty($optionData['queue'])) {
                        $duplicate = $duplicate->where('queue', $optionData['queue'])->whereNull('target_menu_id');
                    }

                    $duplicate = $duplicate->where('id', '!=', $optionId)->exists();

                    if ($duplicate) {
                        return response()->json(['error' => 'Duplicate menu option detected.'], 422);
                    }

                    $updateMenuOption = $option->update([
                        'menu_id' => $optionData['menu_id'],
                        'option_number' => $optionData['option_number'] ?? null,
                        'option_text' => $optionData['option_text'] ?? null,
                        'target_menu_id' => $optionData['target_menu_id'] ?? null,
                        'queue' => $optionData['queue'] ?? null,
                    ]);

                    if(!$updateMenuOption) {
                        return response()->json(['error' => 'Failed to update menu option.'], 500);
                    }
                }
            }
        }

        return response()->json(['message' => 'Menu updated successfully!']);
    }

    public function updateMenuMediaFile(Request $request, $id)
    {
        $menu = Menu::findOrFail($id);

        // Validate the request
        $request->validate([
            'media_file' => ['nullable', 'file', 'mimes:wav']
        ]);

        // Handle media upload
        $asteriskSoundDir = '/var/lib/asterisk/sounds/en/';
        $asteriskSoundDir = '/var/lib/asterisk/sounds/en/';

        if ($request->hasFile('media_file')) {

            $storedFilePath = $request->file('media_file')->storeAs('ivr', $request->file('media_file')->getClientOriginalName(), 'sounds');

            if(!$storedFilePath) {
                return response()->json(['error' => 'Failed to save media file'], 500);
            }

            $menu->media = explode('.', $storedFilePath)[0];
            $menu->path  = $storedFilePath;

        } elseif ($request->input('remove_media')) {
            if ($menu->media && file_exists($asteriskSoundDir . $menu->media)) {
                unlink($asteriskSoundDir . $menu->media);
            }

            $menu->media = null;
            $menu->path = null;
        }

        if($menu->save()) {
            return response()->json(['message' => 'Menu updated successfully!']);
        }

        return response()->json(['message' => 'Failed to update media file']);
    }

    public function destroy(MenuOption $menuOption)
    {
        $menuOption->delete();
        return response()->json(['message' => 'Option deleted successfully!']);
    }
}
