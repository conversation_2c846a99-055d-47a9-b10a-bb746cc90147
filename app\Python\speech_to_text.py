# import os
# import sys
# import json
# import subprocess
# import wave
# from vosk import Model, KaldiRecognizer

# def convert_audio(input_path, output_path):
#     """
#     Converts an audio file to mono, 16-bit PCM, 16000Hz using FFmpeg.
#     """
#     try:
#         command = [
#             "ffmpeg", "-y", "-i", input_path,   # Input file
#             "-ac", "1", "-ar", "16000", "-sample_fmt", "s16",  # Convert to mono, 16-bit, 16000Hz
#             output_path
#         ]
#         subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
#         return output_path
#     except subprocess.CalledProcessError as e:
#         print("Error converting audio:", e)
#         sys.exit(1)

# def transcribe_audio(audio_path, model_path):
#     """
#     Transcribes speech from an audio file using Vosk.
#     """
#     if not os.path.exists(model_path):
#         print(f"Model path '{model_path}' does not exist.")
#         sys.exit(1)

#     # Load the Vosk model
#     model = Model(model_path)

#     # Open the converted audio file
#     wf = wave.open(audio_path, "rb")

#     # Initialize the recognizer
#     rec = KaldiRecognizer(model, wf.getframerate())

#     # Process the audio file
#     transcript = ""
#     while True:
#         data = wf.readframes(4000)
#         if len(data) == 0:
#             break
#         if rec.AcceptWaveform(data):
#             result = json.loads(rec.Result())
#             transcript += result.get("text", "") + " "

#     # Get the final result
#     result = json.loads(rec.FinalResult())
#     transcript += result.get("text", "")

#     return transcript.strip()

# if __name__ == "__main__":
#     if len(sys.argv) != 3:
#         print("Usage: python speech_to_text.py <audio_path> <model_path>")
#         sys.exit(1)

#     input_audio_path = sys.argv[1]
#     model_path = sys.argv[2]

#     # Define temp output path
#     converted_audio_path = "converted_audio.wav"

#     # Convert audio to required format
#     converted_audio_path = convert_audio(input_audio_path, converted_audio_path)

#     # Transcribe speech
#     transcript = transcribe_audio(converted_audio_path, model_path)

#     # Print transcription
#     print("The Final Result Of Wav File")
#     print(transcript)

#     # Remove temporary converted file
#     os.remove(converted_audio_path)


# import whisper

# # Load the Whisper model (choose a model size: tiny, base, small, medium, large)
# model = whisper.load_model("medium")

# # Transcribe the WAV file
# result = model.transcribe("C:/Users/<USER>/Downloads/queue-2138797850-03289302039-20250211-124053-1739259645.176663.wav", language="ur")  # Use "ur" for Urdu

# # Print the transcribed text
# print(result["text"])

# import whisper
# import warnings

# warnings.filterwarnings("ignore")
# model = whisper.load_model("large")  # Choose "base", "small", "medium", "large"

# # ffmpeg -i customer_2.wav -af "volume=2.0" customer_2_ffmpeg.wav
# # result = model.transcribe("C:/Users/<USER>/Downloads/wav-ffmpeg/customer_ffmpeg.wav", language="ur", task="translate")
# result = model.transcribe("C:/Users/<USER>/Downloads/wav-ffmpeg/customer_2_ffmpeg.wav", language="ur", task="translate", temperature=0.5)

# print("Translated English Text:", result["text"])

# result = model.transcribe(audio_path, language="ur", task="translate", temperature=0.1, beam_size=10, best_of=5, condition_on_previous_text=False)


# import whisper
# import sys
# import warnings
# from collections import Counter

# warnings.filterwarnings("ignore")

# def transcribe_with_whisper(audio_path, model, temperature=0.5, beam_size=1):
#     """Run a single transcription with given parameters."""
#     result = model.transcribe(
#         audio_path,
#         language="ur",
#         task="translate",
#         temperature=temperature,
#         beam_size=beam_size
#     )
#     return result["text"]

# def choose_best_transcription(transcriptions):
#     """Choose the best transcription from a list of candidates."""
#     # Use the most frequent transcription as the best candidate
#     transcription_counter = Counter(transcriptions)
#     best_transcription = transcription_counter.most_common(1)[0][0]
#     return best_transcription

# if len(sys.argv) < 2:
#     print("Error: No file path provided.")
#     sys.exit(1)

# audio_path = sys.argv[1]

# # Load the Whisper model
# model = whisper.load_model("medium")

# # Run multiple transcriptions with different temperatures
# num_transcriptions = 3  # Number of transcriptions to generate
# transcriptions = []

# for i in range(num_transcriptions):
#     temperature = 0.2 + (i * 0.1)  # Vary temperature between 0.2 and 0.6
#     transcription = transcribe_with_whisper(audio_path, model, temperature=temperature, beam_size=1)
#     transcriptions.append(transcription)

# # Choose the best transcription
# best_transcription = choose_best_transcription(transcriptions)
# print(best_transcription)


import whisper
import sys
import warnings
from pydub import AudioSegment
from multiprocessing import Pool

warnings.filterwarnings("ignore")

def chunk_audio(audio_path, chunk_duration=30):
    """Split the audio file into chunks of specified duration (in seconds)."""
    audio = AudioSegment.from_wav(audio_path)
    chunks = []
    for i in range(0, len(audio), chunk_duration * 1000):
        chunk = audio[i:i + chunk_duration * 1000]
        chunk_path = f"chunk_{i}.wav"
        chunk.export(chunk_path, format="wav")
        chunks.append(chunk_path)
    return chunks

def transcribe_with_whisper(audio_path, model, temperature=0.0, beam_size=10):
    """Run a single transcription with given parameters."""
    result = model.transcribe(
        audio_path,
        language="ur",
        task="translate",
        temperature=temperature,
        beam_size=beam_size
    )
    return result["text"]

def transcribe_chunk(chunk):
    """Transcribe a single chunk using Whisper."""
    model = whisper.load_model("medium")
    return transcribe_with_whisper(chunk, model)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Error: No file path provided.")
        sys.exit(1)

    audio_path = sys.argv[1]

    # Chunk the audio file into smaller segments
    chunk_duration = 30  # Duration of each chunk in seconds
    chunks = chunk_audio(audio_path, chunk_duration)

    # Transcribe chunks in parallel
    with Pool(processes=4) as pool:  # Use 4 processes
        all_transcriptions = pool.map(transcribe_chunk, chunks)

    # Combine all chunk transcriptions into a single final transcription
    final_transcription = " ".join(all_transcriptions)
    print(final_transcription)