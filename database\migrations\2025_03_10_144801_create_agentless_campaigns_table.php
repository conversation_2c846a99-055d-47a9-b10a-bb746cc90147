<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agentless_campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->boolean('status')->default(false);
            $table->string('file_path');
            $table->date('date_from')->nullable()->default(null);
            $table->date('date_to')->nullable()->default(null);
            $table->time('time_from')->nullable()->default(null);
            $table->time('time_to')->nullable()->default(null);
            $table->string('days')->nullable()->default(null);
            $table->string('unique_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->string('recording_name')->nullable();
            $table->string('stage')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agentless_campaigns');
    }
};
