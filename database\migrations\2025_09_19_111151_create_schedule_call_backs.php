<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schedule_call_backs', function (Blueprint $table) {
            $table->id();
            $table->string('number');
            $table->dateTime('date');
            $table->string('created_by')->nullable();
            $table->string('called_by')->nullable();
            $table->string('uniqueid')->nullable();
            $table->enum('status', ['pending', 'completed', 'cancelled', 'answer', 'noanswer', 'redial']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schedule_call_backs');
    }
};
