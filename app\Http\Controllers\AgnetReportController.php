<?php

namespace App\Http\Controllers;

use App\Models\AgnetReport;
use Carbon\Carbon;
use Couchbase\QueryStringSearchQuery;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;

class AgnetReportController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $size = $request->pageSize ?? 10;
//        $size = 21;
//        $data = DB::table('queue_log as q1')->select(['q1.time as intime', 'q1.Agent as agent', 'q1.Event', 'q2.time as outtime','q1.queuename as queuename'])->selectRaw('TIMEDIFF(q2.time, q1.time) as duration')->leftJoin('queue_log as q2', 'q1.Agent', '=', 'q2.Agent')->where('q1.Event', '=', 'ADDMEMBER')->where('q2.Event', '=', 'REMOVEMEMBER')->whereRaw('q1.time < q2.time')->whereRaw('DAY(q2.time) = DAY(q1.time)')->groupBy('q1.time')->orderBy('q1.time')->paginate($size);
//        return response()->json($data);

//        $data= AgnetReport::query()->orWhere('event','=','ADDMEMBER')->orWhere('event','=','REMOVEMEMBER')->whereRaw('Month(time) = MONTH(CURRENT_DATE())')->whereRaw('YEAR(time) = YEAR(CURRENT_DATE())')->get();
        $data= AgnetReport::query()->whereRaw('Month(time) = MONTH(CURRENT_DATE())')->whereRaw('YEAR(time) = YEAR(CURRENT_DATE())')->whereIn('event',['ADDMEMBER','REMOVEMEMBER'])->get();

        $getagaentinfor = array ();
        $GetInfor = array ();
        $Queue = array ();
        foreach ($data as $value) {
            $agentdatabody = str_replace("/", "", $value->Agent);
            // echo $agentdata;
            if ($value->Event == "ADDMEMBER") {
                $getagaentinfor["agent"][$agentdatabody] = $value->Agent;
                $getagaentinfor["agentname"][] = $agentdatabody;
                $GetInfor["timeIn"][$agentdatabody][] = $value->time;
                $Queue["queuename"][$agentdatabody][] = $value->queuename;
                $Queue["callid"][$agentdatabody][] = $value->callid;
            }
            if ($value->Event == "REMOVEMEMBER") {
                if(array_key_exists("timeIn",$GetInfor) && array_key_exists(" Test Agent",$GetInfor))
                    $GetInfor["timeOut"][$agentdatabody][count($GetInfor["timeIn"][$agentdatabody]) - 1] = $value->time;
            }
        }
        $portagentinfo = array();
        $counter = 0;
        if(isset($getagaentinfor['agent'])) {
            foreach ($getagaentinfor['agent'] as $key => $agentname) {
                foreach ($GetInfor["timeIn"][$key] as $timekey => $Intime) {
                    $start = new Carbon($Intime);
                    $end = '';
                    if (isset($GetInfor['timeOut'][$key][$timekey]))
                        $end = new Carbon($GetInfor['timeOut'][$key][$timekey]);
                    else
                        $end = null;

                    $portagentinfo[$counter]["agent"] = $agentname;
                    $portagentinfo[$counter]["intime"] = $Intime;
                    if($end!=null)
                        $portagentinfo[$counter]["duration"] = $start->diffInHours($end) . ':' . $start->diff($end)->format('%I');
                    else
                        $portagentinfo[$counter]["duration"] = 'not logged out';
                    if (isset($GetInfor['timeOut'][$key][$timekey]))
                        $portagentinfo[$counter]["outtime"] = $GetInfor['timeOut'][$key][$timekey];
                    $portagentinfo[$counter]["queuename"] = $Queue['queuename'][$key][$timekey];
                    $portagentinfo[$counter]["callid"] = $Queue['callid'][$key][$timekey];

                    $counter++;
                }
            }
        }
        return response()->json($portagentinfo);
    }



//    public function getCallPerAgent()
//    {
//        return response()->json();
//    }
//
//    public function getCallPerAgentFilter(Request $request)
//    {
//        return response()->json($request->all());
//    }

    public function ringNoAnswer()
    {
        return response()->json(AgnetReport::query()->where('event','=','RINGNOANSWER')->get());
    }

    public function ringNoAnswerFilter()
    {
        $where="1 ";
        $status=0;
        if(isset($request->time)){
            $where .=" and time between '".$request->time[0]."' and '".$request->time[1]."'";
            $status=1;
        }
        if(isset($request->callid)){
            $where .= " and callid= '".$request->callid."' ";
            $status=1;
        }
        if(isset($request->agent)){
            $where .= "and agent= '".$request->agent."'";
            $status=1;
        }
        if(isset($request->event)){
            $where .= " and event= '".$request->event."'";
            $status=1;
        }
        if(isset($request->queuename)){
            $where .= " and queuename= '".$request->queuename."'";
            $status=1;
        }
        $Query= "SELECT * FROM queue_log WHERE $where AND EVENT = 'RINGNOANSWER' ";
//        return $Query;
        $data= array();
        if($status==1)
            $data= DB::select($Query);
        return response()->json($data);
    }

    public function exitWithTimeout()
    {
        return response()->json(AgnetReport::query()->where('event','=','EXITWITHTIMEOUT')->get());
    }

    public function exitWithTimeoutFilter()
    {
        $where="1 ";
        $status=0;
        if(isset($request->time)){
            $where .=" and time between '".$request->time[0]."' and '".$request->time[1]."'";
            $status=1;
        }
        if(isset($request->callid)){
            $where .= " and callid= '".$request->callid."' ";
            $status=1;
        }
        if(isset($request->agent)){
            $where .= "and agent= '".$request->agent."'";
            $status=1;
        }
        if(isset($request->event)){
            $where .= " and event= '".$request->event."'";
            $status=1;
        }
        if(isset($request->queuename)){
            $where .= " and queuename= '".$request->queuename."'";
            $status=1;
        }
        $Query= "SELECT * FROM queue_log WHERE $where AND EVENT = 'EXITWITHTIMEOUT' ";
//        return $Query;
        $data= array();
        if($status==1)
            $data= DB::select($Query);
        return response()->json($data);
    }

    public function exitEmpty()
    {
        return response()->json(AgnetReport::query()->where('event','=','EXITEMPTY')->get());
    }

    public function exitEmptyFilter()
    {
        $where="1 ";
        $status=0;
        if(isset($request->time)){
            $where .=" and time between '".$request->time[0]."' and '".$request->time[1]."'";
            $status=1;
        }
        if(isset($request->callid)){
            $where .= " and callid= '".$request->callid."' ";
            $status=1;
        }
        if(isset($request->agent)){
            $where .= "and agent= '".$request->agent."'";
            $status=1;
        }
        if(isset($request->event)){
            $where .= " and event= '".$request->event."'";
            $status=1;
        }
        if(isset($request->queuename)){
            $where .= " and queuename= '".$request->queuename."'";
            $status=1;
        }
        $Query= "SELECT * FROM queue_log WHERE $where AND EVENT = 'EXITEMPTY' ";
//        return $Query;
        $data= array();
        if($status==1)
            $data= DB::select($Query);
        return response()->json($data);
    }



    public function completeCaller()
    {
        return response()->json(AgnetReport::query()->where('event', '=', 'COMPLETECALLER')->get());
    }

    public function completeCallerFilter()
    {
        $where="1 ";
        $status=0;
        if(isset($request->time)){
            $where .=" and time between '".$request->time[0]."' and '".$request->time[1]."'";
            $status=1;
        }
        if(isset($request->callid)){
            $where .= " and callid= '".$request->callid."' ";
            $status=1;
        }
        if(isset($request->agent)){
            $where .= "and agent= '".$request->agent."'";
            $status=1;
        }
        if(isset($request->event)){
            $where .= " and event= '".$request->event."'";
            $status=1;
        }
        if(isset($request->queuename)){
            $where .= " and queuename= '".$request->queuename."'";
            $status=1;
        }
        $Query= "SELECT * FROM queue_log WHERE $where AND EVENT = 'COMPLETECALLER' ";
//        return $Query;
        $data= array();
        if($status==1)
            $data= DB::select($Query);
        return response()->json($data);
    }

    public function completeAgent()
    {
        return response()->json(AgnetReport::query()->where('event', '=', 'COMPLETEAGENT')->get());
    }

    public function completeAgentFilter()
    {
        $where="1 ";
        $status=0;
        if(isset($request->time)){
            $where .=" and time between '".$request->time[0]."' and '".$request->time[1]."'";
            $status=1;
        }
        if(isset($request->callid)){
            $where .= " and callid= '".$request->callid."' ";
            $status=1;
        }
        if(isset($request->agent)){
            $where .= "and agent= '".$request->agent."'";
            $status=1;
        }
        if(isset($request->event)){
            $where .= " and event= '".$request->event."'";
            $status=1;
        }
        if(isset($request->queuename)){
            $where .= " and queuename= '".$request->queuename."'";
            $status=1;
        }
        $Query= "SELECT * FROM queue_log WHERE $where AND EVENT = 'COMPLETEAGENT' ";
//        return $Query;
        $data= array();
        if($status==1)
            $data= DB::select($Query);
        return response()->json($data);
    }

    public function configuredLoad()
    {
        return response()->json(AgnetReport::query()->where('event', '=', 'CONFIGRELOAD')->get());
    }

    public function configuredLoadFilter()
    {
        $where="1 ";
        $status=0;
        if(isset($request->time)){
            $where .=" and time between '".$request->time[0]."' and '".$request->time[1]."'";
            $status=1;
        }
        if(isset($request->callid)){
            $where .= " and callid= '".$request->callid."' ";
            $status=1;
        }
        if(isset($request->agent)){
            $where .= "and agent= '".$request->agent."'";
            $status=1;
        }
        if(isset($request->event)){
            $where .= " and event= '".$request->event."'";
            $status=1;
        }
        if(isset($request->queuename)){
            $where .= " and queuename= '".$request->queuename."'";
            $status=1;
        }
        $Query= "SELECT * FROM queue_log WHERE $where AND EVENT = 'CONFIGRELOAD' ";
//        return $Query;
        $data= array();
        if($status==1)
            $data= DB::select($Query);
        return response()->json($data);
    }

    public function callerHangup()
    {
        return response()->json(AgnetReport::query()->where('event', '=', 'COMPLETECALLER')->get());
    }

    public function callerHangupFilter(Request $request)
    {
        $where="1 ";
        $status=0;
        if(isset($request->time)){
            $where .=" and time between '".$request->time[0]."' and '".$request->time[1]."'";
            $status=1;
        }
        if(isset($request->callid)){
            $where .= " and callid= '".$request->callid."' ";
            $status=1;
        }
        if(isset($request->agent)){
            $where .= "and agent= '".$request->agent."'";
            $status=1;
        }
        if(isset($request->event)){
            $where .= " and event= '".$request->event."'";
            $status=1;
        }
        if(isset($request->queuename)){
            $where .= " and queuename= '".$request->queuename."'";
            $status=1;
        }
        $Query= "SELECT * FROM queue_log WHERE $where AND EVENT = 'COMPLETECALLER' ";
//        return $Query;
        $data= array();
        if($status==1)
            $data= DB::select($Query);
        return response()->json($data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Filter Report
//        $size = $request->pageSize ?? 10;
//        $data = DB::table('queue_log as q1')->select(['q1.time as intime', 'q1.Agent as agent', 'q1.Event', 'q2.time as outtime','q1.queuename as queuename'])->selectRaw('TIMEDIFF(q2.time, q1.time) as duration')->leftJoin('queue_log as q2', 'q1.Agent', '=', 'q2.Agent')->where('q1.Event', '=', 'ADDMEMBER')->where('q2.Event', '=', 'REMOVEMEMBER')->whereRaw('q1.time < q2.time')->whereRaw('DAY(q2.time) = DAY(q1.time)')->groupBy('q1.time')->orderBy('q1.time')->paginate($size);



        $where="1 ";
        $status=0;
        if(isset($request->time)){
            $where .=" and time between '".$request->time[0]."' and '".$request->time[1]."'";
            $status=1;
        }
        if(isset($request->callid)){
            $where .= " and callid= '".$request->callid."' ";
            $status=1;
        }
        if(isset($request->agent)){
            $where .= "and agent= '".$request->agent."'";
            $status=1;
        }
        if(isset($request->event)){
            $where .= " and event= '".$request->event."'";
            $status=1;
        }
        if(isset($request->queuename)){
            $where .= " and queuename= '".$request->queuename."'";
            $status=1;
        }
        $Query= "SELECT * FROM queue_log WHERE $where AND EVENT IN ('ADDMEMBER', 'REMOVEMEMBER') ";
//        return $Query;
        $data= array();
        if($status==1)
            $data= DB::select($Query);

        $getagaentinfor = array ();
        $GetInfor = array ();
        $Queue = array ();
        foreach ($data as $value) {
            $agentdatabody = str_replace("/", "", $value->Agent);
            // echo $agentdata;
            if ($value->Event == "ADDMEMBER") {
                $getagaentinfor["agent"][$agentdatabody] = $value->Agent;
                $getagaentinfor["agentname"][] = $agentdatabody;
                $GetInfor["timeIn"][$agentdatabody][] = $value->time;
                $Queue["queuename"][$agentdatabody][] = $value->queuename;
                $Queue["callid"][$agentdatabody][] = $value->callid;
            }
            if ($value->Event == "REMOVEMEMBER") {
//                if(array_key_exists("timeIn",$GetInfor) && array_key_exists(" Test Agent",$GetInfor))
                $GetInfor["timeOut"][$agentdatabody][count($GetInfor["timeIn"][$agentdatabody]) - 1] = $value->time;
            }
        }
        $portagentinfo = array();
        $counter = 0;
        if(isset($getagaentinfor['agent'])) {
            foreach ($getagaentinfor['agent'] as $key => $agentname) {
                foreach ($GetInfor["timeIn"][$key] as $timekey => $Intime) {
                    $start = new Carbon($Intime);
                    $end = '';
                    if (isset($GetInfor['timeOut'][$key][$timekey]))
                        $end = new Carbon($GetInfor['timeOut'][$key][$timekey]);
                    else
                        $end = null;

                    $portagentinfo[$counter]["agent"] = $agentname;
                    $portagentinfo[$counter]["intime"] = $Intime;
                    if($end!=null)
                        $portagentinfo[$counter]["duration"] = $start->diffInHours($end) . ':' . $start->diff($end)->format('%I:%S');
                    else
                        $portagentinfo[$counter]["duration"] = 'not logged out';
                    if (isset($GetInfor['timeOut'][$key][$timekey]))
                        $portagentinfo[$counter]["outtime"] = $GetInfor['timeOut'][$key][$timekey];
                    $portagentinfo[$counter]["queuename"] = $Queue['queuename'][$key][$timekey];
                    $portagentinfo[$counter]["callid"] = $Queue['callid'][$key][$timekey];

                    $counter++;
                }
            }
        }
        return response()->json($portagentinfo);
    }

    public function getPauseReasonReport()
    {
        $data= AgnetReport::query()->orWhere('event','=','PAUSE')->orWhere('event','=','UNPAUSE')->get();

        $getagaentinfor = array ();
        $GetInfor = array ();
        $Queue = array ();
        foreach ($data as $value) {
            $agentdatabody = str_replace("/", "", $value->Agent);
            // echo $agentdata;
            if ($value->Event == "PAUSE") {
                $getagaentinfor["agent"][$agentdatabody] = $value->Agent;
                $getagaentinfor["agentname"][] = $agentdatabody;
                $GetInfor["timeIn"][$agentdatabody][] = $value->time;
                $Queue["queuename"][$agentdatabody][] = $value->queuename;
                $Queue["callid"][$agentdatabody][] = $value->callid;
            }
            if ($value->Event == "UNPAUSE") {
                $GetInfor["timeOut"][$agentdatabody][count($GetInfor["timeIn"][$agentdatabody]) - 1] = $value->time;
            }
        }
        $portagentinfo = array();
        $counter = 0;
        if(isset($getagaentinfor['agent'])) {
            foreach ($getagaentinfor['agent'] as $key => $agentname) {
                foreach ($GetInfor["timeIn"][$key] as $timekey => $Intime) {
                    $start = new Carbon($Intime);
                    $end = '';
                    if (isset($GetInfor['timeOut'][$key][$timekey]))
                        $end = new Carbon($GetInfor['timeOut'][$key][$timekey]);

                    $portagentinfo[$counter]["agent"] = $agentname;
                    $portagentinfo[$counter]["intime"] = $Intime;
                    $portagentinfo[$counter]["duration"] = $start->diffInHours($end) . ':' . $start->diff($end)->format('%I:%S');
                    if (isset($GetInfor['timeOut'][$key][$timekey]))
                        $portagentinfo[$counter]["outtime"] = $GetInfor['timeOut'][$key][$timekey];
                    $portagentinfo[$counter]["queuename"] = $Queue['queuename'][$key][$timekey];
                    $portagentinfo[$counter]["callid"] = $Queue['callid'][$key][$timekey];
                    $counter++;
                }
            }
        }
        return response()->json($portagentinfo);
    }

    public function getPauseReasonReportFilter(Request $request)
    {
    // Filter Report PauseReause

        $where="1 ";
        $status=0;
        if(isset($request->time)){
        $where .=" and time between '".$request->time[0]."' and '".$request->time[1]."'";
        $status=1;
        }
        if(isset($request->callid)){
            $where .= " and callid= '".$request->callid."' ";
            $status=1;
        }
        if(isset($request->agent)){
            $where .= "and agent= '".$request->agent."'";
            $status=1;
        }
        if(isset($request->event)){
            $where .= " and event= '".$request->event."'";
            $status=1;
        }
        if(isset($request->queuename)){
            $where .= " and queuename= '".$request->queuename."'";
            $status=1;
        }
        $Query= "SELECT * FROM queue_log WHERE $where AND EVENT IN ('PAUSE', 'UNPAUSE') ";

        $data= array();
        if($status==1)
            $data= DB::select($Query);

        $getagaentinfor = array ();
        $GetInfor = array ();
        $Queue = array ();
        foreach ($data as $value) {
            $agentdatabody = str_replace("/", "", $value->Agent);
            // echo $agentdata;
            if ($value->Event == "PAUSE") {
                $getagaentinfor["agent"][$agentdatabody] = $value->Agent;
                $getagaentinfor["agentname"][] = $agentdatabody;
                $GetInfor["timeIn"][$agentdatabody][] = $value->time;
                $Queue["queuename"][$agentdatabody][] = $value->queuename;
                $Queue["callid"][$agentdatabody][] = $value->callid;

            }
            if ($value->Event == "UNPAUSE") {
                $GetInfor["timeOut"][$agentdatabody][count($GetInfor["timeIn"][$agentdatabody]) - 1] = $value->time;
            }
        }
        $portagentinfo = array();
        $counter = 0;
        if(isset($getagaentinfor['agent'])) {
            foreach ($getagaentinfor['agent'] as $key => $agentname) {
                foreach ($GetInfor["timeIn"][$key] as $timekey => $Intime) {
                    $start = new Carbon($Intime);
                    $end = '';
                    if (isset($GetInfor['timeOut'][$key][$timekey]))
                        $end = new Carbon($GetInfor['timeOut'][$key][$timekey]);

                    $portagentinfo[$counter]["agent"] = $agentname;
                    $portagentinfo[$counter]["intime"] = $Intime;
                    $portagentinfo[$counter]["duration"] = $start->diffInHours($end) . ':' . $start->diff($end)->format('%I:%S');
                    if (isset($GetInfor['timeOut'][$key][$timekey]))
                        $portagentinfo[$counter]["outtime"] = $GetInfor['timeOut'][$key][$timekey];
                    $portagentinfo[$counter]["queuename"] = $Queue['queuename'][$key][$timekey];
                    $portagentinfo[$counter]["callid"] = $Queue['callid'][$key][$timekey];
                    $counter++;
                }
            }
        }
        return response()->json($portagentinfo);
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AgnetReport  $agnetReport
     * @return \Illuminate\Http\Response
     */
    public function destroy(AgnetReport $agnetReport)
    {
        //
    }
}
