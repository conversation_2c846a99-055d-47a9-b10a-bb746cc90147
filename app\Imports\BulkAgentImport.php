<?php

namespace App\Imports;

use App\Models\User;
use Maatwe<PERSON>ite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\Importable;

use Hash;
use DB;
use App\Models\Role;


class BulkAgentImport implements ToModel,WithHeadingRow,WithValidation,SkipsOnFailure
{
    use Importable, SkipsFailures;
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        $user =  User::create([
            'name' => $row['name'],
            'username' => $row['user_name'],
            'password' => Hash::make($row['password']),
            'email' => $row['email'],
            'type' => $row['type'],
            'auth_username' => $row['auth_username'],
            'auth_password' => $row['auth_password'],
        ]);

        if($row['queue']) {
            $queue = $row['queue'];
            /** @var User $user */
            $user->queues()->attach($queue);
        }
        $user->syncRoles(['agent']);

        DB::table('ps_aors')->insert([
            'id' => $row['auth_username'],
            'max_contacts' => 1,
            'remove_existing' => 'yes',
            'qualify_frequency' => 1,
        ]);
        DB::table('ps_auths')->insert([
            'id' => $row['auth_username'],
            'auth_type' => 'userpass',
            'password' => $row['auth_password'],
            'username' => $row['auth_username']
        ]);
        DB::table('ps_endpoints')->insert([
            'id' => $row['auth_username'],
            'transport' => 'transport-wss',
            'aors' => $row['auth_username'],
            'context' => 'default',
            'disallow' => 'all',
            'allow' => 'alaw,ulaw,opus',
            'direct_media' => 'no',
            'webrtc' => 'yes'
        ]);
    }

    public function rules(): array
    {
        return[
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:Inbound,Outbound,Blended'],
            'user_name' => ['required','min:3','unique:users,username'],
            'email' => ['required','email'],
            'password' => [ 'string', 'min:4' , "required"],
            'auth_username' => ['required', 'min:3','numeric', 'unique:users,auth_username'],
            'auth_password' => ['required', 'min:3', 'numeric', 'unique:users,auth_password'],
            'queue' => ['sometimes', 'exists:queues,name'],
        ];
    }
    public function customValidationMessages(){

        return [
            'name.required'=>"Name is required",
            'type.required' => "Type is required",
            'user_name.unique' => 'User Name should be unique',
            'password.required' =>  'Password is required',
            'auth_username.unique' =>'Auth Username should be unique',
            'auth_password.unique' =>  'Auth Password should be unique',
            'email.required' => 'Email is required'
        ];
    }
}
