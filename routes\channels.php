<?php

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Cache;
use App\Models\Script;
/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::routes(['middleware' => ['auth:sanctum']]);

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('agent-panel-isready-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-login-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-pause-reason-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-workcode-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-pannel-agent-status-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-queue-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-getscriptbyqueue-channel.{id}-{queue}', function ($user, $userId, $queue) {
    if ((int) $user->id !== (int) $userId) {
        return false;
    }

    $exists = Script::query()->where('queue_name', $queue)->where('status', true)->exists();
    // if (!$exists) {
    //     return false;
    // }

    return true;
});

Broadcast::channel('agent-panel-getabandoncallreport-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-getschedule-callback-report-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-campaign-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-user-campaign-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-systemsetting-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-getuser-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('agent-panel-schedule-callback-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('overall_unread_count.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('unread_message_count.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('message_channel.{conversationId}', function ($user, $conversationId) {
    return \App\Models\Conversation::where(function ($query) use ($user, $conversationId) {
        $query->where('user_one', $user->id)
              ->orWhere('user_two', $user->id);
    })->where('id', $conversationId)->exists();
});

Broadcast::channel('agent-panel-getcallbackrequest-channel.{id}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Live Dashboard Monitoring
Broadcast::channel('dashboard-agent-queue-channel.{id}.{queue}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('dashboard-data-queue-channel.{id}.{queue}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('dashboard-outbond-channel.{id}.{queue}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('dashboard-outbond-agent-channel.{id}.{queue}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});