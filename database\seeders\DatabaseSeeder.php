<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {

        $this->call(QueueSeeder::class);
        $this->call(SystemSettingSeeder::class);
        $this->call(LaratrustSeeder::class);
        $this->call(FormFieldTypeSeeder::class);
        $this->call(CampaignSettingSeeder::class);
        $this->call(FormFunctionTypSeeder::class);
        $this->call(ServiceRatingSettingSeeder::class);
        $this->call(TickerSeeder::class);
        $this->call(NavbarSeeder::class);
        
        
    }
}
