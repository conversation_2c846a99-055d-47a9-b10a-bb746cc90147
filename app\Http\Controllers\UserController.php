<?php

namespace App\Http\Controllers;

use App\Actions\Fortify\PasswordValidationRules;
use App\Models\User;
use Dotenv\Validator;
use GuzzleHttp\Psr7\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\Rule;
use App\Rules\MatchOldPassword;

class UserController extends Controller
{
    use PasswordValidationRules;
    public function __construct()
    {

        $this->middleware('permission:create_users', ['only' => ['store']]);
        $this->middleware('permission:update_users', ['only' => ['update']]);
        $this->middleware('permission:delete_users', ['only' => ['destroy']]);
        $this->middleware('permission:read_users', ['only' => ['index']]);
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        return response()->json(User::query()->where('type', 'Normal')->get());
    }

    public function getAllUsers()
    {
        try {
            $collection = collect(User::all())->map(function ($name) {
                return $name->username;
            })->reject(function ($name) {
                return empty($name);
            });
            return response()->json($collection);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // dd($request->all());
        $request->validate([
            'name' => ['required', 'string', 'min:3'],
            'username' => ['required', 'unique:users,username', 'min:3'],
            'email' => ['required', 'email', 'unique:users,email', 'regex:/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'],
            'password' => ['string', 'min:4', 'confirmed', "required"],
        ]);

        try {
            $data = array_merge(
                $request->except('password'), 
                ['password' => Hash::make($request->password), 'type' => 'Normal']
            );
            $user = User::query()->create($data);
            return response()->json("User {$user->name} has been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
       
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, User $user): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'min:3'],
            'username' => ['required', 'min:3', Rule::unique('users')->ignoreModel($user, 'username')],
            'email' => ['required', 'email', 'unique:users,email,' . $user->id, 'regex:/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'],
            //            'password' => [ 'string', 'min:4', 'confirmed' , "required"],
        ]);
        try {
            $user->update($request->all());
            return response()->json("User {$user->name} has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  User  $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(User $user)
    {
        try {
            $user->delete();
            return response()->json("{$user->name} has been deleted");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }


    public function changePassword(Request $request)
    {
        // $user = $request->user();
        $request->validate([
            'current_password' => ['required', new MatchOldPassword],
            'new_password' => ['required'],
            'new_confirm_password' => ['same:new_password'],
        ]);

        User::find(auth()->user()->id)->update(['password' => Hash::make($request->new_password)]);

        return response()->json("Password change successfully.");
    }
}
