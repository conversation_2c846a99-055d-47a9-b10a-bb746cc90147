<?php

namespace App\Http\Controllers;

use App\Models\Script;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ScriptController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        try {
            return response()->json(Script::query()->with(['owner', 'queue'])->get());
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getScriptByQueue(Request $request)
    {
        $request->validate([
            'queue.*' => ['required', 'exists:queues,name']
        ]);
        try {
            return response()->json(Script::query()->whereIn('queue_name', $request->queue)->where('status', true)->get());
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(),500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'unique:scripts,name'],
            'content' => ['required'],
            'queue' => ['required', 'exists:queues,name']
        ]);

        try {
            $script = new Script;
            $script->fill($request->all());
            $script->owner()->associate($request->user());
            $script->queue()->associate($request->queue);
            $script->save();
            return response()->json("Script has been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }

    /**
     * Update Script Status.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, Script $script) : \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'status' => ['required', 'boolean'],
        ]);

        try {
            $script->status = $request->status;
            $script->save();
            Script::where('id', '!=', $script->id)->update(['status' => false]);
            
            return response()->json("Script {$script->name} has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 400);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Script  $script
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Script $script): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'name' => ['required', 'string', Rule::unique('scripts')->ignoreModel($script)],
            'content' => ['required'],
            'queue' => ['required', 'exists:queues,name']
        ]);

        try {
            $script->fill($request->all());
            $script->queue()->associate($request->queue);
            $script->save();
            return response()->json("Script {$script->name} has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Script  $script
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Script $script): \Illuminate\Http\JsonResponse
    {
        try {
            $script->delete();
            return response()->json("Script has been deleted.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
