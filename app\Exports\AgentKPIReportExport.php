<?php

namespace App\Exports;

use DateTime;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AgentKPIReportExport implements  WithHeadings, WithMapping,FromArray
{
    /**
     * @return \Illuminate\Support\Collection
     */
 

    function __construct(array $data)
    {
        $this->data= $data;
    }
    public function array(): array
    {
        return $this->data;
    }
 

    public function map($data): array
    {   
        // dd($data->Agent);
        return [
            $data->date,
            $data->Agent,
            $data->totalLoginTime,
            $data->totalSiginTime,
            $data->totalBreakTime,
            $data->totalCalls,
            $data->totalTalkTime,
            $data->AHT
        ];
    }

    public function headings(): array
    {
        return [
            'Date',
            'Agent Name',
            'Total Login Time',
            'Total Sign On Time',
            'Total Break Time',
            'Total Call',
            'Total Talk Time',
            'AHT'

        ];
    }
}
