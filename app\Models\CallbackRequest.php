<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CallbackRequest extends Model
{
    use HasFactory;

    protected $table = 'callback_requests';

    protected $fillable = [
        'caller_id',
        'queue',
        'agent',
        'filePath',
        'fileName',
        'fileLoc',
        'status',
        'answered_date',
        'request_time',
    ];

}
