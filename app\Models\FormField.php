<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormField extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'label', 'required'];
    protected $appends = ['type'];

    public function getTypeAttribute()
    {
        return $this->form_field_type()->first()->html;
    }

    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function form(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Form::class);
    }

    public function form_field_type(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(FormFieldType::class);
    }

    public function form_field_options(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(FormFieldOption::class);
    }

    public function reference_field(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(FormFunction::class ,'reference_field_id');
    }
}
