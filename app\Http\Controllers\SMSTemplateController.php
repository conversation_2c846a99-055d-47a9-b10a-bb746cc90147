<?php

namespace App\Http\Controllers;

use App\Models\SMS;
use App\Models\SMSCategory;
use App\Models\SMSTemplate;
use App\Models\SystemSetting;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class SMSTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response()->json(SMSTemplate::query()->with('sMSCategory')->get());
    }

    public function sendSMS(Request $request)
    {
        $request->validate([
            'number' => ['required', 'min:10'],
            'sms' => ['required', 'exists:s_m_s_templates,id']
        ]);

        try {
            $setting = SystemSetting::query()->whereIn('key', ['sms_url', 'pwd' ,'userid'])->orderBy('key', 'asc')->get();
            if(count($setting) > 0) {
                $number = "";
                if(strpos($request->number, '3') == 0) $number = '92'. $request->number;
                else if(strpos($request->number, '03') == 0) $number = '92'. ltrim($request->number, '0');
                else if(strpos($request->number, '+92') == 0) $number = ltrim($request->number, '+');
                else if(strpos($request->number, '92') == 0) {}
                else return response()->json("it's not a mobile number");

                $sMSContent = SMSTemplate::query()->where('id', $request->sms)->first();
                $url = $setting[1]->value."?userid=".$setting[2]->value."&pwd=".$setting[0]->value."&mobileno=". $number ."&msg=".urlencode($sMSContent->msg);
                $response = Http::get($url);
                if ($response->successful()) {
                    SMS::query()->insert(['number' => $request->number, 'sms_template_id' => $request->sms]);
                    return $response->getBody();
                }
                else if ($response->serverError())
                    return response()->json($response->serverError());
                else if ($response->clientError())
                    return response()->json($response->clientError());
                return response()->json($response->getBody());
            }
            return response()->json("SMS URL not found");
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'msg' => ['required', 'string'],
            'sms_category_id' => ['required', 'exists:s_m_s_categories,id', 'unique:s_m_s_templates,sms_category_id']
        ]);
        try {
            $sMSTemplate = new SMSTemplate();
            $sMSTemplate->fill($request->except('sms_category_id'));
            $sMSTemplate->sMSCategory()->associate($request->sms_category_id);
            $sMSTemplate->save();
            return response()->json('SMS Tempalte has been inserted');
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

//    public function getSMSDetails(Request $request)
//    {
//        try {
//            $category = SMSCategory::query()->
//            $template
//        } catch (\Exception $exception){
//            return response()->json($exception->getMessage(), 500);
//        }
//    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\SMSTemplate  $sMSTemplate
     * @return \Illuminate\Http\Response
     */
    public function show(SMSTemplate $sMSTemplate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\SMSTemplate  $sMSTemplate
     * @return \Illuminate\Http\Response
     */
    public function edit(SMSTemplate $sMSTemplate)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\SMSTemplate  $sMSTemplate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, SMSTemplate $sMSTemplate)
    {
        $request->validate([
            'msg' => ['required', 'string'],
            'sms_category_id' => ['required', 'exists:s_m_s_categories,id']
        ]);
        try {
            $sMSTemplate->fill($request->all());
            $sMSTemplate->sMSCategory()->associate($request->sms_category_id);
            $sMSTemplate->save();
            return response()->json('SMS Tempalte has been inserted');
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\SMSTemplate  $sMSTemplate
     * @return \Illuminate\Http\Response
     */
    public function destroy(SMSTemplate $sMSTemplate)
    {
        try {
            $sMSTemplate->delete();
            return response()->json("SMS Template has been deleted");
        } catch (\Exception $exception){
            return response()->json($exception->getMessage(), 500);
        }
    }
}
