<?php

namespace App\Http\Traits;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;
use App\Models\BitrixIntegration;
use App\Models\BitrixCallLog;
use App\Models\User;
use App\Models\Cdr;
use Illuminate\Support\Facades\Storage;


use Carbon\Carbon;

Trait BitrixIntegrationTrait{

    public function leadOnOutgoingCall(Request $request){

        $request->validate([
            'phone_number' => 'required',
            'unique_id' => 'required',
        ]);
        try{

            $number = $request->phone_number;

            if($number[0] === '0'){
                $array = explode('0', $number, 2);
                $phoneNumber = '+92'.$array[1];
            }else{
                $phoneNumber = '+92'.$number;
            }
            //dd($phoneNumber);

            $api = BitrixIntegration::first();
            $api_key = $api->api_key;
            $api_enable = $api->is_enable;
            $bitrix_id = Auth::user()->bitrix_id;

            if($api_enable === 1 && $bitrix_id !== NULL){
                $internal_number = Auth::user()->auth_username;

                $response = Http::post("$api_key/telephony.externalcall.register",[
                    'USER_ID' => $bitrix_id,
                    'USER_PHONE_INNER' => $internal_number,
                    'PHONE_NUMBER' => $phoneNumber,
                    'TYPE' => 1,
                    'CALL_START_DATE' => Carbon::now()->toISOString(),
                    'CRM_CREATE' => 1,
                    'CRM_SOURCE' => 'Call',
                    'CRM_ENTITY_TYPE' => 'Lead',
                ]);
                $response = $response->object();
                $call_id = $response->result->CALL_ID;
                $call_log = new BitrixCallLog;
                $call_log->call_id = $call_id;
                $call_log->call_unique_id = $request->unique_id;
                $call_log->save();
                return response()->json($response);
            }else{
                return false;
            }

        }catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function leadOnIncomingCall(Request $request){

        $request->validate([
            'phone_number' => 'required',
            'unique_id' => 'required',
        ]);

        //condition for storing number starting with 92

        try{

            $number =$request->phone_number;

            if($number[0] === '0'){
                $array = explode('0', $number, 2);
                $phoneNumber = '+92'.$array[1];
            }else{
                $phoneNumber = '+92'.$number;
            }

            $api = BitrixIntegration::first();
            $api_key = $api->api_key;
            $api_enable = $api->is_enable;
            $bitrix_id = Auth::user()->bitrix_id;

            if($api_enable === 1 && $bitrix_id !== NULL){
                $internal_number = Auth::user()->auth_username;
                //$phone_number = $request->phone_number;
                //$date= Carbon::now()->format('Y-m-d');

                $response = Http::post("$api_key/telephony.externalcall.register",[
                    'USER_ID' => $bitrix_id,
                    'USER_PHONE_INNER' => $internal_number,
                    'PHONE_NUMBER' => $phoneNumber,
                    'TYPE' => 2,
                    'CALL_START_DATE' => Carbon::now()->toISOString(),
                    'CRM_CREATE' => 1,
                    'CRM_SOURCE' => 'Call',
                    'CRM_ENTITY_TYPE' => 'Lead',
                ]);

                $response = $response->object();
                $call_id = $response->result->CALL_ID;
                $call_log = new BitrixCallLog;
                $call_log->call_id = $call_id;
                $call_log->call_unique_id = $request->unique_id;
                $call_log->save();
                return response()->json($response);

            }else{
                return false;
            }
        }catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }

    }

    public function callFinish(Request $request){

        usleep(5000000);
        $request->validate([
            'unique_id' => 'required',
            'call_id' => 'required',
        ]);

        try{
            $api = BitrixIntegration::first();
            $api_key = $api->api_key;
            $api_enable = $api->is_enable;
            $bitrix_id = Auth::user()->bitrix_id;

            if($api_enable === 1 && $bitrix_id !== NULL){

                $cdr = Cdr::where('uniqueid', $request->unique_id)->orderBy('end' , 'DESC')->first();
                $recordingFileName =  $cdr->recordingfile;
                $response = Http::post("$api_key/telephony.externalcall.finish",[

                    'CALL_ID' => $request->call_id,
                    'USER_ID' => $bitrix_id,
                    'DURATION' => $cdr->billsec,
                ]);
                //attach record

                $date = Carbon::createFromFormat("Ymd", explode("-", $recordingFileName)[3]);
                $recordingFile = Storage::disk('recordings')->get("{$date->year}/{$date->format('m')}/{$date->format('d')}/$recordingFileName");

                $recordingFile = base64_encode($recordingFile);

                $response = Http::post("$api_key/telephony.externalCall.attachRecord",[

                    'CALL_ID' => $request->call_id,
                    'FILENAME'=> $recordingFileName,
                    'FILE_CONTENT' => $recordingFile,

                ]);

                return response()->json($response->object());

            }else{
                return false;
            }

        }catch (\Exception $exception) {
            return response()->json($exception->getMessage(),500);
        }



    }
    public function customCallRouting($phone_number){
        try{
            $api = BitrixIntegration::first();
            $api_key = $api->api_key;
            $api_enable = $api->is_enable;

            if($api_enable === 1){
                $number = $phone_number;

                if($number[0] === '0'){
                    $array = explode('0', $number, 2);
                    $phoneNumber = '+92'.$array[1];
                }else{
                    $phoneNumber = '+92'.$number;
                }

                $data = ["PHONE" => $phoneNumber];
                $select = ['ID', 'ASSIGNED_BY_ID'];

                $result = http::post("$api_key/crm.lead.list",[
                    'filter' => $data,
                    'select' => $select,
                ]);

                $response = $result->object();
                if($response->result){
                    $creator_id = $response->result[0]->ASSIGNED_BY_ID;
                    $user = User::where('bitrix_id', $creator_id)->first();
                    //return response($user);
                    $user_ext = $user->auth_username ?? NULL;
                    return $user_ext;
                }else{
                    return false;
                }
            }

        }catch(\Exception $exception){
            return response()->json($exception->getMessage(),500);

        }
    }

    // public function fileAttach(Request $request){


    //     try{
    //         $cdr =  Cdr::where('uniqueid' ,$request->unique_id)->orderBy('end' ,'DESC')->first();
    //         $recordingFileName =  $cdr->recordingfile;

    //         $date = Carbon::createFromFormat("Ymd", explode("-", $recordingFileName)[3]);
    //         $recordingFile = Storage::disk('recordings')->get("{$date->year}/{$date->format('m')}/{$date->format('d')}/$recordingFileName");

    //         $recordingFile = base64_encode($recordingFile);

    //         $api = BitrixIntegration::first();
    //         $api_key = $api->api_key;
    //         $response = Http::post("$api_key/telephony.externalCall_attachReport",[

    //             'CALL_ID' => $request->call_id,
    //             'FILENAME'=> $recordingFileName,
    //             'FILE_CONTENT' => $recordingFile,

    //         ]);

    //         return response()->json($response->object());

    //     }catch (\Exception $exception) {
    //         return response()->json($exception->getMessage(), 500);
    //     }
    // }
}



