<?php

namespace App\Exports;

use App\Models\CampaignNumber;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Contracts\Queue\ShouldQueue;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithChunkReading;


class AgentlessCampaignExport implements FromArray,WithHeadings,WithMapping
{
    /**
    * @return \Illuminate\Support\Collection
    */

    protected $data;
    public function __construct(array $data)
    {
        $this->data = $data;

    }
    public function array(): array
    {
        return $this->data;
    }

    public function map($row): array
    {
        $dst = $row['dst'];
        if($row['dst'] === 's') {
            $dst = $row['recordingfile'];
        }
        return [
            'Outbound',
            $row['src'],
            $dst,
            $row['start'],
             ucfirst($row['disposition']),
            $row['answer'] ?? NULL,
            $row['start'],
            $row['userfield'],
            $row['billsec']
        ];
    }

    public function headings(): array
    {
        return [
            'CallType',
            'Outgoing Number',
            'Mobile Number',
            'CreatedDateTime',
            'CallResponse',
            'CallPickDateTime',
            'CallStartDateTime',
            'CallInput',
            'CallDuration'
        ];
    }
    // public function chunkSize(): int
    // {
    //     return 1000;
    // }

}
