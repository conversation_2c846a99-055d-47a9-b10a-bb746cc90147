<?php

namespace App\Jobs;

use App\Models\AgentlessCampaign;
use App\Models\AgentlessCampaignLog;
use App\Models\CallingServer;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AgentlessCampaignHandlerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */

    public int $campaign;
    public int $timeout = 3600;


    public function __construct($campaign)
    {
        $this->campaign = $campaign;
    }


    public function tags()
    {
        return ['campaign_handler_job', 'campaign:' . $this->campaign];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try{
            $server =CallingServer::first()->id;
            AgentlessCampaignLog::create(['campaign_id' => $this->campaign, 'log' => 'started']);
            AgentlessCampaign::where('id', $this->campaign)->update(['stage' => 'started']);
            AgentlessServerHandlerJob::dispatch($server, $this->campaign)->onQueue("default");
        }catch (\Exception $e){
            Log::info("AgentlessCampaignHandlerJob::[campaignId {$this->campaign}]: {$e->getMessage()}");
        }
    }
}
