<?php

namespace Database\Seeders;

use App\Models\PrepaidSetting;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PrepaidSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $setting = new PrepaidSetting();
        $setting->start_date = Carbon::now();
        $setting->pulse_duration = 10;
        $setting->tariff = 1;
        $setting->amount = 1000;
        $setting->remaining_amount = 1000;
        $setting->pkg_update = Carbon::now();
        $setting->save();
    }
}
