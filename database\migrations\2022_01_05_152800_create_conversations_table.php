<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
class CreateConversationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Schema::create('conversations', function (Blueprint $table) {
        //     $table->id();
        //     $table->foreignId('user_one')
        //         ->constrained('users')
        //         ->onUpdate('cascade')
        //         ->onDelete('cascade');
        //     $table->foreignId('user_two')
        //         ->constrained('users')
        //         ->onUpdate('cascade')
        //         ->onDelete('cascade');
        //     $table->timestamps();
        // });
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_one')
                ->constrained('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreignId('user_two')
                ->constrained('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->unique(['user_one', 'user_two']); 
            $table->timestamps();
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('conversations');
    }
}