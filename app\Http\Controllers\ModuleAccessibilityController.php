<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ModuleAccessibility;


class ModuleAccessibilityController extends Controller
{

    
    public function index()
    {
        $moduleAccessibility = ModuleAccessibility::where('module_name', 'sms')->first();
        return response()->json(['data' => $moduleAccessibility]);
    }

    public function update(Request $request)
    {
        $moduleAccessibility = ModuleAccessibility::where('module_name', 'sms')->first();
        if ($moduleAccessibility) {
            $moduleAccessibility->update([
                'agent_accessibility' => $request->agent_accessibility,
            ]);
        } else {
            ModuleAccessibility::create([
                'module_name' => 'sms',
                'agent_accessibility' => $request->agent_accessibility,
            ]);
        }

        return response()->json([
            'message' => 'SMS module accessibility updated successfully!',
        ]);
    }
    public function getEmail()
    {
        $moduleAccessibility = ModuleAccessibility::where('module_name', 'email')->first();
        return response()->json(['data' => $moduleAccessibility]);
    }
    public function updateEmail(Request $request)
    {
        $moduleAccessibility = ModuleAccessibility::where('module_name', 'email')->first();
        if ($moduleAccessibility) {
            $moduleAccessibility->update([
                'agent_accessibility' => $request->agent_accessibility,
            ]);
        } else {
            ModuleAccessibility::create([
                'module_name' => 'email',
                'agent_accessibility' => $request->agent_accessibility,
            ]);
        }

        return response()->json([
            'message' => 'Email module accessibility updated successfully!',
        ]);
    }



}
