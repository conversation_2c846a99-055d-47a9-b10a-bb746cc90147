<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// class CreateVoicemailsTable extends Migration
return new class extends Migration

{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('voicemails', function (Blueprint $table) {
            $table->id();
            $table->string('filePath');
            $table->string('fileName');
            $table->string('fileLoc');
            $table->string('number');
            $table->string('uniqueid');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('voicemails');
    }
};
