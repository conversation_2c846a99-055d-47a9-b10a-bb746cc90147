<?php

namespace App\Http\Controllers;

use App\Models\CallingServer;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CallingServerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try{
            $servers = DB::table('calling_servers')->get();
            return response()->json(['servers' => $servers],200);
        }catch(Exception $e){
            return response()->json(['message' => $e->getMessage()],500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'host' => ['required'],
            'port' => ['required', 'integer'],
            'scheme' => ['required'],
            'secret' => ['required'],
            'connect_timeout' => ['required'],
            'read_timeout' => ['required'],
            'allowed_limit' => ['required', 'integer'],
            'caller_id' => ['required'],
            'trunk' => ['required'],
            'context' => ['required'],
            'timeout' => ['integer']
        ]);

        try{

            CallingServer::create($request->all());
            return response()->json(['message' => 'Calling server has been created.'],200);

        }catch(Exception $e){
            return response()->json(['message' => $e->getMessage()],500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request , $id)
    {
        $request->validate([
            'host' => ['required'],
            'port' => ['required', 'integer'],
            'scheme' => ['required'],
            'secret' => ['required'],
            'connect_timeout' => ['required'],
            'read_timeout' => ['required'],
            'allowed_limit' => ['required', 'integer'],
            'caller_id' => ['required'],
            'trunk' => ['required'],
            'context' => ['required'],
            'timeout' => ['integer']
        ]);

        try{
            $calling_server = CallingServer::findOrFail($id);
            $calling_server->update($request->all());
            return response()->json(['message' => 'Calling server has been updated.'],200);

        }catch(Exception $e){
            return response()->json(['message' => $e->getMessage()],500);
        }

    }

    public function destroy($id)
    {
        try{
            $calling_server = CallingServer::findOrFail($id);
            $calling_server->delete();
            return response()->json(['message' => 'Calling server has been deleted.'],200);
        }catch(Exception $e){
            return response()->json(['message' => $e->getMessage()],500);
        }
    }
}
