<?php

namespace App\Exports;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class DailyReportExport implements FromQuery, WithHeadings, WithMapping
{
    /**
     * @return \Illuminate\Support\Collection
     */
    
    function __construct($date) {
        $this->date = $date;
        // $this->sum =0;
        $this->CURRENTTIME = Carbon::now()->format('H');
        
    }
    public function query()
    { 
        $CURRENTDATE = Carbon::now()->format('Y-m-d');
        if ($CURRENTDATE == $this->date) 
        {
            $cdr = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND Date(time) = '$this->date' "))
                ->whereRaw('CURRENT_TIME()>hour')
                ->groupBy(DB::raw('hour'))
                ->orderBy('hour')
                ->select(
                    DB::raw("
                    Date(time) as Date,
                    hour AS 'to', 
                    AddTime(hour, '00:30:00') AS 'from' ,
                    SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                    SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) as totalAnswer20Sec,
                    SUM(CASE WHEN (Event = 'CONNECT' AND data1 >= 20) THEN 1 ELSE 0 END) as totalAnswerAfter20Sec,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                    SUM(CASE WHEN (Event = 'ABANDON' AND data3 <=30) THEN 1 ELSE 0 END) as totalAbandon30Sec,
                    SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) as totalAbandonAfter30Sec,
                    SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalCallsInQueue,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),100) AS GOS,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),0) AS ACR,
                    SUM(CASE WHEN (Event = 'ADDMEMBER') THEN 1 ELSE 0 END) -SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN 1 ELSE 0 END) AS agentLogin,
                    SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END)  AS shortage")
                );
            return $cdr;
        } 
        else {
            $cdr = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND Date(time) = '$this->date' "))
                ->groupBy(DB::raw('hour'))
                ->orderBy('hour')
                ->select(
                    DB::raw("
                    Date(time) as Date,
                    hour AS 'to', 
                    AddTime(hour, '00:30:00') AS 'from' ,
                    SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                    SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) as totalAnswer20Sec,
                    SUM(CASE WHEN (Event = 'CONNECT' AND data1 >= 20) THEN 1 ELSE 0 END) as totalAnswerAfter20Sec,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                    SUM(CASE WHEN (Event = 'ABANDON' AND data3 <=30) THEN 1 ELSE 0 END) as totalAbandon30Sec,
                    SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) as totalAbandonAfter30Sec,
                    SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalCallsInQueue,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= 20) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),100) AS GOS,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON' AND data3 >=30) THEN 1 ELSE 0 END) / SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)*100,2),0) AS ACR,
                    SUM(CASE WHEN (Event = 'ADDMEMBER') THEN 1 ELSE 0 END) -SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN 1 ELSE 0 END) AS agentLogin,
                    SUM(CASE WHEN (Event = 'CONNECT' AND (data1 - data3) > 1) THEN 1 ELSE 0 END) + SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END)  AS shortage")
                );

            return $cdr;
        }
    }
    public function map($cdr): array
    {
        $sum =0;
        $sum = (int)$cdr->agentLogin + $sum;
        
        if ((int)$this->CURRENTTIME < (int)substr($cdr->to, 0, 2) and $this->CURRENTTIME == $this->date  or $sum < 0) {

            $cdr->agentLogin = 0;
        } else {
            $cdr->agentLogin = $sum;
        }


        if ($cdr->shortage != 0) {
            $shortage =   $cdr->shortage - $sum;
            $cdr->shortage = $shortage;
        }

        if ($cdr->Date == NULL) {
            $cdr->Date = $this->date;
        }
      
        return [
            $cdr->Date,
            $cdr->to,
            $cdr->from,
            $cdr->totalInbounCalls,
            $cdr->totalAnswerCalls,
            $cdr->totalAnswer20Sec,
            $cdr->totalAnswerAfter20Sec,
            $cdr->totalAbandonCalls,
            $cdr->totalCallsInQueue,
            $cdr->totalAbandon30Sec,
            $cdr->totalAbandonAfter30Sec,
            $cdr->GOS,
            $cdr->ACR,
            $cdr->agentLogin == 0 ? '0' : $cdr->agentLogin ,
            $cdr->shortage
        ];
    }
    public function headings(): array
    {
        return [
            'Date',
            'From',
            'To',
            'Total Inbound Calls',
            'Total Ans. Calls',
            'Ans. In 20 Sec',
            'Ans. After 20 Sec',
            'Total Abandoned',
            'Abandoned In 30 Sec',
            'Abandoned After 30 Sec',
            'Total In Queued',
            'GOS',
            'ASR',
            'No of Agents Online',
            'Shortage',
        ];
    }
}
