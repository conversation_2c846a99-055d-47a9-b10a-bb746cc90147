<?php

namespace App\Http\Controllers;

use App\Models\Form;
use App\Models\FormFieldType;
use App\Models\FormField;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class FormController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        return response()->json(Form::query()->with(['owner', 'form_fields'])->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'name' => ['required', 'unique:forms,name', 'string', 'max:255', 'min:3'],
            'queue_name' => ['required', 'exists:queues,name'],
            
        ]);

        try {
            $form = new Form;
            $form->name = $request->name;
            $form->owner()->associate($request->user());
            $form->queue()->associate($request->queue_name);
            $form->default = $request->default ?? '0';
            $form->save();

            if($form)
            {
                $fieldType = FormFieldType::query()->where('html', 'input')->first();
                $field = new FormField;
                $field->name = 'phone_number';
                $field->label = 'Phone Number';
                $field->required = 1;
                $field->form_id = $form->id;
                $field->user()->associate($request->user());
                $field->form_field_type()->associate($fieldType);
                $field->save(); 
            }
            
            return response()->json("Form {$form->name} has been created.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Form  $form
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Form $form): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'name' => ['required', Rule::unique('forms', 'name')->ignoreModel($form), 'string', 'max:255', 'min:3'],
            'queue_name' => ['required', 'exists:queues,name']
        ]);
        
        try {
            $form->update($request->all());
            return response()->json("Form {$form->name} has been updated.");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Form  $form
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Form $form): \Illuminate\Http\JsonResponse
    {
        try {
            $form->delete();
            return response()->json("Form {$form->name} has been deleted");
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
