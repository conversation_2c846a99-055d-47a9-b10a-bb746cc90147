<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\UserLimit;
use Illuminate\Http\Request;

class UserLimitController extends Controller
{
    public function __construct()
    {
        $this->middleware('role:superadministrator');
    }
    

    public function index()
    {
        return UserLimit::all();
      
    }

    public function update(Request $request)
    {
        $validatedData = $request->validate([
            'agent_limit' => 'required'
        ]);

        try {
            $userLimit = UserLimit::first();

            $data = [
                'agent_limit' => $validatedData['agent_limit']
            ];

            if ($userLimit) {
                $userLimit->update($data);

                return response()->json(
                    [
                        "status" => 200,
                        "message" => "Agent limit updated successfully."
                    ]
                );
            } else {
                $created = UserLimit::create($data);

                if($created) {
                    return response()->json(
                        [
                            "status" => 200,
                            "message" => "Agent limit created successfully."
                        ]
                    );
                }
                else {
                    return response()->json(
                        [
                            "status" => 400,
                            "message" => "Something went wrong."
                        ]
                    );
                }
            }

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
