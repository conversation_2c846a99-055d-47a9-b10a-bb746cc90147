<?php

namespace App\Models;

use PAGI\Application\PAGIApplication;

class ModelPAGIApplication extends PAGIApplication
{
    protected $agi;
    protected $asteriskLogger;
    protected $channelVariables;

    public function init()
    {
        $this->logger->info('Init');
        $this->agi = $this->getAgi();
        $this->asteriskLogger = $this->agi->getAsteriskLogger();
        $this->channelVariables = $this->agi->getChannelVariables();
        $this->asteriskLogger->notice('Init');
    }

    public function shutdown()
    {
        $this->asteriskLogger->notice('Shutdown');
    }

    public function run()
    {
        $this->asteriskLogger->notice("Run");
        $this->logger->info("Run");
    }

    public function errorHandler($type, $message, $file, $line)
    {
        $this->asteriskLogger->error("$message at $file:$line");
        $this->logger->error("$message at $file:$line");
    }

    public function signalHandler($signal)
    {
        $this->asteriskLogger->notice("Got signal: $signal");
        $this->logger->info("Got signal: $signal");
    }
}
