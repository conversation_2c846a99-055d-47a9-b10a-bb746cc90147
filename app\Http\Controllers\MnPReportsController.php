<?php

namespace App\Http\Controllers;

use App\Models\Cdr;
use App\Models\Queue;
use App\Models\QueueLog;
use App\Models\User;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MnPReportsController extends Controller
{
    public function agentStatus_old(Request $request)//
    {
        try {
            $user = $request->user();
            // dd($user->name);
            $agents = DB::table('queue_log')->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER', 'PAUSE', 'UNPAUSE', 'CONNECT'])
                ->where('time', 'LIKE', "{$request->date}%")
                ->whereBetween(DB::raw('TIME_FORMAT(time,"%H:%i")'), [$request->timeFrom, $request->timeTo])
                ->select(
                    DB::raw("Agent,
                CASE WHEN (Event = 'ADDMEMBER') THEN TIME_FORMAT(time,'%H:%i') ELSE '-'END AS 'Time in',
                CASE WHEN (Event = 'REMOVEMEMBER') THEN TIME_FORMAT(time,'%H:%i') ELSE '-' END AS 'Time out',
                Event,data1,data as status
            ")
                )
                ->get();

            foreach ($agents as $agent) {
                // if("PJSIP/{$user->auth_username}" == $agent->Agent )
                // {
                //     $agent->Agent = $user->name;
                // }
                if ($agent->Event == 'ADDMEMBER') {

                    $agent->status = 'Logged In';
                } elseif ($agent->Event == 'REMOVEMEMBER') {

                    $agent->status = 'Logged Out';
                } elseif ($agent->Event == 'PAUSE') {

                    $agent->status = 'Break';
                } elseif ($agent->Event == 'CONNECT' and $agent->data1 > 2) {

                    $agent->status = 'Hold';
                } elseif ($agent->Event == 'CONNECT' and $agent->data1 <= 2) {

                    $agent->status = 'On Call';
                }
            }
            return response()->json($agents);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function agentStatus(Request $request)
    {
        $request->validate([
            'date' => 'required',
            'from' => 'required',
            'to' => 'required',
        ]);

        $users = $request->agents;
        $date = $request->date;
        $from = $request->from;
        $to = $request->to;

        $data = [];
        $holdData = [];

        $queryBuilder = QueueLog::query();

        if ($users) {
            $queryBuilder->whereIn('Agent', $users);
        }
        if ($date) {
            $queryBuilder->whereDate('time', $date);
        }
        if ($from) {
            $queryBuilder->whereTime('time', '>=', $from);
        }
        if ($to) {
            $queryBuilder->whereTime('time', '<=', $to);
        }
        if (isset($request->queue) && !empty($request->queue)) {
            $queues = [$request->queue];
            $queryBuilder->whereIn('queue_log.queuename', $queues);
        }
        $records = $queryBuilder->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER', 'PAUSE', 'UNPAUSE'])->get();
        $hold = $queryBuilder->whereIn('Event', ['HOLD', 'UNHOLD', 'COMPLETECALLER', 'COMPLETEAGENT', 'PAUSE'])->get();
        // $breakRecords = $queryBuilder->whereIn('Event', ['PAUSE', 'UNPAUSE'])->whereNotIn('data1', ['Bio Break', 'Working on CXM', 'Namaz with lunch break', 'C                       ounselling/Training', 'Support on HFC Desk'])->get();

        // Process hold data separately
        for ($y = 0; $y < count($hold); $y++) {
            $time = Carbon::parse($hold[$y]->time)->format('H:i:s');

            if ($hold[$y]->Event == 'HOLD' && $y !== count($hold) - 1 && ($hold[$y + 1]->Event == 'UNHOLD' || $hold[$y + 1]->Event == 'REMOVEMEMBER' || $hold[$y + 1]->Event == 'COMPLETEAGENT' || $hold[$y + 1]->Event == 'COMPLETECALLER')) {
                $timeOut = Carbon::parse($hold[$y + 1]->time)->format('H:i:s');
                $timeIn = Carbon::parse($hold[$y]->time);
                $timeDiff = $timeIn->diff($timeOut);

                $holdData[] = [
                    'agentName' => $hold[$y]->Agent,
                    'Time-in' => $time,
                    'Time-out' => $timeOut,
                    'Time-Difference' => $timeDiff->format('%H:%I:%S'),
                    'Status' => 'On-Hold',
                    'timeForSorting' => $time
                ];
            }
        }

        // Process records
        $timeInMap = [];
        for ($x = 0; $x < count($records); $x++) {
            $agentName = $records[$x]->Agent;
            $eventTime = Carbon::parse($records[$x]->time)->format('H:i:s');

            if ($records[$x]->Event == 'ADDMEMBER') {
                $timeInMap[$agentName] = Carbon::parse($records[$x]->time);

                $data[] = [
                    'agentName' => $agentName,
                    'Time-in' => $eventTime,
                    'Time-out' => '-',
                    'Time-Difference' => '-',
                    'Status' => 'Logged In',
                    'timeForSorting' => $eventTime
                ];
            } elseif ($records[$x]->Event == 'REMOVEMEMBER' && isset($timeInMap[$agentName])) {
                $timeOut = Carbon::parse($records[$x]->time);
                $timeIn = $timeInMap[$agentName];

                $duration = $timeOut->diffInSeconds($timeIn);
                $durations = sprintf('%02d:%02d:%02d', floor($duration / 3600), floor($duration / 60 % 60), $duration % 60);

                $data[] = [
                    'agentName' => $agentName,
                    'Time-in' => '-',
                    'Time-out' => $eventTime,
                    'Time-Difference' => $durations,
                    'Status' => 'Logged Out',
                    'timeForSorting' => $eventTime
                ];

                // Clear the login time for this agent after logging out
                unset($timeInMap[$agentName]);
            }
        }

        // Handle case where there's a login without a corresponding logout
        foreach ($timeInMap as $agentName => $loginTime) {
            // Set the logout time as the end of the day
            $logoutTime = Carbon::now()->endOfDay();

            // Calculate the duration between login and end of day
            $duration = $logoutTime->diffInSeconds($loginTime);
            $durations = sprintf('%02d:%02d:%02d', floor($duration / 3600), floor($duration / 60 % 60), $duration % 60);

            $data[] = [
                'agentName' => $agentName,
                'Time-in' => Carbon::parse($loginTime)->format('H:i:s'),
                'Time-out' => $logoutTime->format('H:i:s'),
                'Time-Difference' => $durations,
                'Status' => 'Logged Out',
                'timeForSorting' => Carbon::parse($loginTime)->format('H:i:s')
            ];
        }

        $pauseStartTimes = [];
        $breakData = [];

        foreach ($records as $logEntry) {
            $breakType = $logEntry->data1;
            $agentName = $logEntry->Agent;
            $eventTime = Carbon::parse($logEntry->time);
            $breakDurations = '';
            $timeOut = null;

            // Check if this is a PAUSE event
            if ($logEntry->Event == 'PAUSE') {
                // Store the pause start time for the break type and agent
                $pauseStartTimes[$agentName][$breakType] = $eventTime;
            }
            // Check if this is an UNPAUSE event and the corresponding PAUSE event exists
            elseif ($logEntry->Event == 'UNPAUSE' && isset($pauseStartTimes[$agentName][$breakType])) {
                // Calculate the time difference when unpausing
                $timeOut = Carbon::parse($logEntry->time);
                $pauseStartTime = $pauseStartTimes[$agentName][$breakType];
                $duration = $timeOut->diffInSeconds($pauseStartTime);

                // Format the duration as H:i:s
                $breakSeconds = round($duration);
                $breakDurations = sprintf('%02d:%02d:%02d', floor($breakSeconds / 3600), floor($breakSeconds / 60 % 60), $breakSeconds % 60);

                // Add break data entry (combining PAUSE and UNPAUSE events)
                $breakData[] = [
                    'agentName' => $agentName,
                    'Time-in' => $pauseStartTime->format('H:i:s'),
                    'Time-out' => $timeOut->format('H:i:s'),
                    'Time-Difference' => $breakDurations,
                    'Status' => $breakType,
                    'timeForSorting' => $pauseStartTime->format('H:i:s')
                ];

                // Reset the pause start time after processing
                unset($pauseStartTimes[$agentName][$breakType]);
            }
        }

        $result = array_merge($data, $holdData);
        $result = array_merge($result, $breakData);
        array_multisort(array_column($result, "timeForSorting"), SORT_ASC, $result);
        return response()->json($result);
    }

    public function callQueueSummaryReport(Request $request)
    {
        try {
            $threshold = Queue::first('servicelevel');
            $differnce = 0;
            $totalAbd = 0;

            $cdr = DB::table('queue_log');

            if ($request->filled('queue')) {
                $cdr->where('queuename', $request->queue);
            }

            $cdr = $cdr->where('time', 'LIKE', "{$request->month}%")
                ->groupBy(DB::raw('Date(time)'))
                ->selectRaw("
                    Date(time),
                    SUM(CASE WHEN (Event = 'CONNECT' OR Event = 'ABANDON' OR Event = 'EXITWITHKEY') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                    SUM(CASE WHEN (Event = 'ABANDON' OR Event = 'EXITWITHKEY') THEN 1 ELSE 0 END) as totalAbandonCalls,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= ?) THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'CONNECT' OR Event = 'ABANDON' OR Event = 'EXITWITHKEY') THEN 1 ELSE 0 END)*100),0) as CustomerServiceFactor,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)/SUM(CASE WHEN (Event = 'CONNECT' OR Event = 'ABANDON' OR Event = 'EXITWITHKEY') THEN 1 ELSE 0 END)*100),0) as percentageOfAnsweredCalls
                ", [$threshold->servicelevel])
                ->get();


            foreach ($cdr as $log) {
                $differnce = $log->totalInbounCalls - $log->totalAnswerCalls - $log->totalAbandonCalls;
                $totalAbd = $log->totalAbandonCalls + $differnce;
                $log->totalAbandonCalls = $totalAbd;
            }


            return response()->json($cdr);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
    
    public function hourlyAbandonReport(Request $request)//
    {

        $sum = 0;
        $counter = 0;
        $CURRENTDATE = Carbon::now()->format('Y-m-d');
        if ($CURRENTDATE == $request->date) {
            $cdr = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND Date(time) = '$request->date' "))
                ->whereRaw('CURRENT_TIME()>hour')
                ->whereBetween(DB::raw('hour'), ['08:00:00', '23:30:00'])
                ->groupBy(DB::raw('hour'))
                ->orderBy('hour')
                ->select(
                    DB::raw("
                        Date(time) as Date,
                        hour AS 'to', 
                        AddTime(hour, '00:30:00') AS 'from' ,
                        SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                        SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                        IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END)
                            /SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END)*100),0) percentageOfAbandonCalls,
                        SUM(CASE WHEN (Event = 'ADDMEMBER') THEN 1 ELSE 0 END) 
                            -SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN 1 ELSE 0 END) AS agentLogin")
                )
                ->get();
        } else {
            $cdr = DB::table('queue_log')
                ->rightJoin('hours', DB::raw('SUBSTRING(FROM_UNIXTIME((UNIX_TIMESTAMP(`time`) DIV (30* 60) ) * (30*60)),12)'), '=', DB::raw("hour AND Date(time) = '$request->date' "))
                ->whereBetween(DB::raw('hour'), ['08:00:00', '23:30:00'])
                ->groupBy(DB::raw('hour'))
                ->orderBy('hour')
                ->select(
                    DB::raw("
                    Date(time) as Date,
                    hour AS 'to', 
                    AddTime(hour, '00:30:00') AS 'from' ,
                    SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END) as totalInbounCalls,
                    SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END) as totalAbandonCalls,
                    IFNULL(ROUND(SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END)
                        /SUM(CASE WHEN (Event = 'ENTERQUEUE') THEN 1 ELSE 0 END)*100),0) percentageOfAbandonCalls,
                    SUM(CASE WHEN (Event = 'ADDMEMBER') THEN 1 ELSE 0 END) 
                        -SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN 1 ELSE 0 END) AS agentLogin")

                )
                ->get();
        }
        $CURRENTTIME = Carbon::now()->format('H');

        foreach ($cdr as &$cd) {

            $sum = (int) $cd->agentLogin + $sum;
            if ($sum < 0) {
                $cd->agentLogin = 0;
            }

            if ((int) $CURRENTTIME < (int) substr($cd->to, 0, 2) and $CURRENTDATE == $request->date or $sum < 0) {

                $cd->agentLogin = 0;
            } else {
                $cd->agentLogin = $sum;
            }


            if ($cd->Date == NULL) {
                $cd->Date = $request->date;
            }
        }
        return response()->json($cdr);
    }

    public function serviceLevelReport(Request $request)//
    {
        $threshold = Queue::first('servicelevel');
        if ($request->type == 'agent') {
            $reports = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
                ->whereIn('Event', ['CONNECT', 'ABANDON', 'RINGNOANSWER'])
                ->where('Agent', '!=', 'None')
                ->groupBy('agent')
                ->select(
                    DB::raw("
            agent,
            (SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'ABANDON' AND data3 <= $threshold->servicelevel) THEN 1 ELSE 0 END))
                /(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'RINGNOANSWER') THEN 1 ELSE 0 END))*100 AS SLA
            ")
                )
                ->get();
        } else {
            $reports = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to])
                ->whereIn('Event', ['CONNECT', 'ABANDON'])
                ->groupBy(DB::raw("DATE(time)"))
                ->select(
                    DB::raw("
            Date(time) as date,
            
            (SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'ABANDON' AND data3 <= $threshold->servicelevel) THEN 1 ELSE 0 END))
                /(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                +SUM(CASE WHEN (Event = 'ABANDON') THEN 1 ELSE 0 END))*100 AS SLA
            ")
                )
                ->get();
        }
        foreach ($reports as $report) {
            if ($report->SLA == NUll)
                $report->SLA = '0.00%';
            else
                $report->SLA = round($report->SLA, 2) . "%";
        }

        return response()->json($reports);
    }

    public function queueWiseReport(Request $request)
    {
        $threshold = Queue::first('servicelevel');
        $difference = 0;
        $totalAbd = 0;

        $queue_log = DB::table('queue_log')->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to]);

        if ($request->filled('queue')) {
            $queue_log = $queue_log->where('queuename', $request->queue);
        }

        $queue_log = $queue_log
            ->groupBy('queuename')
            ->selectRaw("
                queuename,
                SUM(CASE WHEN (Event = 'ABANDON' OR Event ='CONNECT' OR Event = 'EXITWITHKEY') THEN 1 ELSE 0 END) as totalInbounCalls,
                SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalAnswerCalls,
                SUM(CASE WHEN (Event = 'ABANDON' OR Event = 'EXITWITHKEY') THEN 1 ELSE 0 END) as totalAbandonCalls,
                IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= ?) THEN 1 ELSE 0 END)
                /SUM(CASE WHEN (Event = 'ABANDON' OR Event ='CONNECT' OR Event = 'EXITWITHKEY') THEN 1 ELSE 0 END)*100,2),0) as CustomerServiceFactor,
                IFNULL(ROUND(SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END)
                /SUM(CASE WHEN (Event = 'ABANDON' OR Event ='CONNECT' OR Event = 'EXITWITHKEY') THEN 1 ELSE 0 END) *100,2),0) as percentageOfAnsweredCalls
            ", [$threshold->servicelevel]);

        $queue_logs = $queue_log->get();

        foreach ($queue_logs as $log) {
            $difference = $log->totalInbounCalls - $log->totalAnswerCalls - $log->totalAbandonCalls;
            $totalAbd = $log->totalAbandonCalls + $difference;
            $log->totalAbandonCalls = $totalAbd;
        }

        return response()->json($queue_logs);
    }

    public function getAgentKPIReport(Request $request)//
    {

        $threshold = Queue::first('servicelevel');
        // $user = DB::table('users')->where('name', $request->agent)->first('auth_username');

        // $auth_username = "PJSIP/{$user->auth_username}";

        // $username = DB::table('users')->where('auth_username', $user->auth_username)->first('name');
        // $agentName = $username->name;
        $records = DB::select("Select 
            Date(time) as date,
            Agent,
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event = 'ADDMEMBER') THEN time_to_sec(time) ELSE 0 END)),1,8) As 'totalLoginTime',
                 SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'REMOVEMEMBER') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event = 'ADDMEMBER') THEN time_to_sec(time) ELSE 0 END)),1,8) As 'totalSiginTime',
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event='UNPAUSE') THEN time_to_sec(time) ELSE 0 END)-SUM(CASE WHEN (Event='PAUSE') THEN time_to_sec(time) ELSE 0 END)),1,8) as totalBreakTime,
            SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END) as totalCalls,
            SUM(CASE WHEN (Event = 'CONNECT' AND data1 <= $threshold->servicelevel) THEN 1 ELSE 0 END) as totalAnswer20Sec,
            SUBSTRING(SEC_TO_TIME(SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)),1,8) as totalTalkTime,
            SUBSTRING(SEC_TO_TIME(IFNULL((SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data1 ELSE 0 END) 
            + SUM(CASE WHEN (Event = 'COMPLETECALLER' OR Event ='COMPLETEAGENT') THEN data2 ELSE 0 END)) /SUM(CASE WHEN (Event = 'CONNECT') THEN 1 ELSE 0 END),0)),1,8) as AHT
            FROM queue_log
            WHERE  DATE(time) BETWEEN '$request->from' AND '$request->to' and
             group by agent");

        foreach ($records as &$agent) {
            # code...
            // dd($agent->Agent);
            // if ($agent->Agent == $auth_username) {
            //     $agent->Agent = $agentName;
            // }
            if ($agent->totalLoginTime < 0) {
                $agent->totalLoginTime = "00:00:00";
            } else {
                $loginTime = new DateTime($agent->totalLoginTime);
                $breakTime = new DateTime($agent->totalBreakTime);
                $totalSiginTime = $breakTime->diff($loginTime);
                $time = $totalSiginTime->format('%h:%i:%s');
            }
            if ($agent->totalBreakTime < 0) {
                # code...
                $agent->totalBreakTime = "00:00:00";
            }
            if ($agent->totalSiginTime < 0) {
                # code...
                $agent->totalSiginTime = "00:00:00";
            } else {
                $agent->totalSiginTime = $time;
            }
        }


        return response()->json($records);
    }

    public function getCLIAbandonCalls(Request $request)
    {
        ini_set('memory_limit', -1);
        set_time_limit(0);

        $request->validate([
            'contactNumber' => 'required'
        ]);

        $array = [];

        $callIDs = DB::table('queue_log')->where('data2', $request->contactNumber)->get('callid');
        foreach ($callIDs as $callID) {
            $cli = DB::table('queue_log')->where('callid', $callID->callid)->whereIn('Event', ['ABANDON', 'EXITWITHKEY']);
            if ($request->has('from') && $request->has('to')) {
                $cli->whereBetween(DB::raw('DATE(time)'), [$request->from, $request->to]);
            }

            if (isset($request->queue) && !empty($request->queue)) {
                $queues = [$request->queue];
            }

            $cli->whereIn('queuename', $queues);

            foreach ($cli->orderBy(DB::raw('DATE(time)'), 'DESC')->get() as $arr) {
                $date = strtotime($arr->time);

                $array[] = [
                    'Date' => date('Y-m-d', $date),
                    'callid' => $arr->callid = $request->contactNumber,
                    'timeEnd' => $arr->timeEnd = Carbon::create($arr->time)->format('g:i A'),
                    'timeStart' => $arr->timeStart = Carbon::create($arr->time)->subSecond($arr->data3)->format('g:i A'),
                    'data3' => $arr->data3
                ];
            }
        }
        $sort = arsort($array);

        return response()->json(array_values($array));
    }

    public function overallCallHandlingMetric(Request $request)
    {
        $request->validate([
            'queuename' => 'required',
            'month' => 'required_without:day',
            'day' => 'required_without:month'
        ]);
        $threshold = Queue::where('name', $request->queuename)->first();
        if (!$threshold) {
            return response()->json(['message' => 'Queue not found.'], 400);
        }
        $serviceLevel = $threshold->servicelevel ?? 0;
        // filtering data by day and month

        $records = DB::table('queue_log')
            ->where('queuename', $request->queuename)
            ->where(function ($query) use ($request) {
                // filtering by month
                if ($request->has('month')) {
                    $query->where('time', 'LIKE', "{$request->month}%");
                }
                // filtering by day
                if ($request->has('day')) {
                    $query->where('time', 'LIKE', "{$request->day}%");
                }
            })
            ->groupBy('queuename')
            ->select(
                DB::raw("
                queuename as queue,
                COUNT(CASE WHEN (Event = 'ABANDON' OR Event = 'CONNECT' OR Event = 'EXITWITHKEY' ) THEN 1 END) as totalInboundCalls,
                COUNT(CASE WHEN (Event = 'CONNECT') THEN 1 END) as totalAnswerCalls,
                COUNT(CASE WHEN (Event = 'ABANDON' OR Event = 'EXITWITHKEY') THEN 1 END) as totalAbandonCalls,
                COUNT(CASE WHEN (Event = 'CONNECT' AND data1 <= $serviceLevel) THEN 1 END) as answeredWithinThreshold,
                COUNT(CASE WHEN (Event = 'CONNECT' AND data1 > $serviceLevel) THEN 1 END) as answeredAfterThreshold,
                COUNT(CASE WHEN ((Event = 'ABANDON' OR Event = 'EXITWITHKEY')  AND data3 <= $serviceLevel) THEN 1 END) as abandonedCallswithinThreshold,
                COUNT(CASE WHEN ((Event = 'ABANDON' OR Event = 'EXITWITHKEY')  AND data3 > $serviceLevel) THEN 1 END) as abandonedCallsAfterThreshold
                ")
            )->get();
        return response()->json($records);
    }

    public function getAgents(Request $request)
    {
        $users = User::query()->join('queue_user', 'users.id', 'queue_user.user_id')->select(['name', 'auth_username'])->whereIn('type', ['Blended', 'Inbound'])->where('queue_user.queue_name', $request->queue)->get();
        return response()->json($users);
    }

}
