<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ServiceRating extends Model
{
    use HasFactory;

    public function getCreatedAtAttribute($value): string
    {
        return Carbon::parse($value)->setTimezone('Asia/Karachi')->toDateTimeString();
    }

    public function getUpdatedAtAttribute($value): string
    {
        return Carbon::parse($value)->setTimezone('Asia/Karachi')->toDateTimeString();
    }
}
