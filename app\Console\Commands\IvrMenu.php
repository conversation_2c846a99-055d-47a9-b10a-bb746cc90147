<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\SystemSetting;
use App\Models\IvrMenuSetting;

use PAGI\Exception\ChannelDownException;
use PA<PERSON>\AsyncAgi\AsyncClientImpl;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Action\RedirectAction;

class IvrMenu extends Command
{
    protected $signature = 'ivrmenu:start';
    protected $description = 'User customizable IVR menu (fork-per-call to avoid blocking other channels)';

    public function __construct()
    {
        parent::__construct();
    }

    protected function getAMIOptions(): array
    {
        return [
            'host'             => SystemSetting::GetSetting('server_address'),
            'scheme'           => 'tcp://',
            'port'             => SystemSetting::GetSetting('manager_port'),
            'username'         => SystemSetting::GetSetting('username'),
            'secret'           => SystemSetting::GetSetting('secret'),
            'connect_timeout'  => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout'     => SystemSetting::GetSetting('read_timeout'),
        ];
    }

    public function handle()
    {
        $this->info('IVR MENU RUNNING');

        // Reap exited children (no zombies) if pcntl is available
        if (function_exists('pcntl_async_signals') && function_exists('pcntl_signal') && function_exists('pcntl_waitpid')) {
            pcntl_async_signals(true);
            pcntl_signal(SIGCHLD, function () {
                // Non-blocking wait for any child
                while (pcntl_waitpid(-1, $status, WNOHANG) > 0) { /* no-op */ }
            });
        }

        $client = new ClientImpl($this->getAMIOptions());

        $client->registerEventListener(function (EventMessage $event) {
            if (!$event instanceof AsyncAGIStartEvent) {
                return;
            }

            // Only handle our AsyncAGI invocation: AGI(agi:async,IvrMenu)
            $env = (string)$event->getKey('env');
            $this->info("ARG : {$env}");
            preg_match('/agi_arg_1:\s*(\S+)/', $env, $matches);
            $scriptArg = $matches[1] ?? null;
            $this->info("ARG_VALUE : " . ($scriptArg ?? 'null'));
            if ($scriptArg !== 'IvrMenu') {
                return;
            }

            // === Minimal change: fork per channel so one call can't block others ===
            $canFork = function_exists('pcntl_fork');
            if (!$canFork) {
                // If we cannot fork, skip heavy IVR to protect other calls
                $this->error('pcntl_fork() not available; skipping IVR to avoid cross-call blocking');
                return;
            }

            $pid = pcntl_fork();
            if ($pid === -1) {
                $this->error('pcntl_fork() failed; skipping IVR for this call');
                return;
            }

            if ($pid === 0) {
                // ----- CHILD PROCESS: handle just this call with its own AMI connection -----
                try {
                    $childClient = new ClientImpl($this->getAMIOptions());
                    $childClient->open();

                    $agiClient = new AsyncClientImpl([
                        'pamiClient'    => $childClient,
                        'asyncAgiEvent' => $event,
                    ]);

                    $channel   = $event->getChannel();
                    $extension = $event->getExten();

                    $this->processIVR($agiClient, $channel, $extension);

                    $childClient->close();
                } catch (\Throwable $e) {
                    $this->error('Child error: ' . $e->getMessage());
                } finally {
                    exit(0); // ensure child exits
                }
            }

            // PARENT returns immediately to keep draining AMI events
            return;

        }, function ($event) {
            return $event instanceof AsyncAGIStartEvent;
        });

        $client->open();

        // Parent loop: just process AMI events; never blocks on DTMF/media
        while (true) {
            $client->process();
        }
    }

    protected function processIVR(AsyncClientImpl $agiClient, string $channel, string $extension)
    {
        try {
            $this->info('IVR MENU Processing');
            $agiClient->answer();

            $vars         = $agiClient->getChannelVariables();
            $uniqueId     = method_exists($vars, 'getUniqueId') ? $vars->getUniqueId() : null;
            $callerNumber = method_exists($vars, 'getCallerId') ? $vars->getCallerId() : null;

            $timingRes = $this->checkTimings();
            $this->info("Timing Res : {$timingRes}");

            if ($timingRes === 0) {
                // Off-working timing
                $this->info('Off-Timing');
                $menuExit = $this->fetchMenu(null, $off_working = 1);

                if (!$menuExit) {
                    $this->info('No off-working menu found, running normal flow.');
                    $agiClient->asyncBreak(); // return to dialplan
                    return;
                }

                $agiClient->streamFile($menuExit->media);
                $this->info("Running : {$menuExit->media}");
                $agiClient->exec('hangup');
                return;
            } elseif ($timingRes === 3) {
                $this->info('No IVR settings found, running normal flow.');
                $agiClient->asyncBreak(); // return to dialplan
                return;
            }

            // Process priority 1 menu first
            $priorityMenu = $this->fetchMenuByPriority(1);
            if (!$priorityMenu) {
                $this->info('No priority menu found, running normal flow.');
                $agiClient->asyncBreak(); // return to dialplan
                return;
            }

            $priorityMenuOptions = $this->fetchMenuOptions($priorityMenu->id);
            $ivrSettings         = IvrMenuSetting::query()->first();

            if ($priorityMenuOptions->isEmpty()) {
                $agiClient->streamFile($priorityMenu->media);
                $this->info("Priority menu has no options. Redirecting to {$ivrSettings->queue} context.");
                $client         = new ClientImpl($this->getAMIOptions());
                $redirectAction = new RedirectAction($channel, $extension, $ivrSettings->queue, 1);
                $client->open();
                $client->send($redirectAction);
                $client->close();
                return;
            }

            $menuId = $priorityMenu->id ?? null; // Start with the root menu

            do {
                $menu = $this->fetchMenu($menuId, null);
                if (!$menu) {
                    $agiClient->streamFile('invalid');
                    break;
                }

                $menuOptions = $this->fetchMenuOptions($menuId);

                $hasOptionNumber = false;
                $hasQueue        = false;
                $queueName       = '';

                if ($menuOptions->isEmpty()) {
                    $this->info('no-option');
                    $agiClient->streamFile($menu->media);
                    break;
                } else {
                    foreach ($menuOptions as $menuOption) {
                        if (!is_null($menuOption->option_number)) {
                            $hasOptionNumber = true;
                        }
                        if (!is_null($menuOption->queue)) {
                            $hasQueue  = true;
                            $queueName = $menuOption->queue;
                        }
                    }

                    if ($hasOptionNumber) {
                        $digit = $agiClient->getData($menu->media, 5000, 1)->getDigits();
                    } elseif ($hasQueue) {
                        $agiClient->streamFile($menu->media);
                        $this->info("Priority menu has no options. Redirecting to {$queueName} context.");
                        $client         = new ClientImpl($this->getAMIOptions());
                        $redirectAction = new RedirectAction($channel, $extension, $queueName, 1);
                        $client->open();
                        $client->send($redirectAction);
                        $client->close();
                        return;
                    } else {
                        $this->info('No option number or queue available.');
                        $digit = '';
                    }
                }

                // Treat only an actual empty string as "no input" so that '0' is valid
                if (isset($digit) && $digit === '') {
                    if ($callerNumber) {
                        DB::table('dtmf_input')->where('phone_number', $callerNumber)->delete();
                    }

                    if ($hasQueue) {
                        $agiClient->streamFile($menu->media);
                        $this->info("No digit. Redirecting to {$queueName} context.");
                        $client         = new ClientImpl($this->getAMIOptions());
                        $redirectAction = new RedirectAction($channel, $extension, $queueName, 1);
                        $client->open();
                        $client->send($redirectAction);
                        $client->close();
                        return;
                    }

                    $this->info('User did not press any digit. Asking user to press a key to continue.');
                    $agiClient->streamFile('ivr/press-digit');
                    continue;
                }

                // Persist DTMF
                if ($callerNumber) {
                    DB::table('dtmf_input')->where('phone_number', $callerNumber)->delete();

                    DB::table('dtmf_input')->insert([
                        'unique_id'    => $uniqueId,
                        'dtmf'         => $digit,
                        'phone_number' => $callerNumber,
                        'created_at'   => now(),
                        'updated_at'   => now(),
                    ]);
                }

                $nextMenuId = $this->processUserInput($menuId, $digit, $agiClient, $channel, $extension);

                if ($nextMenuId === null) {
                    $agiClient->streamFile('invalid');
                } else {
                    $menuId = $nextMenuId;
                }
            } while ($menuId);

            $agiClient->exec('hangup');
        } catch (ChannelDownException $e) {
            $this->error('Channel down: ' . $e->getMessage());
        } catch (\Exception $e) {
            $this->error('Error processing IVR: ' . $e->getMessage());
        }
    }

    protected function fetchMenuByPriority($priority)
    {
        return DB::table('menus')->where('priority', $priority)->first();
    }

    protected function fetchMenu($menuId = null, $options = null)
    {
        $result = DB::table('menus');

        if ($menuId !== null) {
            $result = $result->where('id', $menuId);
        } elseif ($options !== null) {
            $result = $result->where('off_working', $options);
        }

        return $result->first();
    }

    protected function processUserInput($menuId, $input, $agiClient, $channel, $extension)
    {
        // Fetch the option based on menu ID and input
        $option = DB::table('menu_options')
            ->where('menu_id', $menuId)
            ->where('option_number', $input)
            ->first();

        if ($option) {
            // If target_menu_id is null, check the queue and redirect to the context
            if (is_null($option->target_menu_id)) {
                $queueContext = $option->queue;

                if (!empty($queueContext)) {
                    $client = new ClientImpl($this->getAMIOptions());
                    $action = new RedirectAction($channel, $extension, $queueContext, 1);
                    $client->open();
                    $response = $client->send($action);
                    $client->close();
                    return $response->getMessage();
                }
            }

            // Return the target_menu_id if it exists
            return $option->target_menu_id;
        }

        // Return null if no matching option is found
        return null;
    }

    protected function fetchMenuOptions($menuId)
    {
        return DB::table('menu_options')->where('menu_id', $menuId)->get();
    }

    private function checkTimings(): int
    {
        $settings = IvrMenuSetting::query()->first();

        if (!$settings) {
            return 3; // misconfigured
        }

        $now   = Carbon::now();
        $start = Carbon::parse($settings->start);
        $end   = Carbon::parse($settings->end);

        if ($now->between($start, $end)) {
            return 1; // open
        }

        return 0; // closed
    }
}
