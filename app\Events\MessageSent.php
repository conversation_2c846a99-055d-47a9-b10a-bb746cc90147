<?php
namespace App\Events;
use App\Models\Conversation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\Message;
class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $msg;
    public $receiverId;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($msg)
    {
        $this->msg = $msg;
        $this->receiverId = $msg->conversation->user_one == $msg->user_id
            ? $msg->conversation->user_two
            : $msg->conversation->user_one;
    }
    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new channel('message_channel.' . $this->msg->conversation_id);
        // return new PrivateChannel('message_channel.' . $this->msg->conversation_id);
    }
    public function broadcastWith()
    {
        // return ["message" => $this->msg];
        return [
            "message" => [
                "id" => $this->msg->id,
                "message" => $this->msg->message,
                "sender_id" => (int) $this->msg->user_id,           
                "conversation_id" => $this->msg->conversation_id, 
                "created_at" => $this->msg->created_at,
            ],
           "unread_count" => Message::where('conversation_id', $this->msg->conversation_id)
                ->where('user_id', '!=', $this->msg->user_id) // Messages from other user
                ->where('status', '!=', 'read')
                ->count()
        
        ];
    }
}