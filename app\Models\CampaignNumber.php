<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CampaignNumber extends Model
{
    use HasFactory;
    protected $fillable = ['number', 'name', 'city', 'attempts', 'status', 'file', 'campaign_id', 'vr_id'];

    public function campaign(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    public function getNumberAttribute($value): string
    {
        return $value[0] === "0" ? $value : "0{$value}";
    }
    public function getCreatedAtAttribute($value): string
    {
        return Carbon::parse($value)->setTimezone('Asia/Karachi')->toDateTimeString();
    }
}
