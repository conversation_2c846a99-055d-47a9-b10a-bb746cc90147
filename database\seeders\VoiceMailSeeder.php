<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class VoiceMailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('voicemail_settings')->upsert([
            'id' => 1,
            'weeks' => '["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]',
            'except' => '["Sunday"]',
            'start' => '20:00:00',
            'end' => '09:00:00',
            'status' => false,
            'specificDates' => [],
            'specificDateStart' => '',
            'specificDateEnd' => ''
        ], 'id', ['weeks', 'except', 'start', 'end', 'status']);
    }
}
