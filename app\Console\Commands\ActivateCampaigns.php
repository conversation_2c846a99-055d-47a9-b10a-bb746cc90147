<?php

namespace App\Console\Commands;

use App\Models\Campaign;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ActivateCampaigns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'activate:campaigns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to activate scheduled campaigns';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $campaigns = Campaign::query()->get();
        foreach ($campaigns as $campaign) {
            if($campaign->start_time <= Carbon::now() && $campaign->end_time >= Carbon::now()) {
                $campaign->status = true;
                $campaign->save();
            } elseif($campaign->end_time <= Carbon::now() && $campaign->status) {
                $campaign->status = false;
                $campaign->save();
            }
        }
        return 0;
    }
}
