<?php

// Check if supervisor is running
exec('systemctl is-active supervisor', $systemctlOutput, $systemctlCode);
if ($systemctlCode !== 0) {
    echo "Supervisor service is not running. Attempting to start...\n";
    exec('sudo systemctl start supervisor', $startOutput, $startCode);
    if ($startCode !== 0) {
        echo "Failed to start supervisor service.\n";
        exit(1);
    }
    echo "Supervisor service started successfully.\n";
    sleep(2); // Give it a moment to start up
}

exec('/usr/bin/supervisorctl status 2>&1', $output, $exitCode);

if ($exitCode !== 0) {
    echo "Warning: supervisorctl returned exit code $exitCode (likely due to a FATAL process)\n";
}

echo "Stopped Supervisor Processes:\n";
$restarted = false;

foreach ($output as $line) {
    if (stripos($line, 'STOPPED') !== false || stripos($line, 'FATAL') !== false) {
        echo $line . PHP_EOL;
    //    $stopped = true;
    }

      if (preg_match('/^(\S+):(\S+)\s+(STOPPED|FATAL)/', $line, $matches)) {
          $group = $matches[1];
           $process = $matches[2];
           $fullName = "$group:$process";

           echo "Restarting $fullName...\n";
           exec("/usr/bin/supervisorctl restart $fullName 2>&1", $restartOutput, $restartCode);
           echo implode("\n", $restartOutput) . "\n";

           if ($restartCode === 0) {
              echo "Successfully restarted $fullName.\n";
           } else {
              echo "Failed to restart $fullName. Exit code: $restartCode\n";
           }

           $restarted = true;
       }

}

if (!$restarted) {
    echo "No processes needed restarting.\n";
}

?>