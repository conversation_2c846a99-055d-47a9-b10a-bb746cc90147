<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemSetting extends Model
{
    use HasFactory;
    protected $fillable = ['server_address', 'wss_port', 'manager_port', 'manager_secret', 'username', 'connection_timeout', 'read_timeout', 'theme', 'logo', 'path', 'secret', 'type'];
    public $timestamps = false;

    public static function GetSettings(): array
    {
        return SystemSetting::query()->pluck('value', 'key')->toArray() ?? [];
    }

    public static function GetSetting($key) {
        return SystemSetting::query()->where('key', $key)->first()->value ?? '';
    }
}
