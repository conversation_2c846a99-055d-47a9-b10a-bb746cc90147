<?php

namespace App\Imports;

use App\Models\Campaign;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class CustomNumberImport implements ToModel, WithHeadingRow
{
    private string $file;
    private Campaign $campaign;

    public function __construct($file, Campaign $campaign)
    {
        $this->file = $file;
        $this->campaign = $campaign;
    }

    public function model(array $row)
    {
        /** @var Campaign $this->campaign  */
        return $this->campaign->custom_numbers()->create([
            'customer_phone' => $row['customer_phone'],
            'retailer_name' => $row['retailer_name'],
            'shop_name' => $row['shop_name'],
            'agent_id' => $row['agent_id'],
            'inactive_days' => $row['inactive_days'],
            'target' => $row['target'],
            'gmv' => $row['gmv'],
            'number_of_unique_sku_sold' => $row['no_of_unique_sku_sold'],
            'attempts' => $row['attempts'],
            'file' => $this->file
        ]);
    }
}
