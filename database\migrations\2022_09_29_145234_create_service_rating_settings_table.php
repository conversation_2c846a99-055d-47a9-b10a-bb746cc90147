<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// class CreateServiceRatingSettingsTable extends Migration
return new class extends Migration

{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('service_rating_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('service_rating_file_id')->nullable();
            $table->boolean('status');
            $table->unsignedBigInteger('best');
            $table->unsignedBigInteger('worst');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('service_rating_settings');
    }
};
